"""
PyTorch GPU Configuration Utilities

This module provides utilities for configuring GPU usage with PyTorch.
"""

import logging
import torch
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_gpu_info() -> Dict[str, Any]:
    """
    Get information about available GPUs.
    
    Returns:
        Dictionary with GPU information
    """
    gpu_info = {
        'gpu_available': torch.cuda.is_available(),
        'gpu_count': torch.cuda.device_count(),
        'pytorch_version': torch.__version__,
        'gpu_devices': [],
        'current_device': None,
    }
    
    if gpu_info['gpu_available']:
        gpu_info['current_device'] = torch.cuda.current_device()
        for i in range(gpu_info['gpu_count']):
            device_info = {
                'index': i,
                'name': torch.cuda.get_device_name(i),
                'capability': torch.cuda.get_device_capability(i),
                'total_memory': torch.cuda.get_device_properties(i).total_memory,
            }
            gpu_info['gpu_devices'].append(device_info)
    
    return gpu_info

def select_device(use_gpu: bool = True, device_index: Optional[int] = None) -> torch.device:
    """
    Select the appropriate device (GPU or CPU) for PyTorch.
    
    Args:
        use_gpu: Whether to use GPU if available
        device_index: Specific GPU device index to use (if None, use default)
        
    Returns:
        PyTorch device
    """
    if use_gpu and torch.cuda.is_available():
        if device_index is not None and device_index < torch.cuda.device_count():
            device = torch.device(f'cuda:{device_index}')
            logger.info(f"Using GPU device {device_index}: {torch.cuda.get_device_name(device_index)}")
        else:
            device = torch.device('cuda')
            logger.info(f"Using default GPU device: {torch.cuda.get_device_name()}")
    else:
        device = torch.device('cpu')
        if not torch.cuda.is_available() and use_gpu:
            logger.warning("No GPU available, using CPU")
        elif not use_gpu:
            logger.info("GPU usage disabled, using CPU")
    
    return device

def configure_gpu_memory() -> None:
    """
    Configure GPU memory settings for optimal performance.
    
    This is less critical for PyTorch than TensorFlow, but can still be useful
    for some configurations.
    """
    if torch.cuda.is_available():
        # Empty the cache to free up memory
        torch.cuda.empty_cache()
        
        # Set to deterministic mode for reproducibility if needed
        # Note: This can impact performance
        # torch.backends.cudnn.deterministic = True
        # torch.backends.cudnn.benchmark = False
        
        # For better performance, enable benchmark mode
        # This is good when input sizes don't vary much
        torch.backends.cudnn.benchmark = True
        
        logger.info("Configured GPU memory settings for PyTorch")
    else:
        logger.warning("No GPU available, skipping GPU memory configuration")

def get_available_devices() -> List[torch.device]:
    """
    Get a list of all available devices (CPU and GPUs).
    
    Returns:
        List of available PyTorch devices
    """
    devices = [torch.device('cpu')]
    
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            devices.append(torch.device(f'cuda:{i}'))
    
    return devices

def print_gpu_info() -> None:
    """Print detailed information about available GPUs."""
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        logger.info(f"PyTorch version: {torch.__version__}")
        logger.info(f"CUDA available: {torch.cuda.is_available()}")
        logger.info(f"CUDA version: {torch.version.cuda}")
        logger.info(f"Number of GPUs: {gpu_count}")
        
        for i in range(gpu_count):
            logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            props = torch.cuda.get_device_properties(i)
            logger.info(f"  Compute capability: {props.major}.{props.minor}")
            logger.info(f"  Total memory: {props.total_memory / 1024**3:.2f} GB")
            logger.info(f"  Multi-processor count: {props.multi_processor_count}")
    else:
        logger.info("No GPU available")
        logger.info(f"PyTorch version: {torch.__version__}")

if __name__ == "__main__":
    # Test GPU configuration
    print_gpu_info()
    device = select_device()
    print(f"Selected device: {device}")
