#!/bin/bash
# Script to train TFT models on all timeframes with and without ARIMA

# Create necessary directories
mkdir -p models
mkdir -p metrics
mkdir -p plots
mkdir -p comparison_results

# Set timeframes
TIMEFRAMES=("M5" "M15" "M30" "H1" "H4")

# Set common parameters
EPOCHS=5
BATCH_SIZE=32
SEQUENCE_LENGTH=60
HIDDEN_DIM=64
NUM_HEADS=4
NUM_LAYERS=2
DROPOUT_RATE=0.1
LEARNING_RATE=0.001
FEATURE_COLUMNS="open,high,low,close,real_volume"
TARGET_COLUMN="close"
USE_GPU=true

echo "Starting TFT model training..."

# Train TFT models for all timeframes
for TIMEFRAME in "${TIMEFRAMES[@]}"
do
    echo "Training TFT model for $TIMEFRAME timeframe..."
    python train_tft_pytorch.py \
        --timeframe $TIMEFRAME \
        --feature-columns $FEATURE_COLUMNS \
        --target-column $TARGET_COLUMN \
        --sequence-length $SEQUENCE_LENGTH \
        --hidden-dim $HIDDEN_DIM \
        --num-heads $NUM_HEADS \
        --num-layers $NUM_LAYERS \
        --dropout-rate $DROPOUT_RATE \
        --learning-rate $LEARNING_RATE \
        --epochs $EPOCHS \
        --batch-size $BATCH_SIZE \
        --use-gpu $USE_GPU

    echo "TFT model training for $TIMEFRAME completed."
done

# Train TFT+ARIMA models for all timeframes
for TIMEFRAME in "${TIMEFRAMES[@]}"
do
    echo "Training TFT+ARIMA model for $TIMEFRAME timeframe..."
    python train_tft_pytorch.py \
        --timeframe $TIMEFRAME \
        --feature-columns $FEATURE_COLUMNS \
        --target-column $TARGET_COLUMN \
        --sequence-length $SEQUENCE_LENGTH \
        --hidden-dim $HIDDEN_DIM \
        --num-heads $NUM_HEADS \
        --num-layers $NUM_LAYERS \
        --dropout-rate $DROPOUT_RATE \
        --learning-rate $LEARNING_RATE \
        --epochs $EPOCHS \
        --batch-size $BATCH_SIZE \
        --use-gpu $USE_GPU \
        --with-arima

    echo "TFT+ARIMA model training for $TIMEFRAME completed."
done

# Compare all models
echo "Comparing all models..."
python compare_all_models.py --output-dir comparison_results

echo "All model training and comparison completed."
echo "Results are available in the comparison_results directory."
