"""
Main module for the trading bot.
Handles initialization and execution of trading bots.
"""
import sys
import signal
import logging
import time
import threading
import psutil
from typing import Dict, Optional, Tuple
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/main.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import unified configuration manager
from config.unified_config import config_manager, get_config, TradingConfig, ModelConfig, UnifiedConfigManager

# Get main configuration
trading_config = get_config()

# Initialize utility managers first
from utils.enhanced_error_handler import EnhancedError<PERSON><PERSON><PERSON>
error_handler = EnhancedErrorHandler()

from utils.enhanced_memory_manager import enhanced_memory_manager

# Check if max_memory_usage is a percentage or absolute value
max_memory = trading_config.max_memory_usage

# If max_memory is less than 100, treat it as a percentage
if max_memory < 100:
    # Set unified memory thresholds across all components as percentages
    memory_thresholds = {
        "WARNING": max_memory - 15.0,  # 75% if max is 90%
        "HIGH": max_memory - 5.0,      # 85% if max is 90%
        "CRITICAL": max_memory         # 90% if max is 90%
    }

    # Initialize enhanced memory manager with unified thresholds
    enhanced_memory_manager.base_thresholds = memory_thresholds
    enhanced_memory_manager.thresholds = enhanced_memory_manager._calculate_adaptive_thresholds()

    # Log memory thresholds
    logger.info(f"Unified memory thresholds set as percentages: WARNING={memory_thresholds['WARNING']}%, "
               f"HIGH={memory_thresholds['HIGH']}%, CRITICAL={memory_thresholds['CRITICAL']}%")
else:
    # It's an absolute value in MB
    # Convert to percentage for the memory manager
    total_memory_mb = psutil.virtual_memory().total / (1024 * 1024)
    memory_percent = (max_memory / total_memory_mb) * 100

    # Cap at 95% to prevent system instability
    memory_percent = min(95.0, memory_percent)

    # Set unified memory thresholds
    memory_thresholds = {
        "WARNING": memory_percent - 15.0,
        "HIGH": memory_percent - 5.0,
        "CRITICAL": memory_percent
    }

    # Initialize enhanced memory manager with unified thresholds
    enhanced_memory_manager.base_thresholds = memory_thresholds
    enhanced_memory_manager.thresholds = enhanced_memory_manager._calculate_adaptive_thresholds()
    enhanced_memory_manager.max_memory_mb = max_memory

    # Log memory thresholds
    logger.info(f"Memory limit set to {max_memory:.2f} MB (approx. {memory_percent:.2f}% of total memory)")
    logger.info(f"Unified memory thresholds set: WARNING={memory_thresholds['WARNING']}%, "
               f"HIGH={memory_thresholds['HIGH']}%, CRITICAL={memory_thresholds['CRITICAL']}%")

enhanced_memory_manager.monitor_interval = 60.0

# Use the global instance as memory_manager for backward compatibility
memory_manager = enhanced_memory_manager

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32, # Consider making this configurable
    thread_name_prefix="Main"
)

# ModelManager needs to be instantiated per context (terminal/timeframe)
# It will be created within TradingBotManager or TradingBot

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
mt5_manager = MT5ConnectionManager(config_manager)

# Register standard circuit breakers
from utils.enhanced_circuit_breaker import register_standard_circuit_breakers
standard_circuit_breakers = register_standard_circuit_breakers(memory_manager=enhanced_memory_manager)
logger.info(f"Registered {len(standard_circuit_breakers)} standard circuit breakers")

# Function to register all caches with memory manager
def register_caches_with_memory_manager(memory_manager):
    """Register all caches with the memory manager for consistent cleanup."""
    registered_components = []

    # Try to register intelligent cache
    try:
        from utils.intelligent_cache import intelligent_cache

        # Check if the component is already registered to avoid duplicate registration
        if "intelligent_cache" not in memory_manager.components:
            memory_manager.register_component(
                "intelligent_cache",
                cleanup_handlers={
                    "light": lambda component_name: intelligent_cache.cleanup() or 0,
                    "moderate": lambda component_name: (intelligent_cache.clear(tier="memory"), 0)[1],
                    "aggressive": lambda component_name: (intelligent_cache.clear(), 0)[1]
                }
            )
            registered_components.append("intelligent_cache")
        else:
            logger.debug("Intelligent cache already registered with memory manager")
    except (ImportError, AttributeError):
        logger.debug("Intelligent cache not available for registration")

    # Register data processor cache when it's created
    # This will be done when the data processor is instantiated

    logger.info(f"Registered {len(registered_components)} cache components with memory manager")
    return registered_components

# Register caches with memory manager
registered_caches = register_caches_with_memory_manager(enhanced_memory_manager)

# Now import the rest of the modules
from monitoring.progress import ProgressVisualizer
from trading.bot import TradingBot
from monitoring.performance import PerformanceMonitor
from config.credentials import MT5_TERMINALS

class TradingBotManager:
    """
    Manager class for handling multiple trading bots.
    Coordinates initialization, updates, and shutdown of bots.
    """

    def __init__(self, visualizer: Optional[ProgressVisualizer] = None, config_manager: UnifiedConfigManager = None, error_handler: EnhancedErrorHandler = None, mt5_manager: MT5ConnectionManager = None, thread_manager: ThreadManager = None, memory_manager = None):
        """
        Initialize the trading bot manager.

        Args:
            visualizer: Optional progress visualizer
            config_manager: Configuration manager
            error_handler: Error handler
            mt5_manager: MT5 connection manager
            thread_manager: Thread manager
        """
        self.bots: Dict[int, TradingBot] = {}
        self.monitors: Dict[int, PerformanceMonitor] = {}
        self.running = False
        self.visualizer = visualizer or ProgressVisualizer()
        self._lock = threading.RLock()
        self.config_manager = config_manager or UnifiedConfigManager()
        self.error_handler = error_handler or EnhancedErrorHandler()
        self.mt5_manager = mt5_manager or MT5ConnectionManager(self.config_manager)
        self.thread_manager = thread_manager or ThreadManager(
            max_workers=32,
            thread_name_prefix="Main"
        )
        self.memory_manager = memory_manager or enhanced_memory_manager

    def _create_bot_config(self, terminal_id_str: str) -> Tuple[TradingConfig, Dict[str, ModelConfig]]:
        """
        Create configuration objects needed for a trading bot.

        Args:
            terminal_id_str: Terminal ID as string (e.g., "1")

        Returns:
            Tuple containing the main trading config and all model configs
        """
        try:
            # Get main trading configuration
            # The config_manager is a singleton, already initialized
            main_config = self.config_manager.get_config()

            # Get all model configurations
            all_model_configs = self.config_manager.get_all_model_configs()

            # Get specific terminal configuration
            terminal_config = self.config_manager.get_mt5_config().terminals.get(terminal_id_str)
            if not terminal_config:
                 raise ValueError(f"Terminal configuration for ID '{terminal_id_str}' not found.")

            # Note: We return the main config and all model configs.
            # The bot or its internal managers will use the correct model config based on model_name.
            return main_config, all_model_configs

        except Exception as e:
            self.error_handler.handle_error(e, context={"function": "_create_bot_config", "terminal_id": terminal_id_str})
            raise

    def initialize_bot(self, terminal_id_str: str) -> bool:
        """
        Initialize a trading bot for a specific terminal.

        Args:
            terminal_id_str: Terminal ID as string (e.g., "1")

        Returns:
            bool: True if initialization successful, False otherwise
        """
        with self._lock:
            try:
                # Create bot configuration
                main_config, _ = self._create_bot_config(terminal_id_str)

                # Check MT5 connection first
                logger.info(f"Checking connection for terminal {terminal_id_str}")
                connection = self.mt5_manager.get_connection(terminal_id_str)
                if not connection or not connection.is_connected:
                    logger.error(f"Failed to connect to MT5 for terminal {terminal_id_str}")
                    return False

                # Create bot instance
                logger.info(f"Creating trading bot for terminal {terminal_id_str}")
                self.bots[terminal_id_str] = TradingBot(
                    config_manager=self.config_manager,
                    terminal_id=terminal_id_str,
                    error_handler=self.error_handler,
                    thread_manager=self.thread_manager,
                    mt5_manager=self.mt5_manager,
                    visualizer=self.visualizer,
                    memory_manager=self.memory_manager
                )

                # Create performance monitor
                logger.info(f"Creating performance monitor for terminal {terminal_id_str}")
                self.monitors[terminal_id_str] = PerformanceMonitor(
                    terminal_id=terminal_id_str
                )

                logger.info(f"Initialized trading bot for terminal {terminal_id_str}")
                return True

            except Exception as e:
                error_handler.handle_error(e, context={"function": "initialize_bot", "terminal_id": terminal_id_str})
                logger.error(f"Error initializing bot for terminal {terminal_id_str}: {str(e)}")
                return False

    def start_all_bots(self) -> bool:
        """
        Start all trading bots.

        Returns:
            bool: True if all bots started successfully, False otherwise
        """
        with self._lock:
            try:
                # Get available terminals
                terminal_ids = list(MT5_TERMINALS.keys())

                logger.info(f"Starting bots for terminals: {terminal_ids}")

                for terminal_id in terminal_ids:
                    # Ensure terminal_id is a string for consistency
                    term_id_str = str(terminal_id).replace("terminal", "")

                    if term_id_str not in self.bots:
                        success = self.initialize_bot(term_id_str)
                        if not success:
                            logger.error(f"Failed to initialize bot for terminal {term_id_str}")
                            continue

                    # Start the bot
                    logger.info(f"Starting bot for terminal {term_id_str}")
                    start_success = self.bots[term_id_str].start()
                    if not start_success:
                        logger.error(f"Failed to start bot for terminal {term_id_str}")
                        continue

                    # Start the monitor
                    logger.info(f"Starting monitor for terminal {term_id_str}")
                    self.monitors[term_id_str].start()

                self.running = True
                logger.info("Started all trading bots")

                return True

            except Exception as e:
                error_handler.handle_error(e, context={"function": "start_all_bots"})
                logger.error(f"Error starting all bots: {str(e)}")
                return False

    def stop_all_bots(self) -> bool:
        """
        Stop all trading bots.

        Returns:
            bool: True if all bots stopped successfully, False otherwise
        """
        with self._lock:
            try:
                logger.info("Stopping all trading bots...")

                for terminal_id, bot in self.bots.items():
                    logger.info(f"Stopping bot for terminal {terminal_id}")
                    bot.stop()

                    if terminal_id in self.monitors:
                        logger.info(f"Stopping monitor for terminal {terminal_id}")
                        self.monitors[terminal_id].stop()

                self.running = False
                logger.info("Stopped all trading bots")

                # Disconnect from MT5
                logger.info("Disconnecting from MT5 terminals")
                self.mt5_manager.shutdown_all()

                return True

            except Exception as e:
                error_handler.handle_error(e, context={"function": "stop_all_bots"})
                logger.error(f"Error stopping all bots: {str(e)}")
                return False

    def update(self) -> None:
        """
        Update all trading bots.
        """
        try:
            if not self.running:
                return

            for terminal_id, bot in self.bots.items():
                try:
                    bot.update()
                except Exception as e:
                    error_handler.handle_error(
                        e,
                        context={
                            "function": "update",
                            "terminal_id": terminal_id
                        }
                    )

            # Update visualizer
            if self.visualizer:
                try:
                    # Collect data from all bots for visualization
                    bot_data = {}
                    for terminal_id, bot in self.bots.items():
                        if hasattr(bot, 'performance_metrics'):
                            bot_data[terminal_id] = bot.performance_metrics

                    # Update visualizer
                    self.visualizer.update(bot_data)
                except Exception as e:
                    error_handler.handle_error(e, context={"function": "update_visualizer"})

        except Exception as e:
            error_handler.handle_error(e, context={"function": "update"})

    def shutdown(self) -> None:
        """
        Clean shutdown of the manager and all bots.
        """
        try:
            logger.info("Shutting down trading bot manager...")

            # Stop all bots
            self.stop_all_bots()

            # Clear resources
            self.bots.clear()
            self.monitors.clear()

            # Perform global cleanup
            enhanced_memory_manager.stop_monitoring()
            enhanced_memory_manager.cleanup("aggressive")
            self.thread_manager.shutdown(wait=True)

            logger.info("Trading bot manager shutdown complete")

        except Exception as e:
            error_handler.handle_error(e, context={"function": "shutdown"})
            logger.error(f"Error during shutdown: {str(e)}")

def setup_signal_handlers(manager: TradingBotManager) -> None:
    """
    Set up signal handlers for graceful shutdown.

    Args:
        manager: Trading bot manager
    """
    def sigint_handler(sig, frame):
        # Parameters are required by signal.signal but not used
        logger.info("Received SIGINT, shutting down...")
        manager.shutdown()
        sys.exit(0)

    def sigterm_handler(sig, frame):
        # Parameters are required by signal.signal but not used
        logger.info("Received SIGTERM, shutting down...")
        manager.shutdown()
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, sigint_handler)
    signal.signal(signal.SIGTERM, sigterm_handler)

def main() -> int:
    """Main function to initialize and run the trading bot manager."""
    logger.info("Starting Trading Bot System...")

    # Initialize configuration (already done globally)
    config = config_manager

    # Initialize managers (already done globally)

    # Initialize the main bot manager
    visualizer = ProgressVisualizer() # Optional
    manager = TradingBotManager(visualizer=visualizer,
                              config_manager=config, # Pass config
                              error_handler=error_handler,
                              mt5_manager=mt5_manager,
                              thread_manager=thread_manager,
                              memory_manager=enhanced_memory_manager)

    # Setup signal handlers for graceful shutdown
    setup_signal_handlers(manager)

    # Start all trading bots
    if not manager.start_all_bots():
        logger.error("Failed to start trading bots")
        return 1

    # Main loop
    try:
        logger.info("Entering main loop...")
        while True:
            # Update trading bots
            manager.update()

            # Sleep to prevent CPU overuse
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")

    # Clean shutdown
    manager.shutdown()
    logger.info("Trading bot application exited cleanly")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)