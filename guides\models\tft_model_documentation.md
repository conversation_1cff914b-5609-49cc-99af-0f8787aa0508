# Temporal Fusion Transformer (TFT) Model Documentation

## Table of Contents
1. [Model Architecture](#1-model-architecture)
2. [Configuration Requirements](#2-configuration-requirements)
3. [Model Setup and Training](#3-model-setup-and-training)
4. [Testing and Evaluation](#4-testing-and-evaluation)
5. [Device Management](#5-device-management)
6. [Common Issues and Solutions](#6-common-issues-and-solutions)
7. [Best Practices](#7-best-practices)
8. [Example Usage](#8-example-usage)
9. [Testing History](#9-testing-history)
10. [Environment Configuration](#10-environment-configuration)
11. [Current Working Configuration](#11-current-working-configuration)
12. [Environment-Specific Configurations](#12-environment-specific-configurations)
13. [Other Models Documentation](#13-other-models-documentation)

## 1. Model Architecture

### 1.1 Core Components
- **Base Model**: Inherits from `BaseModel` class
- **TFT Model**: Main model class implementing Temporal Fusion Transformer
- **TransformerBlock**: Custom transformer block implementation
- **Data Preprocessing**: Feature scaling and normalization

### 1.2 Key Features
- Multi-head attention mechanism
- Time-dependent processing
- Feature importance analysis
- Mixed precision training support
- GPU acceleration
- Automatic mixed precision (AMP) support

## 2. Configuration Requirements

### 2.1 Required Parameters
```python
TFT_CONFIG = {
    'batch_size': 32,           # Batch size for training
    'learning_rate': 1e-4,      # Learning rate
    'weight_decay': 1e-5,       # Weight decay
    'gradient_clip_val': 0.1,   # Gradient clipping
    'num_heads': 4,             # Number of attention heads
    'd_model': 64,              # Model dimension
    'd_ff': 128,                # Feed-forward dimension
    'dropout_rate': 0.2,        # Dropout rate
    'sequence_length': 60       # Input sequence length
}
```

### 2.2 Data Requirements
- Time series data with timestamp index
- Required columns:
  - `time_idx`: Integer time index
  - `close`: Target variable
  - Feature columns for prediction
- Data should be preprocessed and normalized

## 3. Model Setup and Training

### 3.1 Initialization
```python
model = TFTModel(config)
model.build(training_data)
```

### 3.2 Training Process
```python
history = model.train(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    epochs=100,
    batch_size=32
)
```

### 3.3 Key Training Features
- Automatic mixed precision training
- Gradient scaling
- Learning rate scheduling
- Early stopping
- Model checkpointing
- TensorBoard logging
- GPU acceleration

## 4. Testing and Evaluation

### 4.1 Test Configuration
```python
TFT_TEST_CONFIG = {
    'batch_size': 16,           # Reduced for testing
    'learning_rate': 1e-4,
    'num_heads': 2,             # Reduced for testing
    'd_model': 32,              # Reduced for testing
    'max_epochs': 2,            # Minimal epochs
    'sequence_length': 60
}
```

### 4.2 Test Cases
1. Model Initialization
2. Data Loading
3. Sequence Preparation
4. Model Training
5. Prediction
6. Device Management

### 4.3 Evaluation Metrics
- Mean Absolute Error (MAE)
- Mean Squared Error (MSE)
- Root Mean Squared Error (RMSE)
- R-squared Score (R2)
- Mean Absolute Percentage Error (MAPE)
- Mean Directional Accuracy (MDA)

## 5. Device Management

### 5.1 GPU Support
```python
# Automatic GPU detection
gpu_available = tf.config.list_physical_devices('GPU')
if gpu_available:
    tf.config.experimental.set_memory_growth(gpu_available[0], True)
```

### 5.2 Performance Optimization
- Memory pinning for faster data transfer
- Mixed precision training
- Gradient scaling
- Batch size optimization

## 6. Common Issues and Solutions

### 6.1 Memory Issues
- Use appropriate batch size
- Enable gradient scaling
- Monitor GPU memory usage
- Use mixed precision training

### 6.2 Training Stability
- Use learning rate scheduling
- Implement gradient clipping
- Monitor validation loss
- Use early stopping

### 6.3 Performance Issues
- Optimize batch size
- Enable mixed precision
- Use GPU acceleration
- Monitor system resources

## 7. Best Practices

### 7.1 Data Preparation
- Normalize time series data
- Handle missing values
- Use proper sequence length
- Implement proper train/val split

### 7.2 Model Configuration
- Use appropriate hyperparameters
- Monitor training progress
- Save model checkpoints
- Log training metrics

### 7.3 Performance Optimization
- Use GPU when available
- Enable mixed precision training
- Optimize batch size
- Monitor system resources

## 8. Example Usage

```python
# Initialize model
model = TFTModel(TFT_CONFIG)

# Prepare data
X_train, y_train, X_val, y_val = prepare_sequences(data, sequence_length)

# Build and train model
model.build(X_train)
history = model.train(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    epochs=100,
    batch_size=32
)

# Make predictions
predictions = model.predict(X_test)

# Get attention weights
attention_weights = model.get_attention_weights(X_test)
```

## 9. Testing History

### 9.1 Initial Testing (3 hours ago)
- **Issue**: `AttributeError: can't set attribute 'device'`
- **Solution**: Removed direct device assignment and used PyTorch Lightning's built-in device management
- **Changes**:
  - Removed `self.device` assignment
  - Added CUDA initialization
  - Enhanced `_move_to_device` method

### 9.2 Second Testing Round
- **Issue**: `TypeError: Can't instantiate abstract class TFTModel with abstract method predict`
- **Solution**: Implemented `predict` method in TFTModel class
- **Changes**:
  - Added prediction method
  - Configured trainer for prediction
  - Added proper error handling

### 9.3 Third Testing Round
- **Issue**: `TypeError: TFTModel.build() missing 1 required positional argument: 'training_data'`
- **Solution**: Updated test to pass training data to build method
- **Changes**:
  - Modified test_model_training method
  - Added proper data preparation
  - Updated assertions

### 9.4 Fourth Testing Round
- **Issue**: Device mismatch during tensor operations
- **Solution**: Implemented proper device handling in attention mask creation
- **Changes**:
  - Added device initialization in TFTModel
  - Updated mask creation to use correct device
  - Added monkey patch for get_attention_mask

### 9.5 Final Testing Round
- **Issue**: Various warnings about logging and DataLoader configuration
- **Solution**: Updated model and test configurations
- **Changes**:
  - Added explicit batch size logging
  - Configured persistent workers
  - Adjusted logging interval
  - Updated optimizer configuration

### 9.6 Current Status
- All tests passing
- Device handling working correctly
- Proper logging and monitoring in place
- Performance optimizations implemented

### 9.7 Remaining Warnings
1. TensorFlow's `np.bool8` deprecation (external library)
2. PyTorch Lightning attribute warnings (handled with ignore list)
3. GPU not used warning (expected in test environment)
4. Checkpoint directory exists warning (expected behavior)
5. Learning rate scheduler verbose parameter warning (fixed)

### 9.8 Next Steps
1. Test remaining models (LSTM, ARIMA)
2. Implement integration tests
3. Add performance benchmarks
4. Create end-to-end testing pipeline

## 10. Environment Configuration

### 10.1 Required Dependencies
```python
# Core dependencies
torch>=1.10.0
pytorch-lightning>=1.5.0
numpy>=1.19.0
pandas>=1.3.0
scikit-learn>=0.24.0

# Optional dependencies for GPU support
cuda-toolkit>=11.0  # For CUDA support
nvidia-cudnn>=8.0   # For deep learning acceleration

# Testing dependencies
pytest>=6.0.0
pytest-cov>=2.12.0
pytest-xdist>=2.4.0  # For parallel testing
```

### 10.2 Environment Setup
```bash
# Create and activate virtual environment
python -m venv tft_env
source tft_env/bin/activate  # Linux/Mac
tft_env\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Install CUDA toolkit if using GPU
# Windows: Download and install from NVIDIA website
# Linux: sudo apt-get install nvidia-cuda-toolkit
```

### 10.3 Testing Environment Configuration
```python
# pytest.ini configuration
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --cov=models --cov-report=term-missing
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning:pytorch_lightning.*
    ignore::UserWarning:torch.*
```

### 10.4 GPU Configuration
```python
# Check CUDA availability
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA version: {torch.version.cuda}")
print(f"Number of GPUs: {torch.cuda.device_count()}")

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Memory management
torch.cuda.empty_cache()  # Clear GPU memory
torch.backends.cudnn.benchmark = True  # Enable cuDNN auto-tuner
```

### 10.5 Model Configuration Best Practices
```python
# Recommended configuration for different scenarios
TFT_CONFIGS = {
    'small_dataset': {
        'batch_size': 32,
        'learning_rate': 1e-4,
        'gradient_clip_val': 0.1,
        'num_heads': 4,
        'd_model': 64,
        'd_ff': 128,
        'dropout_rate': 0.1,
        'gru_units': 32,
        'dense_units': 16
    },
    'medium_dataset': {
        'batch_size': 64,
        'learning_rate': 5e-5,
        'gradient_clip_val': 0.2,
        'num_heads': 8,
        'd_model': 128,
        'd_ff': 256,
        'dropout_rate': 0.2,
        'gru_units': 64,
        'dense_units': 32
    },
    'large_dataset': {
        'batch_size': 128,
        'learning_rate': 1e-5,
        'gradient_clip_val': 0.3,
        'num_heads': 16,
        'd_model': 256,
        'd_ff': 512,
        'dropout_rate': 0.3,
        'gru_units': 128,
        'dense_units': 64
    }
}
```

### 10.6 Testing Configuration
```python
# Test configuration for different scenarios
TEST_CONFIGS = {
    'unit_tests': {
        'batch_size': 16,
        'max_epochs': 2,
        'sequence_length': 10,
        'use_cuda': False,
        'num_workers': 0
    },
    'integration_tests': {
        'batch_size': 32,
        'max_epochs': 5,
        'sequence_length': 20,
        'use_cuda': True,
        'num_workers': 4
    },
    'performance_tests': {
        'batch_size': 64,
        'max_epochs': 10,
        'sequence_length': 30,
        'use_cuda': True,
        'num_workers': 8
    }
}
```

### 10.7 Logging Configuration
```python
# Logging setup
import logging
import sys

logging_config = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'stream': sys.stdout
        },
        'file': {
            'class': 'logging.FileHandler',
            'level': 'DEBUG',
            'formatter': 'standard',
            'filename': 'tft_model.log'
        }
    },
    'loggers': {
        '': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True
        }
    }
}
```

### 10.8 Performance Optimization Settings
```python
# Performance optimization configuration
PERFORMANCE_CONFIG = {
    'mixed_precision': True,
    'gradient_scaling': True,
    'memory_pinning': True,
    'persistent_workers': True,
    'prefetch_factor': 2,
    'num_workers': 4,
    'pin_memory': True,
    'non_blocking': True
}

# Apply optimizations
if PERFORMANCE_CONFIG['mixed_precision']:
    torch.set_float32_matmul_precision('medium')

if PERFORMANCE_CONFIG['gradient_scaling']:
    torch.cuda.amp.GradScaler()
```

### 10.9 Common Environment Issues and Solutions

#### 10.9.1 CUDA Version Mismatch
```bash
# Check CUDA version compatibility
nvcc --version
python -c "import torch; print(torch.version.cuda)"

# Solution: Install matching versions
pip install torch==1.10.0+cu113 -f https://download.pytorch.org/whl/cu113/torch_stable.html
```

#### 10.9.2 Memory Management
```python
# Monitor GPU memory
def print_gpu_memory():
    if torch.cuda.is_available():
        print(f"GPU Memory Allocated: {torch.cuda.memory_allocated() / 1024**2:.2f} MB")
        print(f"GPU Memory Cached: {torch.cuda.memory_reserved() / 1024**2:.2f} MB")

# Clear memory
def clear_gpu_memory():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
```

#### 10.9.3 DataLoader Configuration
```python
# Optimal DataLoader settings
dataloader_config = {
    'batch_size': 32,
    'num_workers': 4,
    'pin_memory': True,
    'prefetch_factor': 2,
    'persistent_workers': True,
    'shuffle': True,
    'drop_last': False
}
```

### 10.10 Testing Environment Setup Script
```bash
#!/bin/bash
# setup_test_env.sh

# Create virtual environment
python -m venv test_env
source test_env/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-test.txt

# Set environment variables
export PYTHONPATH=$PYTHONPATH:$(pwd)
export CUDA_VISIBLE_DEVICES=0  # Use first GPU if available
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Run tests
pytest tests/models/test_tft_model.py -v --cov=models --cov-report=term-missing
```

## 11. Current Working Configuration

### 11.1 Test Configuration
```python
# Current working test configuration
TEST_CONFIG = {
    'batch_size': 16,           # Reduced batch size for testing
    'max_epochs': 2,            # Minimal epochs for testing
    'sequence_length': 10,      # Short sequence for testing
    'use_cuda': False,          # CPU-only for testing
    'num_workers': 0,           # No parallel workers for testing
    'log_every_n_steps': 5,     # Frequent logging for small datasets
    'enable_checkpointing': False,  # Disable checkpointing for tests
    'enable_progress_bar': False,   # Disable progress bar for tests
    'enable_model_summary': False   # Disable model summary for tests
}
```

### 11.2 Model Configuration
```python
# Current working model configuration
MODEL_CONFIG = {
    'batch_size': 16,
    'learning_rate': 1e-4,
    'weight_decay': 1e-5,
    'gradient_clip_val': 0.1,
    'num_heads': 4,
    'd_model': 64,
    'd_ff': 128,
    'dropout_rate': 0.1,
    'gru_units': 32,
    'dense_units': 16,
    'optimizer': 'adam',
    'scheduler': {
        'name': 'ReduceLROnPlateau',
        'mode': 'min',
        'factor': 0.1,
        'patience': 5,
        'verbose': False
    }
}
```

### 11.3 DataLoader Configuration
```python
# Current working DataLoader configuration
DATALOADER_CONFIG = {
    'batch_size': 16,
    'num_workers': 0,
    'pin_memory': False,        # Disabled for CPU testing
    'prefetch_factor': None,    # Not used with num_workers=0
    'persistent_workers': False,# Not used with num_workers=0
    'shuffle': True,
    'drop_last': False
}
```

### 11.4 Trainer Configuration
```python
# Current working trainer configuration
TRAINER_CONFIG = {
    'max_epochs': 2,
    'accelerator': 'cpu',       # Force CPU for testing
    'devices': 1,
    'enable_checkpointing': False,
    'enable_progress_bar': False,
    'enable_model_summary': False,
    'log_every_n_steps': 5,
    'deterministic': True,      # Ensure reproducible results
    'precision': 32,            # Full precision for testing
    'callbacks': [
        {
            'class': 'EarlyStopping',
            'monitor': 'val_loss',
            'patience': 3,
            'mode': 'min'
        }
    ]
}
```

### 11.5 Working Test Example
```python
def test_model_training():
    """Current working test implementation"""
    # Load and prepare test data
    df = pd.read_csv('tests/data/test_data.csv')
    df = df.reset_index()
    df['time_idx'] = df.index.astype(int)

    # Prepare sequences
    sequence_length = 10
    training, validation = prepare_tft_data(df, sequence_length)

    # Create dataloaders
    train_dataloader = DataLoader(
        training,
        batch_size=TEST_CONFIG['batch_size'],
        num_workers=TEST_CONFIG['num_workers'],
        pin_memory=TEST_CONFIG['pin_memory'],
        shuffle=True
    )

    val_dataloader = DataLoader(
        validation,
        batch_size=TEST_CONFIG['batch_size'],
        num_workers=TEST_CONFIG['num_workers'],
        pin_memory=TEST_CONFIG['pin_memory'],
        shuffle=False
    )

    # Initialize model
    model = TFTModel(MODEL_CONFIG)
    model.build(training)

    # Train model
    train_metrics = model.train(
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader,
        max_epochs=TEST_CONFIG['max_epochs']
    )

    # Verify training metrics
    assert 'val_loss' in train_metrics
    assert isinstance(train_metrics['val_loss'], float)
    assert train_metrics['val_loss'] > 0
```

### 11.6 Environment Variables
```bash
# Current working environment variables
export PYTHONPATH=$PYTHONPATH:$(pwd)
export CUDA_VISIBLE_DEVICES=""  # Force CPU usage
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export PYTORCH_ENABLE_MPS_FALLBACK=1
```

### 11.7 Warning Suppression
```python
# Current working warning filters
warnings.filterwarnings(
    'ignore',
    category=UserWarning,
    module='pytorch_lightning.*'
)
warnings.filterwarnings(
    'ignore',
    category=UserWarning,
    module='torch.*'
)
warnings.filterwarnings(
    'ignore',
    category=DeprecationWarning
)
```

### 11.8 Test Execution Command
```bash
# Current working test command
python -m pytest tests/models/test_tft_model.py -v \
    --cov=models \
    --cov-report=term-missing \
    --disable-warnings
```

## 12. Environment-Specific Configurations

### 12.1 Windows Configuration
```python
# Windows-specific settings
WINDOWS_CONFIG = {
    'num_workers': 0,           # Windows multiprocessing issues
    'pin_memory': False,        # Windows memory management
    'persistent_workers': False,# Windows worker persistence
    'prefetch_factor': None,    # Windows prefetch settings
    'environment_vars': {
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',
        'PYTORCH_ENABLE_MPS_FALLBACK': '1',
        'CUDA_VISIBLE_DEVICES': ''
    }
}
```

### 12.2 Linux Configuration
```python
# Linux-specific settings
LINUX_CONFIG = {
    'num_workers': 4,           # Linux multiprocessing support
    'pin_memory': True,         # Linux memory pinning
    'persistent_workers': True, # Linux worker persistence
    'prefetch_factor': 2,       # Linux prefetch optimization
    'environment_vars': {
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',
        'CUDA_VISIBLE_DEVICES': '0'
    }
}
```

### 12.3 MacOS Configuration
```python
# MacOS-specific settings
MACOS_CONFIG = {
    'num_workers': 2,           # MacOS multiprocessing limits
    'pin_memory': False,        # MacOS memory management
    'persistent_workers': False,# MacOS worker persistence
    'prefetch_factor': None,    # MacOS prefetch settings
    'environment_vars': {
        'PYTORCH_ENABLE_MPS_FALLBACK': '1',
        'CUDA_VISIBLE_DEVICES': ''
    }
}
```

### 12.4 Cloud Environment Configuration
```python
# Cloud-specific settings
CLOUD_CONFIG = {
    'num_workers': 8,           # Cloud instance capabilities
    'pin_memory': True,         # Cloud memory management
    'persistent_workers': True, # Cloud worker persistence
    'prefetch_factor': 2,       # Cloud prefetch optimization
    'environment_vars': {
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',
        'CUDA_VISIBLE_DEVICES': '0,1'  # Multiple GPUs
    }
}
```

## 13. Other Models Documentation

### 13.1 LSTM Model (PyTorch)
```python
# LSTM Model Configuration
LSTM_CONFIG = {
    'batch_size': 32,
    'learning_rate': 1e-4,
    'hidden_size': 64,
    'num_layers': 2,
    'dropout': 0.2,
    'bidirectional': True,
    'sequence_length': 10
}

# LSTM Test Configuration
LSTM_TEST_CONFIG = {
    'batch_size': 16,           # Reduced for testing
    'learning_rate': 1e-4,
    'hidden_size': 32,          # Reduced for testing
    'num_layers': 1,            # Reduced for testing
    'max_epochs': 2,            # Minimal epochs
    'sequence_length': 10
}
```

### 13.2 ARIMA Model
```python
# ARIMA Model Configuration
ARIMA_CONFIG = {
    'p': 1,                     # AR order
    'd': 1,                     # Differencing order
    'q': 1,                     # MA order
    'seasonal_p': 0,            # Seasonal AR order
    'seasonal_d': 0,            # Seasonal differencing order
    'seasonal_q': 0,            # Seasonal MA order
    'seasonal_m': 0,            # Seasonal period
    'use_seasonal': False,      # Whether to use seasonal component
    'auto_arima': True,         # Whether to use auto ARIMA
    'use_exog': False,          # Whether to use exogenous variables
    'forecast_steps': 1         # Number of steps to forecast
}

# ARIMA Test Configuration
ARIMA_TEST_CONFIG = {
    'p': 1,
    'd': 1,
    'q': 1,
    'auto_arima': False,        # Disable auto ARIMA for testing
    'use_exog': False,
    'forecast_steps': 1
}
```

### 13.3 Common Test Setup for All Models
```python
def setup_test_environment(model_type):
    """Common test environment setup for all models"""
    # Set environment variables
    os.environ['PYTHONPATH'] = os.path.join(os.getcwd(), 'src')
    os.environ['CUDA_VISIBLE_DEVICES'] = ''

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Load test data
    df = pd.read_csv('tests/data/test_data.csv')
    df = df.reset_index()
    df['time_idx'] = df.index.astype(int)

    # Prepare sequences
    sequence_length = 10
    training, validation = prepare_data(df, sequence_length)

    # Create dataloaders
    train_dataloader = DataLoader(
        training,
        batch_size=16,
        num_workers=0,
        pin_memory=False,
        shuffle=True
    )

    val_dataloader = DataLoader(
        validation,
        batch_size=16,
        num_workers=0,
        pin_memory=False,
        shuffle=False
    )

    return train_dataloader, val_dataloader
```

### 13.4 Model-Specific Test Functions
```python
def test_lstm_model():
    """LSTM model test implementation"""
    train_dataloader, val_dataloader = setup_test_environment('lstm')
    model = LSTMModel(LSTM_TEST_CONFIG)
    model.build(train_dataloader.dataset)
    train_metrics = model.train(train_dataloader, val_dataloader)
    assert 'val_loss' in train_metrics

def test_arima_model():
    """ARIMA model test implementation"""
    train_dataloader, val_dataloader = setup_test_environment('arima')
    model = ARIMAModel(ARIMA_TEST_CONFIG)
    model.build(train_dataloader.dataset)
    train_metrics = model.train(train_dataloader, val_dataloader)
    assert 'val_loss' in train_metrics
```