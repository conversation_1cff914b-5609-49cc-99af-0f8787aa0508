@echo off
echo ===================================================
echo Running TensorFlow Training in Docker with GPU
echo ===================================================

REM Check if Docker is installed
where docker >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker is not installed or not in PATH.
    echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop/
    goto end
)

REM Get current directory for mounting
set CURRENT_DIR=%CD%

REM Check if the TensorFlow image exists
docker image inspect tensorflow/tensorflow:latest-gpu >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo TensorFlow GPU image not found. Pulling the latest version...
    docker pull tensorflow/tensorflow:latest-gpu
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to pull TensorFlow image. Please check your internet connection.
        goto end
    )
)

REM Parse command line arguments
set TIMEFRAMES=M5
set MODELS=all
set TERMINAL_ID=1

:parse_args
if "%~1"=="" goto run_docker
if /i "%~1"=="--timeframes" (
    set TIMEFRAMES=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--models" (
    set MODELS=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--terminal_id" (
    set TERMINAL_ID=%~2
    shift
    shift
    goto parse_args
)
shift
goto parse_args

:run_docker
echo Running TensorFlow training in Docker with GPU support...
echo Timeframes: %TIMEFRAMES%
echo Models: %MODELS%
echo Terminal ID: %TERMINAL_ID%
echo.

REM Run the Docker container with GPU support
docker run --gpus all -it --rm ^
    -v "%CURRENT_DIR%:/workspace" ^
    -w /workspace ^
    tensorflow/tensorflow:latest-gpu ^
    python train_with_tensorflow.py --timeframes %TIMEFRAMES% --models %MODELS% --terminal_id %TERMINAL_ID%

echo.
if %ERRORLEVEL% EQU 0 (
    echo Training completed successfully.
) else (
    echo Training failed with error code %ERRORLEVEL%.
)

:end
pause
