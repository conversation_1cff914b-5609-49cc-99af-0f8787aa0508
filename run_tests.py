#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the integration tests for the trading bot.
"""
import unittest
import sys
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/tests.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Create necessary directories
os.makedirs("logs", exist_ok=True)

def run_tests():
    """
    Run all integration tests.
    
    Returns:
        bool: True if all tests passed, False otherwise
    """
    # Load tests
    loader = unittest.TestLoader()
    
    # Find test directory
    test_dir = Path(__file__).parent / 'tests'
    
    if not test_dir.exists():
        logger.error(f"Test directory not found: {test_dir}")
        return False
    
    # Load tests from the test directory
    suite = loader.discover(str(test_dir))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return True if all tests passed
    return result.wasSuccessful()

if __name__ == "__main__":
    logger.info("Starting integration tests...")
    success = run_tests()
    
    if success:
        logger.info("All tests passed!")
        sys.exit(0)
    else:
        logger.error("Some tests failed!")
        sys.exit(1) 