# Trading Bot Documentation

This directory contains comprehensive documentation for the trading bot system.

## Core Guides

- [Comprehensive Guide](COMPREHENSIVE_GUIDE.md) - Complete overview of the entire system
- [Data Collection Guide](DATA_COLLECTION_GUIDE.md) - Detailed instructions for collecting and verifying data
- [Model Training Guide](MODEL_TRAINING_GUIDE.md) - In-depth guide for training and evaluating models
- [Trading Execution Guide](TRADING_EXECUTION_GUIDE.md) - Complete guide for configuring and running the trading bot
- [Trading Strategy Guide](TRADING_STRATEGY_GUIDE.md) - Detailed information about trading strategies
- [Bot Control Guide](BOT_CONTROL_GUIDE.md) - Instructions for starting, stopping, and managing the bots
- [Visualization Guide](visualization_guide.md) - Guide for using the visualization system

## Model Documentation

- [LSTM Model](models/lstm_model_documentation.md) - Documentation for the LSTM model
- [TFT Model](models/tft_model_documentation.md) - Documentation for the TFT model
- [ARIMA Model](models/arima_model_documentation.md) - Documentation for the ARIMA model

## Other Documentation

- [Changes Summary](CHANGES_SUMMARY.md) - Summary of recent changes to the system

## Getting Started

If you're new to the trading bot system, start with the [Comprehensive Guide](COMPREHENSIVE_GUIDE.md), which provides an overview of the entire system and links to more detailed guides for specific components.

For specific tasks:

1. **Collecting Data**: Follow the [Data Collection Guide](DATA_COLLECTION_GUIDE.md)
2. **Training Models**: Follow the [Model Training Guide](MODEL_TRAINING_GUIDE.md)
3. **Running the Bot**: Follow the [Trading Execution Guide](TRADING_EXECUTION_GUIDE.md)
4. **Controlling the Bot**: Follow the [Bot Control Guide](BOT_CONTROL_GUIDE.md)
5. **Visualizing Results**: Follow the [Visualization Guide](visualization_guide.md)
