"""
Convert CSV files to Parquet format with the correct naming convention.
"""
import pandas as pd
import os
from pathlib import Path

# Directory containing the CSV files
input_dir = Path('data/combined')
output_dir = Path('data/combined')

# List of timeframes
timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
symbol = 'BTCUSD.a'

for timeframe in timeframes:
    # Input CSV file
    csv_file = input_dir / f"{symbol}_{timeframe}_combined_data.csv"
    
    # Output Parquet file with the correct naming convention
    parquet_file = output_dir / f"{symbol}_{timeframe}.parquet"
    
    if csv_file.exists():
        print(f"Converting {csv_file} to {parquet_file}")
        
        # Read CSV file
        df = pd.read_csv(csv_file)
        
        # Convert 'time' column to datetime if it exists
        if 'time' in df.columns:
            df['time'] = pd.to_datetime(df['time'])
            
        # Save as Parquet
        df.to_parquet(parquet_file, index=False)
        print(f"Converted {csv_file} to {parquet_file}")
    else:
        print(f"CSV file not found: {csv_file}")

print("Conversion completed.")
