"""
MT5 Terminal Management Script

This script provides command-line utilities for managing MT5 terminals.
It can start, stop, and check the status of MT5 terminals.
"""

import os
import sys
import time
import logging
import argparse
from pathlib import Path

# Add project root to path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
sys.path.append(PROJECT_ROOT)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(PROJECT_ROOT, 'logs', 'mt5_terminals.log'))
    ]
)
logger = logging.getLogger('mt5_terminals')

# Import MT5 Terminal Launcher
from utils.mt5_launcher import (
    start_terminal,
    stop_terminal,
    is_terminal_running,
    start_all_terminals,
    stop_all_terminals,
    get_running_terminals
)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Manage MT5 terminals')

    # Action group
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--start', action='store_true', help='Start MT5 terminals')
    action_group.add_argument('--stop', action='store_true', help='Stop MT5 terminals')
    action_group.add_argument('--status', action='store_true', help='Check MT5 terminals status')
    action_group.add_argument('--restart', action='store_true', help='Restart MT5 terminals')

    # Terminal selection
    parser.add_argument('--terminal', '-t', type=str, help='Terminal ID to manage (default: all terminals)')
    parser.add_argument('--all', '-a', action='store_true', help='Manage all terminals')

    # Other options
    parser.add_argument('--wait', '-w', type=int, default=5, help='Wait time in seconds between operations (default: 5)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')

    return parser.parse_args()

def main():
    """Main entry point for the script."""
    args = parse_arguments()

    # Set log level
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # Determine which terminals to manage
    if args.terminal:
        terminals = [args.terminal]
    elif args.all:
        terminals = ["1", "2", "3", "4", "5"]  # All terminals
    else:
        terminals = ["1", "2", "3", "4", "5"]  # Default to all terminals

    logger.info(f"Managing terminals: {terminals}")

    # Execute requested action
    if args.start:
        start_terminals(terminals, args.wait)
    elif args.stop:
        stop_terminals(terminals, args.wait)
    elif args.status:
        check_terminals_status(terminals)
    elif args.restart:
        restart_terminals(terminals, args.wait)

    return 0

def start_terminals(terminals, wait_time):
    """Start the specified terminals."""
    logger.info(f"Starting terminals: {terminals}")

    results = {}
    for terminal_id in terminals:
        logger.info(f"Starting terminal {terminal_id}...")
        success = start_terminal(terminal_id)
        results[terminal_id] = success

        if success:
            logger.info(f"Terminal {terminal_id} started successfully")
        else:
            logger.error(f"Failed to start terminal {terminal_id}")

        # Wait between operations
        if wait_time > 0 and terminal_id != terminals[-1]:
            time.sleep(wait_time)

    # Print summary
    logger.info("Start operation summary:")
    for terminal_id, success in results.items():
        status = "SUCCESS" if success else "FAILED"
        logger.info(f"Terminal {terminal_id}: {status}")

    return all(results.values())

def stop_terminals(terminals, wait_time):
    """Stop the specified terminals."""
    logger.info(f"Stopping terminals: {terminals}")

    results = {}
    for terminal_id in terminals:
        logger.info(f"Stopping terminal {terminal_id}...")
        success = stop_terminal(terminal_id)
        results[terminal_id] = success

        if success:
            logger.info(f"Terminal {terminal_id} stopped successfully")
        else:
            logger.error(f"Failed to stop terminal {terminal_id}")

        # Wait between operations
        if wait_time > 0 and terminal_id != terminals[-1]:
            time.sleep(wait_time)

    # Print summary
    logger.info("Stop operation summary:")
    for terminal_id, success in results.items():
        status = "SUCCESS" if success else "FAILED"
        logger.info(f"Terminal {terminal_id}: {status}")

    return all(results.values())

def check_terminals_status(terminals):
    """Check the status of the specified terminals."""
    logger.info(f"Checking status of terminals: {terminals}")

    results = {}
    for terminal_id in terminals:
        running = is_terminal_running(terminal_id)
        results[terminal_id] = running

        status = "RUNNING" if running else "STOPPED"
        logger.info(f"Terminal {terminal_id}: {status}")

    # Print summary
    logger.info("Status summary:")
    for terminal_id, running in results.items():
        status = "RUNNING" if running else "STOPPED"
        logger.info(f"Terminal {terminal_id}: {status}")

    return results

def restart_terminals(terminals, wait_time):
    """Restart the specified terminals."""
    logger.info(f"Restarting terminals: {terminals}")

    # Stop terminals
    stop_terminals(terminals, wait_time)

    # Wait before starting
    logger.info(f"Waiting {wait_time} seconds before starting terminals...")
    time.sleep(wait_time)

    # Start terminals
    return start_terminals(terminals, wait_time)

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)
