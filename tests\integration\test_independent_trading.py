"""
Test script for the independent trading system.

This script tests the independent trading system with proportional ensemble allocation.
It initializes the TradingManager and runs it for a specified number of iterations.
"""

import os
import sys
import time
import logging
import argparse
from datetime import datetime

# Import the independent trading system
from independent_trading_bot import TradingManager, IndependentTradingBot, TerminalConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"independent_trading_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test the independent trading system")
    parser.add_argument("--iterations", type=int, default=3, help="Number of iterations to run")
    parser.add_argument("--interval", type=int, default=300, help="Interval between iterations in seconds")
    parser.add_argument("--terminal", type=int, help="Specific terminal ID to test (1-5)")
    return parser.parse_args()


def test_single_terminal(terminal_id, iterations, interval):
    """Test a single terminal."""
    logger.info(f"Testing terminal {terminal_id} for {iterations} iterations with {interval}s interval")

    # Define terminal configuration
    model_mapping = {
        1: "lstm",
        3: "tft",
        5: "arima"
    }

    primary_model = model_mapping.get(terminal_id, "lstm")
    terminal_config = TerminalConfig(
        terminal_id=terminal_id,
        primary_model=primary_model,
        allocation_percentage=100  # 100% allocation for single terminal test
    )

    # Initialize trading bot
    bot = IndependentTradingBot(terminal_config)

    # Run iterations
    for i in range(iterations):
        logger.info(f"Running iteration {i+1}/{iterations}")

        # Run iteration
        result = bot.run_iteration()

        # Log result
        if result:
            logger.info(f"Iteration {i+1} completed successfully")

            # Get performance metrics
            metrics = bot.calculate_performance_metrics()
            logger.info(f"Performance metrics: {metrics}")
        else:
            logger.error(f"Iteration {i+1} failed")

        # Sleep between iterations
        if i < iterations - 1:
            logger.info(f"Sleeping for {interval} seconds")
            time.sleep(interval)

    logger.info("Test completed")


def test_all_terminals(iterations, interval):
    """Test all terminals using the TradingManager."""
    logger.info(f"Testing all terminals for {iterations} iterations with {interval}s interval")

    # Initialize trading manager
    manager = TradingManager()

    # Run iterations
    for i in range(iterations):
        logger.info(f"Running iteration {i+1}/{iterations}")

        # Run all bots
        manager.run_all_bots()

        # Get performance summary
        summary = manager.get_performance_summary()
        logger.info(f"Performance summary: {summary}")

        # Sleep between iterations
        if i < iterations - 1:
            logger.info(f"Sleeping for {interval} seconds")
            time.sleep(interval)

    logger.info("Test completed")


def main():
    """Main function."""
    # Parse arguments
    args = parse_arguments()

    try:
        # Test specific terminal or all terminals
        if args.terminal:
            valid_terminals = [1, 3, 5]
            if args.terminal in valid_terminals:
                test_single_terminal(args.terminal, args.iterations, args.interval)
            else:
                logger.error(f"Invalid terminal ID: {args.terminal}. Must be one of {valid_terminals}.")
                return 1
        else:
            test_all_terminals(args.iterations, args.interval)

        return 0

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        return 0

    except Exception as e:
        logger.error(f"Error in test: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
