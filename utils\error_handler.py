"""
Error Handler - Centralized error handling and recovery system for the trading bot.
Provides consistent error logging, tracking, and recovery mechanisms.
"""

import logging
import traceback
import sys
import json
import uuid
from typing import Dict, List, Callable, Any, Optional, TypeVar, Type, Union, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime
import threading
from collections import defaultdict, Counter
from enum import Enum

logger = logging.getLogger(__name__)

# Type definitions
ErrorCallback = Callable[[Exception, Dict[str, Any]], Any]
RecoveryHandler = Callable[[Exception, Dict[str, Any]], bool]
T = TypeVar('T')  # Generic type for error context

class ErrorCategory(Enum):
    """Categories of errors for better handling and statistics."""
    NETWORK = "network"  # Network-related errors
    AUTHENTICATION = "auth"  # Authentication failures
    DATA = "data"  # Data integrity/parsing issues
    EXECUTION = "execution"  # Trade execution problems
    RESOURCE = "resource"  # Resource exhaustion (memory, CPU)
    MT5_API = "mt5_api"  # MT5-specific API errors
    MODEL = "model"  # ML model errors
    CONFIG = "config"  # Configuration errors
    UNKNOWN = "unknown"  # Uncategorized errors

@dataclass
class ErrorInfo:
    """Information about an error occurrence."""
    error_type: str  # Type of the error/exception
    error_message: str  # Error message
    timestamp: datetime = field(default_factory=datetime.now)  # When the error occurred
    traceback: str = None  # Full traceback of the error
    context: Dict[str, Any] = field(default_factory=dict)  # Additional context
    recovery_attempted: bool = False  # Whether recovery was attempted
    recovery_succeeded: bool = False  # Whether recovery succeeded
    error_id: str = field(default_factory=lambda: str(uuid.uuid4()))  # Unique ID for this error instance
    source: str = None  # Source of the error (module, function, etc.)
    severity: str = "ERROR"  # Severity level (ERROR, WARNING, CRITICAL)
    category: ErrorCategory = ErrorCategory.UNKNOWN  # Error category for better classification

class ErrorHandler:
    """
    Centralized error handler for the trading bot.
    
    Provides methods for:
    - Consistent error logging and tracking
    - Error recovery attempts with registered handlers
    - Maintaining error history and statistics
    - Supporting retry mechanisms and circuit breakers
    """
    
    def __init__(self, max_error_history: int = 1000):
        """
        Initialize the error handler.
        
        Args:
            max_error_history: Maximum number of errors to keep in history
        """
        self.max_error_history = max_error_history
        self._error_history: List[ErrorInfo] = []
        self._recovery_handlers: Dict[str, List[RecoveryHandler]] = defaultdict(list)
        self._error_callbacks: Dict[str, List[ErrorCallback]] = defaultdict(list)
        self._lock = threading.RLock()
        self._error_counters = Counter()  # Counts errors by type
        self._source_counters = Counter()  # Counts errors by source
        self._category_counters = Counter()  # Counts errors by category
        logger.info("Error handler initialized")
    
    def categorize_error(self, error: Exception) -> ErrorCategory:
        """
        Categorize an error based on its type and message.
        
        Args:
            error: The exception to categorize
            
        Returns:
            ErrorCategory: The category of the error
        """
        # Try to import necessary modules only when needed
        try:
            import socket
            import MetaTrader5 as mt5
        except ImportError:
            # If modules can't be imported, we can still categorize based on string matching
            pass
        
        error_type = type(error).__name__
        error_msg = str(error).lower()
        
        # Network errors
        if any(net_err in error_type for net_err in ['ConnectionError', 'Timeout', 'Socket']):
            return ErrorCategory.NETWORK
        if ('socket.error' in str(type(error)) or "connection" in error_msg or 
            "timeout" in error_msg or "network" in error_msg):
            return ErrorCategory.NETWORK
        
        # Authentication errors
        if "login" in error_msg or "password" in error_msg or "auth" in error_msg or "permission" in error_msg:
            return ErrorCategory.AUTHENTICATION
        
        # MT5-specific errors
        try:
            import MetaTrader5 as mt5
            if hasattr(mt5, 'last_error') and mt5.last_error()[0] > 0:
                return ErrorCategory.MT5_API
        except (ImportError, Exception):
            pass
        
        if "mt5" in error_msg or "metatrader" in error_msg:
            return ErrorCategory.MT5_API
        
        # Data errors
        if any(data_err in error_type for data_err in ['ValueError', 'KeyError', 'IndexError', 'TypeError']):
            return ErrorCategory.DATA
        if "invalid data" in error_msg or "missing" in error_msg or "data" in error_msg:
            return ErrorCategory.DATA
        
        # Resource errors
        if 'Memory' in error_type or 'OutOfMemory' in error_type or 'Resource' in error_type:
            return ErrorCategory.RESOURCE
        if "memory" in error_msg or "resource" in error_msg or "capacity" in error_msg:
            return ErrorCategory.RESOURCE
        
        # Model errors
        if any(model_err in error_type for model_err in ['ModelError', 'TensorError', 'CudaError']):
            return ErrorCategory.MODEL
        if "model" in error_msg or "prediction" in error_msg or "inference" in error_msg:
            return ErrorCategory.MODEL
        
        # Config errors
        if 'ConfigError' in error_type or 'ParameterError' in error_type:
            return ErrorCategory.CONFIG
        if "config" in error_msg or "parameter" in error_msg or "setting" in error_msg:
            return ErrorCategory.CONFIG
        
        # Execution errors
        if "trade" in error_msg or "order" in error_msg or "position" in error_msg or "execution" in error_msg:
            return ErrorCategory.EXECUTION
        
        return ErrorCategory.UNKNOWN
    
    def handle_error(self, 
                    error: Exception, 
                    context: Dict[str, Any] = None, 
                    source: str = None,
                    severity: str = "ERROR",
                    attempt_recovery: bool = True) -> ErrorInfo:
        """
        Handle an error - log it, store it, and attempt recovery if appropriate.
        
        Args:
            error: The exception to handle
            context: Additional context about when/where the error occurred
            source: Source of the error (module, function, etc.)
            severity: Severity level (ERROR, WARNING, CRITICAL)
            attempt_recovery: Whether to attempt recovery
            
        Returns:
            ErrorInfo: Information about the handled error
        """
        # Create error info
        error_type = error.__class__.__name__
        category = self.categorize_error(error)
        
        error_info = ErrorInfo(
            error_type=error_type,
            error_message=str(error),
            traceback=traceback.format_exc(),
            context=context or {},
            source=source,
            severity=severity,
            category=category
        )
        
        # Log the error
        self._log_error(error_info)
        
        # Store in history
        with self._lock:
            self._error_history.append(error_info)
            if len(self._error_history) > self.max_error_history:
                self._error_history = self._error_history[-self.max_error_history:]
            
            # Update counters
            self._error_counters[error_type] += 1
            if source:
                self._source_counters[source] += 1
            self._category_counters[category.value] += 1
        
        # Attempt recovery if enabled
        if attempt_recovery:
            error_info.recovery_attempted = True
            error_info.recovery_succeeded = self._attempt_recovery(error, error_info.context)
        
        # Run any registered callbacks for this error type
        self._run_error_callbacks(error, error_info.context)
        
        return error_info
    
    def _log_error(self, error_info: ErrorInfo):
        """
        Log an error with appropriate level and format.
        
        Args:
            error_info: Information about the error
        """
        # Determine log level
        log_level = getattr(logging, error_info.severity, logging.ERROR)
        
        # Create log message
        log_message = (
            f"ERROR [{error_info.error_id}]: {error_info.error_type}: {error_info.error_message} "
            f"(Source: {error_info.source or 'unknown'}, Category: {error_info.category.value})"
        )
        
        # Add context info if available
        if error_info.context:
            try:
                context_str = json.dumps(error_info.context, default=str)
                log_message += f" | Context: {context_str}"
            except Exception:
                log_message += f" | Context: {error_info.context}"
        
        # Log the message
        logger.log(log_level, log_message)
        
        # Log traceback at debug level
        if error_info.traceback:
            logger.debug(f"Traceback for error {error_info.error_id}:\n{error_info.traceback}")
    
    def _attempt_recovery(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Attempt to recover from an error using registered recovery handlers.
        
        Args:
            error: The exception to recover from
            context: Error context
            
        Returns:
            bool: True if recovery was successful, False otherwise
        """
        error_type = error.__class__.__name__
        handlers = []
        
        # Get handlers for this specific error type
        with self._lock:
            handlers.extend(self._recovery_handlers[error_type])
            
            # Also get handlers for parent exception types
            for base in error.__class__.__mro__[1:]:
                if base is Exception or base is BaseException:
                    continue
                base_name = base.__name__
                handlers.extend(self._recovery_handlers[base_name])
                
            # Finally add general handlers
            handlers.extend(self._recovery_handlers["*"])
        
        # No handlers available
        if not handlers:
            return False
        
        # Try each handler until one succeeds
        for handler in handlers:
            try:
                if handler(error, context):
                    logger.info(f"Successfully recovered from {error_type} using handler")
                    return True
            except Exception as recovery_error:
                logger.error(f"Error in recovery handler: {recovery_error}")
        
        return False
    
    def register_recovery_handler(self, 
                                 handler: RecoveryHandler, 
                                 error_types: Union[str, List[str]] = "*") -> None:
        """
        Register a recovery handler for specific error types.
        
        Args:
            handler: Function that attempts to recover from an error
            error_types: Error type(s) this handler can recover from ('*' for all)
        """
        if isinstance(error_types, str):
            error_types = [error_types]
        
        with self._lock:
            for error_type in error_types:
                self._recovery_handlers[error_type].append(handler)
        
        logger.debug(f"Registered recovery handler for error types: {error_types}")
    
    def register_error_callback(self, 
                               callback: ErrorCallback, 
                               error_types: Union[str, List[str]] = "*") -> None:
        """
        Register a callback to be executed when specific errors occur.
        
        Args:
            callback: Function to call when an error occurs
            error_types: Error type(s) this callback handles ('*' for all)
        """
        if isinstance(error_types, str):
            error_types = [error_types]
        
        with self._lock:
            for error_type in error_types:
                self._error_callbacks[error_type].append(callback)
    
    def _run_error_callbacks(self, error: Exception, context: Dict[str, Any]) -> None:
        """
        Run registered callbacks for an error.
        
        Args:
            error: The exception that occurred
            context: Error context
        """
        error_type = error.__class__.__name__
        callbacks = []
        
        # Get callbacks for this error type
        with self._lock:
            callbacks.extend(self._error_callbacks[error_type])
            
            # Also get callbacks for parent exception types
            for base in error.__class__.__mro__[1:]:
                if base is Exception or base is BaseException:
                    continue
                base_name = base.__name__
                callbacks.extend(self._error_callbacks[base_name])
                
            # Finally add general callbacks
            callbacks.extend(self._error_callbacks["*"])
        
        # Run callbacks
        for callback in callbacks:
            try:
                callback(error, context)
            except Exception as callback_error:
                logger.error(f"Error in error callback: {callback_error}")
    
    def get_error_history(self, 
                         error_type: str = None, 
                         source: str = None,
                         severity: str = None,
                         limit: int = None) -> List[ErrorInfo]:
        """
        Get error history, optionally filtered by type, source, or severity.
        
        Args:
            error_type: Filter by error type
            source: Filter by error source
            severity: Filter by severity level
            limit: Maximum number of errors to return
            
        Returns:
            List[ErrorInfo]: Filtered error history
        """
        with self._lock:
            filtered = self._error_history
            
            if error_type:
                filtered = [e for e in filtered if e.error_type == error_type]
            
            if source:
                filtered = [e for e in filtered if e.source == source]
            
            if severity:
                filtered = [e for e in filtered if e.severity == severity]
            
            if limit and limit > 0:
                filtered = filtered[-limit:]
            
            return filtered
    
    def get_error_stats(self) -> Dict[str, Any]:
        """
        Get statistics about errors handled.
        
        Returns:
            Dict[str, Any]: Error statistics
        """
        with self._lock:
            stats = {
                "total_errors": len(self._error_history),
                "error_types_count": dict(self._error_counters),
                "source_count": dict(self._source_counters),
                "category_count": dict(self._category_counters),
                "recovered_count": sum(1 for e in self._error_history if e.recovery_succeeded),
                "recovery_attempts": sum(1 for e in self._error_history if e.recovery_attempted),
                "severity_count": {
                    "ERROR": sum(1 for e in self._error_history if e.severity == "ERROR"),
                    "WARNING": sum(1 for e in self._error_history if e.severity == "WARNING"),
                    "CRITICAL": sum(1 for e in self._error_history if e.severity == "CRITICAL")
                }
            }
            
            # Calculate recovery rate
            attempts = stats["recovery_attempts"]
            if attempts > 0:
                stats["recovery_rate"] = stats["recovered_count"] / attempts
            else:
                stats["recovery_rate"] = 0.0
                
            return stats
    
    def clear_error_history(self) -> None:
        """Clear error history."""
        with self._lock:
            self._error_history.clear()
            self._error_counters.clear()
            self._source_counters.clear()
            self._category_counters.clear()
        logger.info("Error history cleared")
    
    def get_recovery_success_rate(self, error_type: str = None) -> float:
        """
        Get the success rate of recovery attempts.
        
        Args:
            error_type: Filter by error type
            
        Returns:
            float: Recovery success rate (0.0 to 1.0)
        """
        with self._lock:
            filtered = self._error_history
            
            if error_type:
                filtered = [e for e in filtered if e.error_type == error_type]
            
            attempted = [e for e in filtered if e.recovery_attempted]
            
            if not attempted:
                return 0.0
            
            succeeded = [e for e in attempted if e.recovery_succeeded]
            return len(succeeded) / len(attempted)
    
    def safe_execute(self, 
                    func: Callable[..., T], 
                    *args, 
                    on_error: Callable[[Exception], T] = None,
                    retry_count: int = 0,
                    retry_delay: float = 1.0,
                    error_context: Dict[str, Any] = None,
                    source: str = None,
                    **kwargs) -> T:
        """
        Execute a function with error handling and optional retry.
        
        Args:
            func: Function to execute
            *args: Arguments to pass to the function
            on_error: Function to call if an error occurs
            retry_count: Number of retries if the function fails
            retry_delay: Delay between retries in seconds
            error_context: Additional context for error handling
            source: Source identifier for the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            T: Result of the function or on_error handler
        """
        context = error_context or {}
        context.update({
            "function": func.__name__,
            "args": args,
            "kwargs": {k: v for k, v in kwargs.items() if not isinstance(v, (bytes, bytearray))}
        })
        
        for attempt in range(retry_count + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Set attempt information in context
                context["attempt"] = attempt
                context["max_attempts"] = retry_count + 1
                
                # Handle the error
                error_info = self.handle_error(e, context=context, source=source)
                
                # If this is the last attempt and we have an on_error handler
                if attempt == retry_count and on_error:
                    return on_error(e)
                
                # If recovery succeeded, try again
                if error_info.recovery_succeeded and attempt < retry_count:
                    if retry_delay > 0:
                        import time
                        time.sleep(retry_delay)
                    continue
                
                # If we're out of retries, re-raise the exception
                if attempt == retry_count:
                    raise
                
                # Otherwise retry after delay
                if retry_delay > 0:
                    import time
                    time.sleep(retry_delay)
    
    def create_circuit_breaker(self, 
                              max_failures: int = 3, 
                              reset_timeout: float = 60.0,
                              half_open_timeout: float = 30.0) -> 'CircuitBreaker':
        """
        Create a circuit breaker to prevent repeated failures.
        
        Args:
            max_failures: Maximum number of failures before opening the circuit
            reset_timeout: Time in seconds before resetting the circuit
            half_open_timeout: Time in seconds before entering half-open state
            
        Returns:
            CircuitBreaker: A circuit breaker instance
        """
        return CircuitBreaker(self, max_failures, reset_timeout, half_open_timeout)
    
    @staticmethod
    def capture_unhandled_exceptions():
        """Set up global exception handler to capture unhandled exceptions."""
        def global_exception_handler(exctype, value, tb):
            # Log the exception
            logger.critical("Unhandled exception", exc_info=(exctype, value, tb))
            
            # Call the original handler
            sys.__excepthook__(exctype, value, tb)
        
        # Set the exception hook
        sys.excepthook = global_exception_handler
        logger.info("Global exception handler installed")


class CircuitBreaker:
    """
    Circuit breaker pattern implementation to prevent cascading failures.
    
    States:
    - CLOSED: Normal operation, failures are counted
    - OPEN: All calls fail fast without executing the function
    - HALF_OPEN: Test calls are allowed through to see if the issue is resolved
    """
    
    # Circuit states
    CLOSED = "CLOSED"
    OPEN = "OPEN"
    HALF_OPEN = "HALF_OPEN"
    
    def __init__(self, 
                error_handler: ErrorHandler,
                max_failures: int = 3,
                reset_timeout: float = 60.0,
                half_open_timeout: float = 30.0):
        """
        Initialize a circuit breaker.
        
        Args:
            error_handler: ErrorHandler instance
            max_failures: Maximum number of failures before opening the circuit
            reset_timeout: Time in seconds before resetting the circuit
            half_open_timeout: Time in seconds before entering half-open state
        """
        self.error_handler = error_handler
        self.max_failures = max_failures
        self.reset_timeout = reset_timeout
        self.half_open_timeout = half_open_timeout
        
        self._state = self.CLOSED
        self._failure_count = 0
        self._last_failure_time = None
        self._lock = threading.RLock()
    
    def execute(self, 
               func: Callable[..., T], 
               *args, 
               circuit_name: str = None,
               **kwargs) -> T:
        """
        Execute a function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args: Arguments to pass to the function
            circuit_name: Name of this circuit for logging
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            T: Result of the function
            
        Raises:
            CircuitOpenError: If the circuit is open
            Exception: Any exception raised by the function
        """
        circuit_name = circuit_name or func.__name__
        
        with self._lock:
            # Check if we should transition from OPEN to HALF_OPEN
            if self._state == self.OPEN and self._last_failure_time:
                time_since_failure = (datetime.now() - self._last_failure_time).total_seconds()
                if time_since_failure >= self.half_open_timeout:
                    logger.info(f"Circuit {circuit_name} transitioning from OPEN to HALF_OPEN")
                    self._state = self.HALF_OPEN
            
            # If circuit is open, fail fast
            if self._state == self.OPEN:
                logger.warning(f"Circuit {circuit_name} is OPEN, failing fast")
                raise CircuitOpenError(f"Circuit {circuit_name} is open")
        
        # Execute the function
        try:
            result = func(*args, **kwargs)
            
            # Reset on success if in HALF_OPEN state
            with self._lock:
                if self._state == self.HALF_OPEN:
                    logger.info(f"Circuit {circuit_name} transitioning from HALF_OPEN to CLOSED")
                    self._state = self.CLOSED
                    self._failure_count = 0
                    self._last_failure_time = None
            
            return result
            
        except Exception as e:
            with self._lock:
                # Record the failure
                self._failure_count += 1
                self._last_failure_time = datetime.now()
                
                # Check if we should open the circuit
                if self._state == self.CLOSED and self._failure_count >= self.max_failures:
                    logger.warning(f"Circuit {circuit_name} opening after {self._failure_count} failures")
                    self._state = self.OPEN
                
                # If in HALF_OPEN and we get a failure, go back to OPEN
                elif self._state == self.HALF_OPEN:
                    logger.warning(f"Circuit {circuit_name} transitioning from HALF_OPEN back to OPEN due to failure")
                    self._state = self.OPEN
            
            # Log the error and re-raise
            self.error_handler.handle_error(
                e, 
                context={
                    "circuit_name": circuit_name,
                    "circuit_state": self._state,
                    "failure_count": self._failure_count
                },
                source=f"circuit_breaker.{circuit_name}"
            )
            raise
    
    def get_state(self) -> str:
        """Get the current state of the circuit."""
        with self._lock:
            return self._state
    
    def reset(self) -> None:
        """Manually reset the circuit to CLOSED state."""
        with self._lock:
            self._state = self.CLOSED
            self._failure_count = 0
            self._last_failure_time = None
            logger.info("Circuit manually reset to CLOSED")


class CircuitOpenError(Exception):
    """Exception raised when a circuit is open."""
    pass 