#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script to copy ARIMA model files to all terminal directories.
This ensures that the ARIMA model is available for all terminals.
"""

import os
import shutil
import json
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config():
    """Load configuration from config file."""
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return None

def get_terminal_ids(config):
    """Get terminal IDs from config."""
    try:
        if not config or 'mt5' not in config or 'terminals' not in config['mt5']:
            logger.warning("No terminals found in config")
            return []

        # Handle different config formats
        terminals = config['mt5']['terminals']
        if isinstance(terminals, list):
            return [str(terminal.get('id', i+1)) for i, terminal in enumerate(terminals)]
        elif isinstance(terminals, dict):
            return [str(k) for k in terminals.keys()]
        else:
            logger.warning(f"Unexpected terminals format: {type(terminals)}")
            return []
    except Exception as e:
        logger.error(f"Error getting terminal IDs: {str(e)}")
        return []

def get_timeframes():
    """Get all timeframes."""
    return ['M5', 'M15', 'M30', 'H1', 'H4']

def copy_arima_model(source_dir, target_dir):
    """Copy ARIMA model files from source to target directory."""
    try:
        # Create target directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True)

        # Copy config.json
        if os.path.exists(os.path.join(source_dir, 'config.json')):
            shutil.copy(
                os.path.join(source_dir, 'config.json'),
                os.path.join(target_dir, 'config.json')
            )
            logger.info(f"Copied config.json to {target_dir}")

        # Copy last_values.npy
        if os.path.exists(os.path.join(source_dir, 'last_values.npy')):
            shutil.copy(
                os.path.join(source_dir, 'last_values.npy'),
                os.path.join(target_dir, 'last_values.npy')
            )
            logger.info(f"Copied last_values.npy to {target_dir}")

        # Copy model.pkl
        if os.path.exists(os.path.join(source_dir, 'model.pkl')):
            shutil.copy(
                os.path.join(source_dir, 'model.pkl'),
                os.path.join(target_dir, 'model.pkl')
            )
            logger.info(f"Copied model.pkl to {target_dir}")

        return True
    except Exception as e:
        logger.error(f"Error copying model files: {str(e)}")
        return False

def main():
    """Main function."""
    logger.warning("=" * 80)
    logger.warning("⚠️  WARNING: This script creates massive model duplication!")
    logger.warning("⚠️  It copies 21GB ARIMA models to multiple terminal directories.")
    logger.warning("⚠️  This was the cause of 515GB of redundant model files.")
    logger.warning("⚠️  Modern model loading can find models without duplication.")
    logger.warning("=" * 80)

    # Ask for explicit confirmation
    response = input("\n❓ Are you sure you want to create duplicate model files? (type 'YES' to confirm): ")
    if response != 'YES':
        logger.info("❌ Operation cancelled. No models will be copied.")
        logger.info("💡 Tip: Modern model loading automatically finds models in canonical locations.")
        return

    logger.info("Starting ARIMA model copy process")

    # Load config
    config = load_config()
    terminal_ids = get_terminal_ids(config)
    timeframes = get_timeframes()

    if not terminal_ids:
        logger.warning("No terminal IDs found, using default terminals 1-5")
        terminal_ids = ['1', '2', '3', '4', '5']

    logger.info(f"Found terminal IDs: {terminal_ids}")
    logger.info(f"Using timeframes: {timeframes}")

    # Calculate total space that will be used
    source_dir = 'models/arima_BTCUSD.a_M5'
    if not os.path.exists(source_dir):
        logger.error(f"Source model directory {source_dir} does not exist")
        return

    # Calculate source model size
    source_size = 0
    for root, _, files in os.walk(source_dir):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                source_size += os.path.getsize(file_path)
            except (OSError, IOError):
                pass

    total_copies = len(terminal_ids) * len(timeframes)
    total_space_gb = (source_size * total_copies) / (1024**3)

    logger.warning(f"⚠️  This will create {total_copies} copies of the model")
    logger.warning(f"⚠️  Total space required: {total_space_gb:.1f} GB")

    final_confirm = input(f"\n❓ Proceed with creating {total_space_gb:.1f} GB of duplicate files? (type 'CONFIRM' to proceed): ")
    if final_confirm != 'CONFIRM':
        logger.info("❌ Operation cancelled.")
        return

    # Copy model to all terminal directories
    for terminal_id in terminal_ids:
        for timeframe in timeframes:
            target_dir = f'models/{terminal_id}/{timeframe}/arima_BTCUSD.a_M5'
            logger.info(f"Copying ARIMA model to {target_dir}")
            success = copy_arima_model(source_dir, target_dir)
            if success:
                logger.info(f"Successfully copied ARIMA model to {target_dir}")
            else:
                logger.error(f"Failed to copy ARIMA model to {target_dir}")

    logger.info("ARIMA model copy process completed")
    logger.warning("⚠️  Remember: This creates massive duplication. Consider using symbolic links instead.")

if __name__ == "__main__":
    main()
