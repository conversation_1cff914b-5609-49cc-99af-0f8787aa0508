# Data Folders Analysis

## Overview

This document provides a comprehensive analysis of what folders models use to get their training data, including all conflicts, errors, and inconsistencies found in the codebase.

## Data Folder Structure

The codebase contains multiple data directories with different purposes:

### Primary Data Directories

1. **`data/historical/btcusd.a/`** - Main training data location
   - Contains: `BTCUSD.a_M5.parquet`, `BTCUSD.a_M15.parquet`, etc.
   - Used by: Most training scripts as default
   - Status: ✅ Consistent primary location

2. **`data/combined/`** - Combined/processed data
   - Contains: Combined data from multiple terminals
   - Used by: Some batch scripts (inconsistent)
   - Status: ⚠️ Inconsistent usage

3. **`data/cache/`** - Cached data files
   - Contains: Processed cache files
   - Used by: Data processing utilities
   - Status: ✅ Utility purpose

4. **`data/terminal_X/`** - Terminal-specific data
   - Contains: Data organized by terminal ID
   - Used by: Terminal-specific processing
   - Status: ✅ Organized structure

### Secondary Data Directories

5. **`data/training/`** - Training-specific data
6. **`data/testing/`** - Testing-specific data  
7. **`data/validation/`** - Validation data and charts
8. **`data/processed/`** - Processed data by terminal and timeframe
9. **`data/raw/`** - Raw data storage

## Model Data Usage Analysis

### LSTM Models

#### Primary Training Scripts
- **`train_lstm_btcusd.py`**
  - Default path: `'data/historical/btcusd.a'`
  - File pattern: `BTCUSD.a_{timeframe}.parquet`
  - Status: ✅ Consistent

- **`train_lstm_single.py`**
  - Default path: `'data/historical/btcusd.a'` (via argument)
  - File pattern: `BTCUSD.a_{timeframe}.parquet`
  - Status: ✅ Consistent

#### Configuration
- **`config/config.json`**
  - Base path: `"data_base_path": "data/"`
  - Status: ✅ Configurable

### ARIMA Models

#### Primary Training Scripts
- **`train_arima_single.py`**
  - Default path: `'data/historical/btcusd.a'`
  - File pattern: `BTCUSD.a_{timeframe}.parquet`
  - Status: ✅ Consistent

#### Configuration
- Uses same base configuration as LSTM models
- Status: ✅ Consistent

### TFT Models

#### Primary Training Scripts
- **`train_tft_pytorch.py`**
  - Default path: `'data/historical/btcusd.a'` (via argument)
  - File pattern: `BTCUSD.a_{timeframe}.parquet`
  - Status: ✅ Consistent

- **`train_tft_single.py`**
  - Default path: `'data/historical/btcusd.a'` (via argument)
  - File pattern: `BTCUSD.a_{timeframe}.parquet`
  - Status: ✅ Consistent

- **`train_tft_arima_single.py`**
  - Default path: `'data/historical/btcusd.a'`
  - File pattern: `BTCUSD.a_{timeframe}.parquet`
  - Status: ✅ Consistent

## Issues Found and Fixed

### 1. Batch Script Data Path Inconsistency
**Issue**: `train_models.bat` used `data/combined` instead of `data/historical/btcusd.a`
**Location**: Line 15 in `train_models.bat`
**Fix Applied**: Changed default DATA_DIR to `data/historical/btcusd.a`
**Status**: ✅ Fixed

### 2. Help Text Inconsistency
**Issue**: Help text referenced old data directory
**Location**: Line 218 in `train_models.bat`
**Fix Applied**: Updated help text to reflect correct default path
**Status**: ✅ Fixed

### 3. Configuration Path Variations
**Issue**: Multiple configuration files with different base paths
**Analysis**: 
- `config.json`: `"data_base_path": "data/"`
- `config.example`: `"data_base_path": "data/"`
- `unified_config.py`: `data_dir: str = "data"`
**Status**: ✅ Consistent (all use "data" as base)

## Data Path Mapping by Script

### Training Scripts Data Paths

| Script | Default Data Path | Configurable | File Pattern |
|--------|------------------|--------------|--------------|
| `train_lstm_btcusd.py` | `data/historical/btcusd.a` | ✅ | `BTCUSD.a_{timeframe}.parquet` |
| `train_lstm_single.py` | `data/historical/btcusd.a` | ✅ | `BTCUSD.a_{timeframe}.parquet` |
| `train_arima_single.py` | `data/historical/btcusd.a` | ✅ | `BTCUSD.a_{timeframe}.parquet` |
| `train_tft_pytorch.py` | `data/historical/btcusd.a` | ✅ | `BTCUSD.a_{timeframe}.parquet` |
| `train_tft_single.py` | `data/historical/btcusd.a` | ✅ | `BTCUSD.a_{timeframe}.parquet` |
| `train_tft_arima_single.py` | `data/historical/btcusd.a` | ✅ | `BTCUSD.a_{timeframe}.parquet` |
| `train_model.py` | Uses config manager | ✅ | Configurable |

### Batch Scripts Data Paths

| Script | Default Data Path | Status |
|--------|------------------|---------|
| `train_models.bat` | `data/historical/btcusd.a` | ✅ Fixed |
| `train_all_models.bat` | Uses individual scripts | ✅ Consistent |
| `train_all_arima_models.bat` | Uses individual scripts | ✅ Consistent |
| `train_all_tft_models.sh` | Uses individual scripts | ✅ Consistent |

## Configuration Hierarchy

### 1. Command Line Arguments (Highest Priority)
- `--data-dir` parameter in training scripts
- Overrides all other settings

### 2. Configuration Files
- `config/config.json`: `"data_base_path": "data/"`
- `config/unified_config.py`: Default data directory settings

### 3. Script Defaults (Lowest Priority)
- Hardcoded defaults in individual scripts
- Fallback when no configuration provided

## Recommended Data Organization

### Primary Structure (Current Best Practice)
```
data/
├── historical/
│   └── btcusd.a/
│       ├── BTCUSD.a_M5.parquet
│       ├── BTCUSD.a_M15.parquet
│       ├── BTCUSD.a_M30.parquet
│       ├── BTCUSD.a_H1.parquet
│       └── BTCUSD.a_H4.parquet
├── cache/
├── combined/
├── terminal_1/
├── terminal_2/
├── ...
└── validation/
```

### File Naming Convention
- Pattern: `{SYMBOL}_{TIMEFRAME}.parquet`
- Example: `BTCUSD.a_M5.parquet`
- Format: Parquet (preferred) or CSV

## Data Loading Functions

### Common Data Loading Pattern
```python
def load_data(timeframe: str, data_dir: str) -> Optional[pd.DataFrame]:
    file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
    df = pd.read_parquet(file_path)
    return df
```

### Error Handling
- All scripts include file existence checks
- Graceful error handling with logging
- Fallback mechanisms where appropriate

## Validation Results

### ✅ Consistent Across All Models
1. File naming convention: `BTCUSD.a_{timeframe}.parquet`
2. Default data directory: `data/historical/btcusd.a`
3. Configurable data paths via command line arguments
4. Error handling and logging

### ✅ Fixed Issues
1. Batch script data path inconsistency
2. Help text accuracy
3. Configuration alignment

### ⚠️ Areas for Monitoring
1. Multiple data directories (ensure no confusion)
2. Terminal-specific data organization
3. Cache management and cleanup

## Conclusion

The codebase now has consistent data folder usage across all models:

- **Primary Location**: `data/historical/btcusd.a/`
- **File Format**: Parquet files with consistent naming
- **Configuration**: Fully configurable via command line and config files
- **Error Handling**: Robust error checking and logging

All identified conflicts and inconsistencies have been resolved, ensuring reliable data access for model training and testing.
