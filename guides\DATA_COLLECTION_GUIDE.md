# Data Collection and Verification Guide

This guide provides detailed instructions for collecting, verifying, and preprocessing historical data for the trading bot system.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Data Collection Process](#data-collection-process)
   - [Basic Data Collection](#basic-data-collection)
   - [Advanced Data Collection Options](#advanced-data-collection-options)
   - [Collecting Data for Multiple Timeframes](#collecting-data-for-multiple-timeframes)
   - [Collecting Data from Multiple Terminals](#collecting-data-from-multiple-terminals)
4. [Data Verification](#data-verification)
   - [Checking for Missing Data](#checking-for-missing-data)
   - [Identifying Outliers](#identifying-outliers)
   - [Visualizing Data](#visualizing-data)
5. [Data Preprocessing](#data-preprocessing)
   - [Standard Preprocessing](#standard-preprocessing)
   - [Custom Preprocessing](#custom-preprocessing)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## Overview

High-quality historical data is essential for training accurate prediction models. The trading bot system includes tools for collecting, verifying, and preprocessing data from MetaTrader 5.

## Prerequisites

Before collecting data, ensure you have:

1. **MetaTrader 5 installed and configured**:
   - At least one terminal with active account
   - Automated trading enabled
   - DLL imports enabled

2. **Trading bot environment set up**:
   - Python environment activated
   - Dependencies installed
   - Configuration updated with correct terminal paths

3. **Sufficient disk space**:
   - At least 10GB free space for extensive historical data

## Data Collection Process

### Basic Data Collection

The primary tool for data collection is `collect_data.py`. Here's how to use it for basic data collection:

```bash
python collect_data.py --symbol BTCUSD.a --timeframe M5 --start-date 2018-01-01 --end-date 2023-12-31
```

This command will:
1. Connect to the first available MT5 terminal
2. Request historical data for BTCUSD.a on the M5 timeframe
3. Save the data to `data/historical/btcusd.a/BTCUSD.a_M5.parquet`

### Advanced Data Collection Options

The `collect_data.py` script supports several advanced options:

```bash
python collect_data.py --symbol BTCUSD.a --timeframe M5 --start-date 2018-01-01 --end-date 2023-12-31 --terminal-id 2 --validate --format csv --output-dir custom/data/path
```

Key parameters:
- `--symbol`: Trading symbol (e.g., BTCUSD.a, EURUSD)
- `--timeframe`: Timeframe (M1, M5, M15, M30, H1, H4, D1)
- `--start-date`: Start date for historical data (YYYY-MM-DD)
- `--end-date`: End date for historical data (YYYY-MM-DD)
- `--terminal-id`: Specific terminal to use (default: 1)
- `--validate`: Perform data validation after collection
- `--format`: Output format (parquet, csv)
- `--output-dir`: Custom output directory

### Collecting Data for Multiple Timeframes

To collect data for multiple timeframes in a single command:

```bash
python collect_data.py --symbol BTCUSD.a --timeframes M5,M15,M30,H1,H4 --start-date 2018-01-01 --end-date 2023-12-31
```

This will collect data for all specified timeframes and save them as separate files.

### Collecting Data from Multiple Terminals

To collect data from all configured terminals:

```bash
python collect_data.py --symbol BTCUSD.a --timeframe M5 --start-date 2018-01-01 --end-date 2023-12-31 --all-terminals
```

This will:
1. Connect to each configured terminal
2. Collect data from each terminal
3. Merge and validate the data
4. Save the combined dataset

For the most comprehensive data collection:

```bash
python collect_data.py --symbol BTCUSD.a --timeframes M5,M15,M30,H1,H4 --start-date 2018-01-01 --end-date 2023-12-31 --all-terminals --validate
```

## Data Verification

### Checking for Missing Data

After collecting data, it's important to check for missing values or gaps:

```bash
python utils/data_validator.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --check-missing
```

This will:
1. Load the specified data file
2. Check for missing values in each column
3. Identify gaps in the time series
4. Generate a report of missing data

### Identifying Outliers

To check for outliers in the data:

```bash
python utils/data_validator.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --check-outliers
```

This will:
1. Load the specified data file
2. Apply statistical methods to identify outliers
3. Generate a report of potential outliers

### Visualizing Data

To visualize the collected data:

```bash
python utils/data_visualizer.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet
```

This will generate visualizations including:
1. Price charts (OHLC)
2. Volume charts
3. Distribution plots
4. Correlation matrices

## Data Preprocessing

### Standard Preprocessing

Data preprocessing is handled automatically during model training, but you can also preprocess data separately:

```bash
python utils/data_preprocessor.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --output data/processed/BTCUSD.a_M5_processed.parquet
```

Standard preprocessing includes:
1. Handling missing values
2. Normalizing/scaling features
3. Creating technical indicators
4. Preparing sequences for time series models

### Custom Preprocessing

For custom preprocessing, you can specify additional options:

```bash
python utils/data_preprocessor.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --output data/processed/BTCUSD.a_M5_custom.parquet --add-indicators --sequence-length 60 --target-column close
```

Key parameters:
- `--add-indicators`: Add technical indicators as features
- `--sequence-length`: Length of sequences for time series models
- `--target-column`: Column to use as target variable
- `--scaling-method`: Method for scaling features (standard, minmax, robust)
- `--fill-method`: Method for filling missing values (forward, backward, interpolate)

## Best Practices

1. **Collect Sufficient Historical Data**:
   - At least 2-3 years of historical data for robust model training
   - Include different market conditions (bull, bear, sideways)

2. **Use Multiple Timeframes**:
   - Collect data for multiple timeframes (M5, M15, M30, H1, H4)
   - Models trained on different timeframes can capture different patterns

3. **Validate Data Quality**:
   - Always check for missing values and outliers
   - Visualize data to identify anomalies
   - Compare data from multiple terminals for consistency

4. **Preprocess Data Appropriately**:
   - Use appropriate scaling methods for financial data
   - Create relevant technical indicators
   - Handle missing values carefully

5. **Store Data Efficiently**:
   - Use parquet format for efficient storage and faster loading
   - Organize data by symbol and timeframe
   - Keep raw and processed data separate

## Troubleshooting

### Common Issues

1. **MT5 Connection Issues**:
   - Ensure MetaTrader 5 is running
   - Check terminal paths in configuration
   - Verify login credentials
   - Make sure automated trading is enabled

2. **Missing Data Issues**:
   - Check if the requested date range is available in MT5
   - Verify that the symbol is available in your account
   - Try collecting data in smaller chunks

3. **Data Quality Issues**:
   - Check for gaps during weekends or holidays
   - Verify that volume data is available
   - Check for extreme outliers that might indicate data errors

### Solutions

1. **For MT5 Connection Issues**:
   - Restart MetaTrader 5
   - Check network connectivity
   - Update MT5 to the latest version
   - Verify that your account has access to the requested symbol

2. **For Missing Data Issues**:
   - Use the `--validate` option to identify gaps
   - Fill small gaps using interpolation
   - Collect data from multiple terminals and merge

3. **For Data Quality Issues**:
   - Use the data validation tools to identify problems
   - Apply appropriate preprocessing to handle outliers
   - Compare with alternative data sources if available

---

This guide covers the essential aspects of data collection and verification for the trading bot system. For more information on model training and trading execution, refer to the other guides in this directory.
