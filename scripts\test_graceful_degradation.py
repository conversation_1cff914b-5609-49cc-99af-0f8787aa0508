"""
Test script for the graceful degradation implementation.
"""
import os
import sys
import logging
import time
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_graceful_degradation():
    """Test the graceful degradation implementation."""
    try:
        from utils.graceful_degradation import (
            GracefulDegradation,
            ServiceLevel,
            DegradationReason,
            FeatureToggle,
            is_feature_enabled,
            with_feature,
            execute_if_enabled,
            get_service_level,
            set_service_level,
            check_system_health,
            register_feature,
            register_health_check
        )
        
        logger.info("Testing graceful degradation implementation")
        
        # Get the singleton instance
        degradation = GracefulDegradation.get_instance()
        
        # Test 1: Basic feature registration and checking
        logger.info("Test 1: Basic feature registration and checking")
        
        # Register features with different service levels
        register_feature(FeatureToggle(
            name="core_feature",
            description="Essential core feature",
            minimum_service_level=ServiceLevel.EMERGENCY,
            priority=10
        ))
        
        register_feature(FeatureToggle(
            name="important_feature",
            description="Important but not critical feature",
            minimum_service_level=ServiceLevel.MINIMAL,
            priority=5
        ))
        
        register_feature(FeatureToggle(
            name="standard_feature",
            description="Standard feature",
            minimum_service_level=ServiceLevel.REDUCED,
            priority=3
        ))
        
        register_feature(FeatureToggle(
            name="premium_feature",
            description="Premium feature",
            minimum_service_level=ServiceLevel.FULL,
            priority=1
        ))
        
        # Check features at FULL service level
        assert get_service_level() == ServiceLevel.FULL, "Initial service level should be FULL"
        assert is_feature_enabled("core_feature"), "Core feature should be enabled"
        assert is_feature_enabled("important_feature"), "Important feature should be enabled"
        assert is_feature_enabled("standard_feature"), "Standard feature should be enabled"
        assert is_feature_enabled("premium_feature"), "Premium feature should be enabled"
        
        logger.info("All features enabled at FULL service level")
        
        # Test 2: Service level degradation
        logger.info("Test 2: Service level degradation")
        
        # Degrade to REDUCED
        set_service_level(ServiceLevel.REDUCED, DegradationReason.HIGH_LOAD)
        assert get_service_level() == ServiceLevel.REDUCED, "Service level should be REDUCED"
        
        # Check features at REDUCED service level
        assert is_feature_enabled("core_feature"), "Core feature should be enabled at REDUCED"
        assert is_feature_enabled("important_feature"), "Important feature should be enabled at REDUCED"
        assert is_feature_enabled("standard_feature"), "Standard feature should be enabled at REDUCED"
        assert not is_feature_enabled("premium_feature"), "Premium feature should be disabled at REDUCED"
        
        logger.info("Premium features disabled at REDUCED service level")
        
        # Degrade to MINIMAL
        set_service_level(ServiceLevel.MINIMAL, DegradationReason.EXTERNAL_DEPENDENCY)
        assert get_service_level() == ServiceLevel.MINIMAL, "Service level should be MINIMAL"
        
        # Check features at MINIMAL service level
        assert is_feature_enabled("core_feature"), "Core feature should be enabled at MINIMAL"
        assert is_feature_enabled("important_feature"), "Important feature should be enabled at MINIMAL"
        assert not is_feature_enabled("standard_feature"), "Standard feature should be disabled at MINIMAL"
        assert not is_feature_enabled("premium_feature"), "Premium feature should be disabled at MINIMAL"
        
        logger.info("Standard and premium features disabled at MINIMAL service level")
        
        # Degrade to EMERGENCY
        set_service_level(ServiceLevel.EMERGENCY, DegradationReason.MEMORY_PRESSURE)
        assert get_service_level() == ServiceLevel.EMERGENCY, "Service level should be EMERGENCY"
        
        # Check features at EMERGENCY service level
        assert is_feature_enabled("core_feature"), "Core feature should be enabled at EMERGENCY"
        assert not is_feature_enabled("important_feature"), "Important feature should be disabled at EMERGENCY"
        assert not is_feature_enabled("standard_feature"), "Standard feature should be disabled at EMERGENCY"
        assert not is_feature_enabled("premium_feature"), "Premium feature should be disabled at EMERGENCY"
        
        logger.info("Only core features enabled at EMERGENCY service level")
        
        # Test 3: Feature with condition function
        logger.info("Test 3: Feature with condition function")
        
        # Reset to FULL service level
        set_service_level(ServiceLevel.FULL, DegradationReason.NORMAL)
        
        # Register feature with condition function
        condition_state = {"enabled": True}
        
        def condition_func():
            return condition_state["enabled"]
        
        register_feature(FeatureToggle(
            name="conditional_feature",
            description="Feature with condition function",
            condition_func=condition_func
        ))
        
        # Check feature with condition enabled
        assert is_feature_enabled("conditional_feature"), "Conditional feature should be enabled"
        
        # Change condition to disabled
        condition_state["enabled"] = False
        assert not is_feature_enabled("conditional_feature"), "Conditional feature should be disabled"
        
        logger.info("Conditional feature toggled based on condition function")
        
        # Test 4: Feature with dependencies
        logger.info("Test 4: Feature with dependencies")
        
        register_feature(FeatureToggle(
            name="dependent_feature",
            description="Feature that depends on another feature",
            dependencies=["conditional_feature"]
        ))
        
        # Dependency is disabled, so dependent feature should be disabled
        assert not is_feature_enabled("dependent_feature"), "Dependent feature should be disabled when dependency is disabled"
        
        # Enable dependency
        condition_state["enabled"] = True
        assert is_feature_enabled("dependent_feature"), "Dependent feature should be enabled when dependency is enabled"
        
        logger.info("Dependent feature toggled based on dependency state")
        
        # Test 5: Feature with fallback
        logger.info("Test 5: Feature with fallback")
        
        # Fallback function
        def fallback_func(*args, **kwargs):
            return "Fallback result"
        
        register_feature(FeatureToggle(
            name="fallback_feature",
            description="Feature with fallback function",
            fallback_func=fallback_func
        ))
        
        # Function to execute
        def feature_func():
            return "Feature result"
        
        # Execute with feature enabled
        result = execute_if_enabled("fallback_feature", feature_func)
        assert result == "Feature result", "Should return feature result when enabled"
        
        # Disable feature
        degradation.disable_feature("fallback_feature")
        assert not is_feature_enabled("fallback_feature"), "Feature should be disabled"
        
        # Execute with feature disabled
        result = execute_if_enabled("fallback_feature", feature_func)
        assert result == "Fallback result", "Should return fallback result when disabled"
        
        logger.info("Fallback function used when feature is disabled")
        
        # Test 6: Decorator usage
        logger.info("Test 6: Decorator usage")
        
        # Enable feature for testing decorator
        degradation.enable_feature("fallback_feature")
        
        # Define decorated function
        @with_feature("fallback_feature")
        def decorated_function():
            return "Decorated result"
        
        # Execute with feature enabled
        result = decorated_function()
        assert result == "Decorated result", "Decorated function should execute when feature is enabled"
        
        # Disable feature
        degradation.disable_feature("fallback_feature")
        
        # Execute with feature disabled
        result = decorated_function()
        assert result == "Fallback result", "Decorated function should use fallback when feature is disabled"
        
        logger.info("Decorator correctly gates function execution")
        
        # Test 7: Health checks
        logger.info("Test 7: Health checks")
        
        # Reset to FULL service level
        set_service_level(ServiceLevel.FULL, DegradationReason.NORMAL)
        
        # Health check states
        health_states = {
            "database": True,
            "api": True,
            "cache": True
        }
        
        # Register health checks
        register_health_check("database", lambda: health_states["database"])
        register_health_check("api", lambda: health_states["api"])
        register_health_check("cache", lambda: health_states["cache"])
        
        # All healthy
        assert check_system_health(), "System should be healthy"
        assert get_service_level() == ServiceLevel.FULL, "Service level should be FULL when healthy"
        
        # One failure
        health_states["cache"] = False
        assert not check_system_health(), "System should not be healthy with one failure"
        assert get_service_level() == ServiceLevel.MINIMAL, "Service level should be MINIMAL with one failure"
        
        # Multiple failures
        health_states["api"] = False
        assert not check_system_health(), "System should not be healthy with multiple failures"
        assert get_service_level() == ServiceLevel.EMERGENCY, "Service level should be EMERGENCY with multiple failures"
        
        # Restore health
        health_states["cache"] = True
        health_states["api"] = True
        assert check_system_health(), "System should be healthy after recovery"
        assert get_service_level() == ServiceLevel.FULL, "Service level should be FULL after recovery"
        
        logger.info("Health checks correctly affect service level")
        
        # Test 8: Degradation history
        logger.info("Test 8: Degradation history")
        
        history = degradation.get_degradation_history()
        assert len(history) > 0, "Should have degradation history"
        
        logger.info(f"Degradation history has {len(history)} entries")
        for i, entry in enumerate(history[-3:]):  # Show last 3 entries
            logger.info(f"History entry {i+1}: {entry['old_level']} -> {entry['new_level']} due to {entry['reason']}")
        
        logger.info("Graceful degradation test passed")
        return True
        
    except Exception as e:
        logger.error(f"Error testing graceful degradation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_graceful_degradation():
        logger.info("Graceful degradation test passed")
        sys.exit(0)
    else:
        logger.error("Graceful degradation test failed")
        sys.exit(1)
