"""
Unified configuration manager for the trading bot.
Combines the best aspects of both existing configuration systems.
"""
import os
import json
import logging
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Type
from dataclasses import dataclass, field, asdict

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class MT5TerminalConfig:
    """Configuration for a single MT5 terminal."""
    path: str
    login: Union[str, int]
    password: str
    server: str
    trade_mode: bool = True
    auto_trading: bool = True
    timeout: int = 60000
    retry_interval: int = 5

@dataclass
class MT5Config:
    """Configuration for MT5 connection management."""
    max_connections: int
    timeout: int
    retry_interval: int
    terminals: Dict[str, MT5TerminalConfig]
    auto_start_terminals: bool = True

@dataclass
class StrategyConfig:
    """Configuration for trading strategy."""
    symbol: str
    timeframes: List[str]
    sequence_length: int
    lot_size: float
    max_positions: int
    stop_loss_pips: int
    take_profit_pips: int
    max_spread_pips: int
    risk_per_trade: float
    max_daily_loss: float
    max_daily_trades: int
    cooldown_period: int
    volatility_threshold: float = 2.0
    trend_threshold: float = 0.7
    position_sizing_factor: float = 0.5

@dataclass
class ModelConfig:
    """Configuration for a single model."""
    model_path: str
    input_dim: int
    output_dim: int
    weight: float
    FEATURE_COLUMNS: List[str] = field(default_factory=list)

    # Common parameters
    sequence_length: int = 288
    batch_size: int = 32
    epochs: int = 100
    patience: int = 10
    learning_rate: float = 0.001
    dropout_rate: float = 0.2

    # LSTM specific
    hidden_units: int = 64
    num_layers: int = 2
    dense_units: int = 32

    # TFT specific
    hidden_size: int = 32
    attention_head_size: int = 4
    hidden_continuous_size: int = 16
    max_encoder_length: int = 100
    max_prediction_length: int = 20

    # ARIMA specific
    p: int = 1
    d: int = 1
    q: int = 1
    seasonal_p: int = 0
    seasonal_d: int = 0
    seasonal_q: int = 0
    seasonal_m: int = 0
    use_seasonal: bool = False
    auto_arima: bool = True

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.model_path:
            raise ValueError("Model path cannot be empty")
        if self.input_dim <= 0:
            raise ValueError("Input dimension must be positive")
        if self.output_dim <= 0:
            raise ValueError("Output dimension must be positive")
        if not 0 <= self.weight <= 1:
            raise ValueError("Weight must be between 0 and 1")
        if not self.FEATURE_COLUMNS:
            raise ValueError("Feature columns cannot be empty")

@dataclass
class SystemConfig:
    """System configuration."""
    log_level: str = "INFO"
    log_file: str = "logs/system.log"
    min_memory_gb: int = 4
    environment: str = "development"
    data_dir: str = "data"
    models_dir: str = "models"
    reports_dir: str = "reports"

@dataclass
class MonitoringConfig:
    """Monitoring configuration."""
    report_interval: int = 10
    metrics_interval: int = 5
    plot_interval: int = 10
    save_interval: int = 10
    output_dir: str = "monitoring"
    plots_dir: str = "plots"
    reports_dir: str = "reports"
    max_log_files: int = 10
    max_log_size_mb: int = 100

@dataclass
class TradingConfig:
    """Main trading configuration."""
    mt5: MT5Config
    strategy: StrategyConfig
    models: Dict[str, ModelConfig]
    data_base_path: str
    models_base_path: str
    confidence_threshold: float
    update_interval: int
    max_memory_usage: float
    log_level: str
    debug_mode: bool
    system: SystemConfig = field(default_factory=SystemConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)

class UnifiedConfigManager:
    """
    Unified configuration manager for the trading bot.
    Handles loading, validation, and access to configuration values.
    Properly handles sensitive data and ensures consistent path handling.
    """

    _instance = None
    _config = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(UnifiedConfigManager, cls).__new__(cls)
        return cls._instance

    def __init__(self,
                config_path: Union[str, Path] = 'config/config.json',
                credentials_path: Union[str, Path] = 'config/credentials.py',
                local_config_path: Union[str, Path] = 'config/local_config.json',
                schema_path: Union[str, Path] = 'config/schemas/config.json'):
        """
        Initialize the configuration manager.

        Args:
            config_path: Path to the main configuration file
            credentials_path: Path to the credentials file
            local_config_path: Path to the local configuration file
            schema_path: Path to the schema file for validation
        """
        if self._config is None:
            self.config_path = Path(config_path)
            self.credentials_path = Path(credentials_path)
            self.local_config_path = Path(local_config_path)
            self.schema_path = Path(schema_path)
            self._lock = threading.RLock()

            # Load configuration
            self._load_config()

            # Set up directories
            self._setup_directories()

            logger.info("Unified configuration manager initialized")

    def _load_config(self):
        """Load configuration from files."""
        try:
            # Load main configuration
            with open(self.config_path, 'r') as f:
                config_data = json.load(f)

            # Load local configuration if available
            local_config = {}
            if self.local_config_path.exists():
                try:
                    with open(self.local_config_path, 'r') as f:
                        local_config = json.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load local configuration: {str(e)}")

            # Load credentials if available
            credentials = {}
            if self.credentials_path.exists():
                try:
                    # Import credentials module
                    import importlib.util
                    spec = importlib.util.spec_from_file_location("credentials", self.credentials_path)
                    credentials_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(credentials_module)

                    # Get MT5 terminals from credentials
                    if hasattr(credentials_module, 'MT5_TERMINALS'):
                        credentials['mt5_terminals'] = credentials_module.MT5_TERMINALS
                except Exception as e:
                    logger.warning(f"Failed to load credentials: {str(e)}")

            # Create MT5 terminal configurations
            mt5_terminals = {}

            # First, use terminals from main config
            for terminal_id, terminal_config in config_data['mt5']['terminals'].items():
                mt5_terminals[terminal_id] = MT5TerminalConfig(
                    path=terminal_config['path'],
                    login=terminal_config['login'],
                    password=terminal_config['password'],
                    server=terminal_config['server'],
                    trade_mode=terminal_config.get('trade_mode', True),
                    auto_trading=terminal_config.get('auto_trading', True)
                )

            # Override with terminals from local config if available
            if 'mt5_terminals' in local_config:
                for terminal_id, terminal_config in local_config['mt5_terminals'].items():
                    # Convert terminal_id to string if it's not already
                    terminal_id_str = str(terminal_id)

                    # Create or update terminal config
                    if terminal_id_str in mt5_terminals:
                        # Update existing terminal config
                        for key, value in terminal_config.items():
                            if value:  # Only update if value is not empty
                                setattr(mt5_terminals[terminal_id_str], key, value)
                    else:
                        # Create new terminal config
                        mt5_terminals[terminal_id_str] = MT5TerminalConfig(
                            path=terminal_config['path'],
                            login=terminal_config['login'],
                            password=terminal_config['password'],
                            server=terminal_config['server'],
                            trade_mode=terminal_config.get('trade_mode', True),
                            auto_trading=terminal_config.get('auto_trading', True)
                        )

            # Override with terminals from credentials if available
            if 'mt5_terminals' in credentials:
                for terminal_id, terminal_config in credentials['mt5_terminals'].items():
                    # Convert terminal_id to string if it's not already
                    terminal_id_str = str(terminal_id)

                    # Create or update terminal config
                    if terminal_id_str in mt5_terminals:
                        # Update existing terminal config
                        for key, value in terminal_config.items():
                            if value:  # Only update if value is not empty
                                setattr(mt5_terminals[terminal_id_str], key, value)
                    else:
                        # Create new terminal config
                        mt5_terminals[terminal_id_str] = MT5TerminalConfig(
                            path=terminal_config['path'],
                            login=terminal_config['login'],
                            password=terminal_config['password'],
                            server=terminal_config['server'],
                            trade_mode=terminal_config.get('trade_mode', True),
                            auto_trading=terminal_config.get('auto_trading', True)
                        )

            # Create MT5Config
            mt5_config = MT5Config(
                max_connections=config_data['mt5']['max_connections'],
                timeout=config_data['mt5']['timeout'],
                retry_interval=config_data['mt5']['retry_interval'],
                terminals=mt5_terminals,
                auto_start_terminals=config_data['mt5'].get('auto_start_terminals', True)
            )

            # Create StrategyConfig
            strategy_config = StrategyConfig(
                symbol=config_data['strategy']['symbol'],
                timeframes=config_data['strategy']['timeframes'],
                sequence_length=config_data['strategy']['sequence_length'],
                lot_size=config_data['strategy']['lot_size'],
                max_positions=config_data['strategy']['max_positions'],
                stop_loss_pips=config_data['strategy']['stop_loss_pips'],
                take_profit_pips=config_data['strategy']['take_profit_pips'],
                max_spread_pips=config_data['strategy']['max_spread_pips'],
                risk_per_trade=config_data['strategy']['risk_per_trade'],
                max_daily_loss=config_data['strategy']['max_daily_loss'],
                max_daily_trades=config_data['strategy']['max_daily_trades'],
                cooldown_period=config_data['strategy']['cooldown_period'],
                volatility_threshold=config_data['strategy'].get('volatility_threshold', 2.0),
                trend_threshold=config_data['strategy'].get('trend_threshold', 0.7),
                position_sizing_factor=config_data['strategy'].get('position_sizing_factor', 0.5)
            )

            # Create ModelConfigs
            models_config = {}
            for model_name, model_data in config_data['models'].items():
                # Create model config based on model type
                if model_name.lower() == 'arima':
                    models_config[model_name] = ModelConfig(
                        model_path=model_data['model_path'],
                        input_dim=model_data['input_dim'],
                        output_dim=model_data['output_dim'],
                        weight=model_data['weight'],
                        FEATURE_COLUMNS=model_data.get('FEATURE_COLUMNS', []),
                        sequence_length=model_data.get('sequence_length', 288),
                        # ARIMA specific parameters
                        p=model_data.get('p', 1),
                        d=model_data.get('d', 1),
                        q=model_data.get('q', 1),
                        seasonal_p=model_data.get('seasonal_p', 0),
                        seasonal_d=model_data.get('seasonal_d', 0),
                        seasonal_q=model_data.get('seasonal_q', 0),
                        seasonal_m=model_data.get('seasonal_m', 0),
                        use_seasonal=model_data.get('use_seasonal', False),
                        auto_arima=model_data.get('auto_arima', True)
                    )
                elif model_name.lower() == 'tft':
                    models_config[model_name] = ModelConfig(
                        model_path=model_data['model_path'],
                        input_dim=model_data['input_dim'],
                        output_dim=model_data['output_dim'],
                        weight=model_data['weight'],
                        FEATURE_COLUMNS=model_data.get('FEATURE_COLUMNS', []),
                        sequence_length=model_data.get('sequence_length', 288),
                        batch_size=model_data.get('batch_size', 32),
                        epochs=model_data.get('epochs', 100),
                        patience=model_data.get('patience', 10),
                        learning_rate=model_data.get('learning_rate', 0.001),
                        dropout_rate=model_data.get('dropout_rate', 0.2),
                        # TFT specific parameters
                        hidden_size=model_data.get('hidden_size', 32),
                        attention_head_size=model_data.get('attention_head_size', 4),
                        hidden_continuous_size=model_data.get('hidden_continuous_size', 16),
                        max_encoder_length=model_data.get('max_encoder_length', 100),
                        max_prediction_length=model_data.get('max_prediction_length', 20)
                    )
                else:  # Default to LSTM
                    models_config[model_name] = ModelConfig(
                        model_path=model_data['model_path'],
                        input_dim=model_data['input_dim'],
                        output_dim=model_data['output_dim'],
                        weight=model_data['weight'],
                        FEATURE_COLUMNS=model_data.get('FEATURE_COLUMNS', []),
                        sequence_length=model_data.get('sequence_length', 288),
                        batch_size=model_data.get('batch_size', 32),
                        epochs=model_data.get('epochs', 100),
                        patience=model_data.get('patience', 10),
                        learning_rate=model_data.get('learning_rate', 0.001),
                        dropout_rate=model_data.get('dropout_rate', 0.2),
                        # LSTM specific parameters
                        hidden_units=model_data.get('hidden_units', 64),
                        num_layers=model_data.get('num_layers', 2),
                        dense_units=model_data.get('dense_units', 32)
                    )

            # Create SystemConfig
            system_config = SystemConfig(
                log_level=config_data.get('log_level', 'INFO'),
                log_file=config_data.get('log_file', 'logs/system.log'),
                min_memory_gb=config_data.get('min_memory_gb', 4),
                environment=config_data.get('environment', 'development'),
                data_dir=config_data.get('data_base_path', 'data'),
                models_dir=config_data.get('models_base_path', 'models'),
                reports_dir=config_data.get('reports_dir', 'reports')
            )

            # Create MonitoringConfig
            monitoring_config = MonitoringConfig(
                report_interval=config_data.get('report_interval', 10),
                metrics_interval=config_data.get('metrics_interval', 5),
                plot_interval=config_data.get('plot_interval', 10),
                save_interval=config_data.get('save_interval', 10),
                output_dir=config_data.get('output_dir', 'monitoring'),
                plots_dir=config_data.get('plots_dir', 'plots'),
                reports_dir=config_data.get('reports_dir', 'reports'),
                max_log_files=config_data.get('max_log_files', 10),
                max_log_size_mb=config_data.get('max_log_size_mb', 100)
            )

            # Create TradingConfig
            self._config = TradingConfig(
                mt5=mt5_config,
                strategy=strategy_config,
                models=models_config,
                data_base_path=config_data['data_base_path'],
                models_base_path=config_data['models_base_path'],
                confidence_threshold=config_data['confidence_threshold'],
                update_interval=config_data['update_interval'],
                max_memory_usage=config_data['max_memory_usage'],
                log_level=config_data['log_level'],
                debug_mode=config_data['debug_mode'],
                system=system_config,
                monitoring=monitoring_config
            )

            logger.info("Configuration loaded successfully")

        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            raise

    def _setup_directories(self):
        """Set up required directories."""
        try:
            # Create base directories
            Path(self._config.data_base_path).mkdir(parents=True, exist_ok=True)
            Path(self._config.models_base_path).mkdir(parents=True, exist_ok=True)
            Path('logs').mkdir(parents=True, exist_ok=True)

            # Create system directories
            Path(self._config.system.data_dir).mkdir(parents=True, exist_ok=True)
            Path(self._config.system.models_dir).mkdir(parents=True, exist_ok=True)
            Path(self._config.system.reports_dir).mkdir(parents=True, exist_ok=True)
            Path(os.path.dirname(self._config.system.log_file)).mkdir(parents=True, exist_ok=True)

            # Create monitoring directories
            Path(self._config.monitoring.output_dir).mkdir(parents=True, exist_ok=True)
            Path(self._config.monitoring.output_dir, self._config.monitoring.plots_dir).mkdir(parents=True, exist_ok=True)
            Path(self._config.monitoring.output_dir, self._config.monitoring.reports_dir).mkdir(parents=True, exist_ok=True)

            logger.info("Directories created successfully")

        except Exception as e:
            logger.error(f"Error creating directories: {str(e)}")
            raise

    def get_config(self) -> TradingConfig:
        """Get the main trading configuration."""
        return self._config

    def get_mt5_config(self) -> MT5Config:
        """Get the MT5 configuration."""
        return self._config.mt5

    def get_strategy_config(self) -> StrategyConfig:
        """Get the strategy configuration."""
        return self._config.strategy

    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """Get the configuration for a specific model."""
        return self._config.models.get(model_name)

    def get_all_model_configs(self) -> Dict[str, ModelConfig]:
        """Get all model configurations."""
        return self._config.models

    def get_system_config(self) -> SystemConfig:
        """Get the system configuration."""
        return self._config.system

    def get_monitoring_config(self) -> MonitoringConfig:
        """Get the monitoring configuration."""
        return self._config.monitoring

    def get_data_base_path(self) -> str:
        """Get the base path for data."""
        return self._config.data_base_path

    def get_models_base_path(self) -> str:
        """Get the base path for models."""
        return self._config.models_base_path

    def get_data_path(self, terminal_id=None, timeframe=None) -> Path:
        """Get standardized data path.

        Args:
            terminal_id: Optional terminal ID
            timeframe: Optional timeframe

        Returns:
            Path: Standardized data path
        """
        # Ensure we're using the main data path from config
        path = Path(self._config.data_base_path)

        # Add terminal_id and timeframe if provided
        if terminal_id is not None:
            # Ensure terminal_id is a string
            terminal_id = str(terminal_id)
            path = path / terminal_id
            if timeframe is not None:
                path = path / timeframe

        return path

    def get_model_path(self, model_name=None, terminal_id=None, timeframe=None) -> Path:
        """Get standardized model path.

        Args:
            model_name: Optional model name
            terminal_id: Optional terminal ID
            timeframe: Optional timeframe

        Returns:
            Path: Standardized model path
        """
        # Ensure we're using the main models path from config
        path = Path(self._config.models_base_path)

        # Add terminal_id, timeframe, and model_name if provided
        if terminal_id is not None:
            # Ensure terminal_id is a string
            terminal_id = str(terminal_id)
            path = path / terminal_id
            if timeframe is not None:
                path = path / timeframe
                if model_name is not None:
                    path = path / f"{model_name}_model"
        elif model_name is not None:
            # If only model_name is provided (without terminal context)
            path = path / f"{model_name}_model"

        return path

    def normalize_terminal_id(self, terminal_id) -> str:
        """Ensure terminal ID is consistently a string.

        Args:
            terminal_id: Terminal ID (can be int or str)

        Returns:
            str: Terminal ID as string
        """
        return str(terminal_id)

    def get_model_directory(self) -> str:
        """Get the directory for storing models.

        Returns:
            str: Path to the models directory
        """
        return self._config.models_base_path

    def validate(self) -> bool:
        """Validate the configuration."""
        try:
            # Basic validation
            if not self._config:
                logger.error("Configuration not loaded")
                return False

            # Validate MT5 terminals
            if not self._config.mt5.terminals:
                logger.error("No MT5 terminals configured")
                return False

            # Validate strategy
            if not self._config.strategy.timeframes:
                logger.error("No timeframes configured")
                return False

            # Validate models
            if not self._config.models:
                logger.error("No models configured")
                return False

            # Validate paths
            if not os.path.exists(self._config.data_base_path):
                logger.warning(f"Data base path does not exist: {self._config.data_base_path}")

            if not os.path.exists(self._config.models_base_path):
                logger.warning(f"Models base path does not exist: {self._config.models_base_path}")

            logger.info("Configuration validation successful")
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    def save_config(self, config_path: Optional[Union[str, Path]] = None) -> bool:
        """
        Save the configuration to a file.

        Args:
            config_path: Path to save the configuration to (defaults to self.config_path)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            config_path = Path(config_path) if config_path else self.config_path

            # Convert config to dictionary
            config_dict = asdict(self._config)

            # Remove sensitive data
            if 'mt5' in config_dict and 'terminals' in config_dict['mt5']:
                for terminal_id, terminal in config_dict['mt5']['terminals'].items():
                    terminal['login'] = "YOUR_LOGIN"
                    terminal['password'] = "YOUR_PASSWORD"

            # Save to file
            with open(config_path, 'w') as f:
                json.dump(config_dict, f, indent=2)

            logger.info(f"Configuration saved to {config_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False

    def create_example_config(self) -> bool:
        """
        Create an example configuration file.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            example_path = self.config_path.with_suffix('.example')
            return self.save_config(example_path)

        except Exception as e:
            logger.error(f"Error creating example configuration: {str(e)}")
            return False

# Create global instance
config_manager = UnifiedConfigManager()

# Export configuration getters for convenience
get_config = config_manager.get_config
get_mt5_config = config_manager.get_mt5_config
get_strategy_config = config_manager.get_strategy_config
get_model_config = config_manager.get_model_config
get_all_model_configs = config_manager.get_all_model_configs
get_system_config = config_manager.get_system_config
get_monitoring_config = config_manager.get_monitoring_config
get_data_base_path = config_manager.get_data_base_path
get_models_base_path = config_manager.get_models_base_path
