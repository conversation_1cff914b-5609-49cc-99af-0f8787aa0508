"""
Single MT5 Terminal Status Checker

This script connects to a single MT5 terminal and displays its state and trading balance
without switching accounts, which should preserve Algo Trading.

Usage:
    python single_mt5_status.py --terminal 1
"""

import os
import argparse
import logging
import MetaTrader5 as mt5

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import MT5 credentials from config
from config.credentials import MT5_TERMINALS

def check_terminal_status(terminal_id: int):
    """
    Check and display terminal status and trading balance for a single terminal.
    
    Args:
        terminal_id: ID of the terminal to check (1-5)
    """
    # Get terminal configuration
    if terminal_id not in MT5_TERMINALS:
        logger.error(f"Terminal {terminal_id} not found in configuration")
        return
    
    terminal_config = MT5_TERMINALS[terminal_id]
    
    # Check if terminal path exists
    if not os.path.exists(terminal_config["path"]):
        logger.error(f"Terminal path does not exist: {terminal_config['path']}")
        return
    
    logger.info(f"Checking terminal {terminal_id}")
    logger.info(f"Path: {terminal_config['path']}")
    logger.info(f"Server: {terminal_config['server']}")
    
    # Initialize MT5 with proper parameters
    if not mt5.initialize(
        path=terminal_config["path"],
        login=int(terminal_config["login"]),
        password=terminal_config["password"],
        server=terminal_config["server"],
        portable=True     # Use portable mode to preserve settings
    ):
        error = mt5.last_error()
        logger.error(f"Failed to initialize MT5 for terminal {terminal_id}: {error}")
        return
    
    # Check terminal info
    terminal_info = mt5.terminal_info()
    if not terminal_info:
        logger.error(f"Could not get terminal info for terminal {terminal_id}")
        return
    
    # Check account info
    account_info = mt5.account_info()
    if not account_info:
        logger.error(f"Could not get account info for terminal {terminal_id}")
        return
    
    # Display terminal status
    logger.info("\n=== Terminal Status ===")
    logger.info(f"Terminal Name: {terminal_info.name}")
    logger.info(f"Terminal Path: {terminal_info.path}")
    logger.info(f"Terminal Connected: {terminal_info.connected}")
    
    # Check if Algo Trading is enabled
    if terminal_info.trade_allowed:
        logger.info("Algo Trading: ENABLED ✓")
    else:
        logger.warning("Algo Trading: DISABLED ✗")
        logger.warning("Please enable Algo Trading in the MT5 terminal:")
        logger.warning(f"1. Open the terminal at {terminal_config['path']}")
        logger.warning("2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
        logger.warning("3. Verify that 'Algo Trading enabled' appears in the status bar")
    
    # Display account info
    logger.info("\n=== Account Information ===")
    logger.info(f"Login: {account_info.login}")
    logger.info(f"Server: {account_info.server}")
    logger.info(f"Currency: {account_info.currency}")
    logger.info(f"Balance: {account_info.balance}")
    logger.info(f"Equity: {account_info.equity}")
    logger.info(f"Margin: {account_info.margin}")
    logger.info(f"Margin Free: {account_info.margin_free}")
    
    # Display symbol info for BTCUSD
    symbol = "BTCUSD.a"
    logger.info(f"\n=== Symbol Information for {symbol} ===")
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info:
        logger.info(f"Bid: {symbol_info.bid}")
        logger.info(f"Ask: {symbol_info.ask}")
        logger.info(f"Spread: {symbol_info.spread} points")
    else:
        logger.warning(f"Symbol {symbol} not found")
    
    # DO NOT call mt5.shutdown() as it will disable Algo Trading
    logger.info("\nStatus check completed")

def main():
    """Main function to check a specific terminal."""
    parser = argparse.ArgumentParser(description="Single MT5 Terminal Status Checker")
    parser.add_argument("--terminal", type=int, required=True, help="Terminal ID to check (1-5)")
    args = parser.parse_args()
    
    check_terminal_status(args.terminal)

if __name__ == "__main__":
    main()
