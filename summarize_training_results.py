"""
<PERSON><PERSON><PERSON> to summarize training results for all timeframes.
"""
import json
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def load_training_stats(terminal_id, timeframe):
    """
    Load training statistics for a specific terminal and timeframe.

    Args:
        terminal_id: Terminal ID
        timeframe: Timeframe

    Returns:
        Dictionary with training statistics or None if not found
    """
    # Try different model types
    model_types = ['lstm', 'tft', 'arima']
    for model_type in model_types:
        stats_path = Path(f"training_stats/{terminal_id}/{timeframe}/{model_type}_stats.json")
        if stats_path.exists():
            with open(stats_path, 'r') as f:
                return json.load(f)

    print(f"No training statistics found for terminal {terminal_id}, timeframe {timeframe}")
    return None

def load_test_metrics(terminal_id, timeframe):
    """
    Load test metrics for a specific terminal and timeframe.

    Args:
        terminal_id: Terminal ID
        timeframe: Timeframe

    Returns:
        Dictionary with test metrics or None if not found
    """
    # Try different model types
    model_types = ['lstm', 'tft', 'arima']
    for model_type in model_types:
        metrics_path = Path(f"training_stats/{terminal_id}/{timeframe}/{model_type}_test_metrics.json")
        if metrics_path.exists():
            with open(metrics_path, 'r') as f:
                return json.load(f)

    print(f"No test metrics found for terminal {terminal_id}, timeframe {timeframe}")
    return None

def summarize_results(terminal_id=1):
    """
    Summarize training results for all timeframes.

    Args:
        terminal_id: Terminal ID
    """
    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']

    # Collect results
    results = []

    for timeframe in timeframes:
        stats = load_training_stats(terminal_id, timeframe)
        test_metrics = load_test_metrics(terminal_id, timeframe)

        if stats is None or test_metrics is None:
            continue

        result = {
            'timeframe': timeframe,
            'train_samples': stats.get('train_samples', 0),
            'val_samples': stats.get('val_samples', 0),
            'test_samples': stats.get('test_samples', 0) if 'test_samples' in stats else 0,
            'training_time': stats.get('training_time', 0),
            'train_rmse': stats.get('metrics', {}).get('train_rmse', 0),
            'train_mae': stats.get('metrics', {}).get('train_mae', 0),
            'train_r2': stats.get('metrics', {}).get('train_r2', 0),
            'val_rmse': stats.get('metrics', {}).get('val_rmse', 0),
            'val_mae': stats.get('metrics', {}).get('val_mae', 0),
            'val_r2': stats.get('metrics', {}).get('val_r2', 0),
            'test_rmse': test_metrics.get('test_rmse', 0),
            'test_mae': test_metrics.get('test_mae', 0),
            'test_r2': test_metrics.get('test_r2', 0)
        }

        # Get top 5 features
        if 'feature_importance' in stats:
            sorted_importance = {k: v for k, v in sorted(stats['feature_importance'].items(), key=lambda item: item[1], reverse=True)}
            top_features = list(sorted_importance.items())[:5]

            for i, (feature, importance) in enumerate(top_features):
                result[f'top_feature_{i+1}'] = feature
                result[f'importance_{i+1}'] = importance

        results.append(result)

    # Create DataFrame
    if not results:
        print("No results found")
        return

    df = pd.DataFrame(results)

    # Save to CSV
    df.to_csv('training_summary.csv', index=False)
    print(f"Training summary saved to training_summary.csv")

    # Print summary
    print("\nTraining Summary:")
    print("="*80)
    print(df[['timeframe', 'train_samples', 'training_time', 'train_rmse', 'val_rmse', 'test_rmse', 'train_r2', 'val_r2', 'test_r2']])

    # Create visualizations
    create_visualizations(df)

def create_visualizations(df):
    """
    Create visualizations for training results.

    Args:
        df: DataFrame with training results
    """
    # Create visualizations directory
    vis_dir = Path("visualizations/summary")
    vis_dir.mkdir(parents=True, exist_ok=True)

    # 1. RMSE by timeframe
    plt.figure(figsize=(12, 6))
    df_melted = pd.melt(df, id_vars=['timeframe'], value_vars=['train_rmse', 'val_rmse', 'test_rmse'],
                        var_name='Dataset', value_name='RMSE')
    sns.barplot(x='timeframe', y='RMSE', hue='Dataset', data=df_melted)
    plt.title('RMSE by Timeframe')
    plt.xlabel('Timeframe')
    plt.ylabel('RMSE')
    plt.grid(True, axis='y')
    plt.tight_layout()
    plt.savefig(vis_dir / 'rmse_by_timeframe.png')
    plt.close()

    # 2. R² by timeframe
    plt.figure(figsize=(12, 6))
    df_melted = pd.melt(df, id_vars=['timeframe'], value_vars=['train_r2', 'val_r2', 'test_r2'],
                        var_name='Dataset', value_name='R²')
    sns.barplot(x='timeframe', y='R²', hue='Dataset', data=df_melted)
    plt.title('R² by Timeframe')
    plt.xlabel('Timeframe')
    plt.ylabel('R²')
    plt.grid(True, axis='y')
    plt.tight_layout()
    plt.savefig(vis_dir / 'r2_by_timeframe.png')
    plt.close()

    # 3. Training time by timeframe
    plt.figure(figsize=(10, 6))
    sns.barplot(x='timeframe', y='training_time', data=df)
    plt.title('Training Time by Timeframe')
    plt.xlabel('Timeframe')
    plt.ylabel('Training Time (seconds)')
    plt.grid(True, axis='y')
    plt.tight_layout()
    plt.savefig(vis_dir / 'training_time_by_timeframe.png')
    plt.close()

    # 4. Sample size by timeframe
    plt.figure(figsize=(12, 6))
    df_melted = pd.melt(df, id_vars=['timeframe'], value_vars=['train_samples', 'val_samples', 'test_samples'],
                        var_name='Dataset', value_name='Samples')
    sns.barplot(x='timeframe', y='Samples', hue='Dataset', data=df_melted)
    plt.title('Sample Size by Timeframe')
    plt.xlabel('Timeframe')
    plt.ylabel('Number of Samples')
    plt.grid(True, axis='y')
    plt.tight_layout()
    plt.savefig(vis_dir / 'sample_size_by_timeframe.png')
    plt.close()

if __name__ == "__main__":
    summarize_results()
