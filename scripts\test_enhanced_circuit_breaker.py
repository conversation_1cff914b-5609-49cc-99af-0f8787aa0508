"""
Test script for the enhanced circuit breaker implementation.
"""
import os
import sys
import logging
import time
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_enhanced_circuit_breaker():
    """Test the enhanced circuit breaker implementation."""
    try:
        from utils.enhanced_circuit_breaker import (
            EnhancedCircuitBreaker,
            FailureStrategy,
            RetryStrategy,
            with_circuit_breaker,
            get_circuit_breaker,
            create_circuit_breaker,
            reset_circuit_breaker
        )
        from utils.circuit_breaker import CircuitOpenError, CircuitState

        logger.info("Testing enhanced circuit breaker implementation")

        # Test function that sometimes fails
        def unstable_operation(should_fail=False, fail_with_timeout=False):
            if should_fail:
                if fail_with_timeout:
                    raise TimeoutError("Simulated timeout")
                raise ValueError("Simulated failure")
            return "Success"

        # Fallback function
        def fallback_operation(*args, **kwargs):
            return "Fallback result"

        # Test 1: Basic functionality with count-based strategy
        logger.info("Test 1: Basic functionality with count-based strategy")
        circuit1 = EnhancedCircuitBreaker(
            name="test_count_based",
            failure_threshold=3,
            recovery_timeout=2.0,
            failure_strategy=FailureStrategy.COUNT_BASED
        )

        # Test successful execution
        for i in range(2):
            try:
                result = circuit1.execute(unstable_operation)
                logger.info(f"Execution {i+1} successful: {result}")
            except Exception as e:
                logger.error(f"Execution {i+1} failed: {str(e)}")

        # Test failure threshold
        for i in range(5):
            try:
                result = circuit1.execute(unstable_operation, should_fail=True)
                logger.info(f"Execution {i+1} successful: {result}")
            except CircuitOpenError as e:
                logger.info(f"Circuit open as expected: {str(e)}")
            except Exception as e:
                logger.info(f"Execution {i+1} failed as expected: {str(e)}")

        # Verify circuit is open
        assert circuit1.state == CircuitState.OPEN, "Circuit should be open after failures"
        logger.info(f"Circuit state after failures: {circuit1.state}")

        # Wait for recovery timeout
        logger.info(f"Waiting for recovery timeout ({circuit1.recovery_timeout} seconds)...")
        time.sleep(circuit1.recovery_timeout + 0.5)

        # Verify circuit is half-open
        assert circuit1.state == CircuitState.HALF_OPEN, "Circuit should be half-open after timeout"
        logger.info(f"Circuit state after timeout: {circuit1.state}")

        # Test successful recovery
        try:
            result = circuit1.execute(unstable_operation)
            logger.info(f"Recovery successful: {result}")
            assert circuit1.state == CircuitState.CLOSED, "Circuit should be closed after successful recovery"
        except Exception as e:
            logger.error(f"Recovery failed: {str(e)}")

        # Test 2: Percentage-based strategy
        logger.info("\nTest 2: Percentage-based strategy")
        circuit2 = EnhancedCircuitBreaker(
            name="test_percentage_based",
            failure_strategy=FailureStrategy.PERCENTAGE_BASED,
            failure_percentage_threshold=60.0  # Open after 60% failures
        )

        # Mix of successes and failures
        for i in range(10):
            try:
                # 70% chance of failure to exceed threshold
                should_fail = random.random() < 0.7
                result = circuit2.execute(unstable_operation, should_fail=should_fail)
                logger.info(f"Execution {i+1} successful: {result}")
            except CircuitOpenError as e:
                logger.info(f"Circuit open as expected: {str(e)}")
                break
            except Exception as e:
                logger.info(f"Execution {i+1} failed as expected: {str(e)}")

        # Get enhanced stats
        stats2 = circuit2.get_enhanced_stats()
        logger.info(f"Failure percentage: {stats2.get_failure_percentage():.1f}%")
        logger.info(f"Circuit state: {circuit2.state}")

        # Reset circuit
        circuit2.reset()

        # Test 3: Retry strategy
        logger.info("\nTest 3: Retry strategy")
        circuit3 = EnhancedCircuitBreaker(
            name="test_retry",
            failure_threshold=5,
            retry_strategy=RetryStrategy.EXPONENTIAL,
            max_retries=3
        )

        # Function that fails on first attempts but succeeds later
        attempt_counter = [0]
        def eventually_succeeds():
            attempt_counter[0] += 1
            if attempt_counter[0] <= 2:
                logger.info(f"Attempt {attempt_counter[0]} failing")
                raise ValueError(f"Failing on attempt {attempt_counter[0]}")
            logger.info(f"Attempt {attempt_counter[0]} succeeding")
            return f"Success on attempt {attempt_counter[0]}"

        # Execute with retry
        try:
            result = circuit3.execute(eventually_succeeds)
            logger.info(f"Final result with retry: {result}")
            assert "Success" in result, "Should eventually succeed with retry"
        except Exception as e:
            logger.error(f"Retry failed: {str(e)}")

        # Test 4: Fallback mechanism
        logger.info("\nTest 4: Fallback mechanism")
        circuit4 = EnhancedCircuitBreaker(
            name="test_fallback",
            failure_threshold=2,
            fallback_function=fallback_operation
        )

        # Cause failures to open circuit
        for i in range(3):
            try:
                result = circuit4.execute(unstable_operation, should_fail=True)
                logger.info(f"Execution {i+1} result: {result}")
            except Exception as e:
                logger.info(f"Execution {i+1} error: {str(e)}")

        # Circuit should be open now, but fallback should work
        try:
            result = circuit4.execute(unstable_operation, should_fail=True)
            logger.info(f"Fallback result: {result}")
            assert result == "Fallback result", "Should return fallback result"
        except Exception as e:
            logger.error(f"Fallback failed: {str(e)}")

        # Test 5: Caching
        logger.info("\nTest 5: Result caching")
        call_count = [0]

        def expensive_operation(input_value):
            call_count[0] += 1
            logger.info(f"Expensive operation called {call_count[0]} times")
            time.sleep(0.1)  # Simulate expensive operation
            return f"Result for {input_value} (call {call_count[0]})"

        circuit5 = EnhancedCircuitBreaker(
            name="test_cache",
            cache_results=True,
            cache_ttl=5  # 5 seconds TTL
        )

        # First call should execute the function
        result1 = circuit5.execute(expensive_operation, "test")
        logger.info(f"First call result: {result1}")

        # Second call with same args should use cache
        result2 = circuit5.execute(expensive_operation, "test")
        logger.info(f"Second call result: {result2}")

        # Different args should execute the function again
        result3 = circuit5.execute(expensive_operation, "different")
        logger.info(f"Different args result: {result3}")

        # Verify call count
        assert call_count[0] == 2, f"Function should be called twice, was called {call_count[0]} times"

        # Test 6: Decorator usage
        logger.info("\nTest 6: Decorator usage")

        # Define decorated function
        @with_circuit_breaker(
            name="decorator_test",
            failure_threshold=2,
            retry_strategy=RetryStrategy.LINEAR,
            max_retries=2
        )
        def decorated_function(should_fail=False):
            if should_fail:
                raise ValueError("Decorated function failure")
            return "Decorated success"

        # Test successful execution
        try:
            result = decorated_function()
            logger.info(f"Decorated function result: {result}")
        except Exception as e:
            logger.error(f"Decorated function failed: {str(e)}")

        # Test failure
        try:
            result = decorated_function(should_fail=True)
            logger.info(f"Decorated function result: {result}")
        except Exception as e:
            logger.info(f"Decorated function failed as expected: {str(e)}")

        # Get circuit breaker by name
        decorator_circuit = get_circuit_breaker("decorator_test")
        assert decorator_circuit is not None, "Should be able to retrieve circuit by name"
        logger.info(f"Retrieved circuit stats: {decorator_circuit.get_enhanced_stats()}")

        # Test 7: Registry functions
        logger.info("\nTest 7: Registry functions")

        # Create a new circuit breaker
        new_circuit = create_circuit_breaker(
            name="registry_test",
            failure_threshold=3,
            failure_strategy=FailureStrategy.TIME_BASED
        )

        # Get all circuit breakers
        all_circuits = EnhancedCircuitBreaker.get_all_enhanced()
        logger.info(f"Total circuit breakers: {len(all_circuits)}")

        # Reset a circuit breaker
        reset_result = reset_circuit_breaker("registry_test")
        assert reset_result, "Reset should return True"

        logger.info("Enhanced circuit breaker test passed")
        return True

    except Exception as e:
        logger.error(f"Error testing enhanced circuit breaker: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_enhanced_circuit_breaker():
        logger.info("Enhanced circuit breaker test passed")
        sys.exit(0)
    else:
        logger.error("Enhanced circuit breaker test failed")
        sys.exit(1)
