#!/usr/bin/env python
"""
Cleanup Utility

This script removes redundant files from the project.
"""

import os
import logging
import argparse
from typing import List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Clean up redundant files')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show files to be removed without actually removing them')
    parser.add_argument('--confirm', action='store_true',
                        help='Remove files without confirmation')

    return parser.parse_args()

def get_redundant_files() -> List[str]:
    """
    Get a list of redundant files to remove.

    Returns:
        List of file paths to remove
    """
    # Define patterns of redundant files
    redundant_patterns = [
        # GPU testing scripts
        "check_cuda.py",
        "check_gpu_detailed.py",
        "test_cuda_detailed.py",
        "test_cuda_direct.py",
        "test_gpu.py",
        "test_gpu_support.py",
        "test_tensorflow_gpu.py",
        "test_tensorflow_version.py",

        # Data collection scripts
        "collect_full_history.py",
        "collect_m5_data.py",
        "collect_mt5_data.py",
        "collect_multi_timeframe_history.py",
        "collect_training_data.py",

        # Model training scripts
        "train_all_models.py",
        "train_btcusd_m5.py",
        "train_combined_data.py",
        "train_combined_models.py",
        "train_fixed_models.py",
        "train_lightgbm.py",
        "train_models.py",
        "train_tft_model.py",
        "train_with_pytorch.py",
        "train_with_tensorflow.py",
        "train_xgboost.py",
        "train_xgboost_h1.py",
        "train_xgboost_h4.py",
        "train_xgboost_m15.py",
        "train_xgboost_m30.py",
        "train_xgboost_m5.py",
        "train_xgboost_m5_simple.py",

        # Fix scripts
        "fix_line_333.py",
        "fix_model_input_shape.py",
        "fix_model_shape_mismatch.py",
        "fix_trade_order_class_v2.py",
        "fix_unicode.py",
        "master_fix.py",
        "trade_order_fix.py",
        "trading_bot_fix.py",

        # Removed model files
        "models/gru_model.py",
        "models/xgboost_model.py",
        "models/lightgbm_model.py",
        "models/lstm_model.py",
        "utils/xgboost_trainer.py",
        "utils/lightgbm_trainer.py",
        "utils/gru_trainer.py",
        "utils/lstm_trainer.py"
    ]

    # Find all redundant files
    redundant_files = []
    for pattern in redundant_patterns:
        if os.path.exists(pattern):
            redundant_files.append(pattern)

    return redundant_files

def get_massive_redundant_directories() -> List[str]:
    """
    Get a list of massive redundant directories that are consuming 515GB+ space.

    Returns:
        List of directory paths to remove
    """
    massive_redundant_dirs = [
        # Terminal directories with duplicate 21GB ARIMA models (515GB total)
        "models/1",
        "models/2",
        "models/3",
        "models/4",
        "models/5",

        # Obsolete terminal directories
        "models/terminal_1",
        "models/terminal_2",
        "models/terminal_3",
        "models/terminal_4",
        "models/terminal_5",
        "models/terminal_integration_test",

        # Test and saved directories
        "models/test",
        "models/saved",

        # Large log directories
        "lightning_logs",
        "test_results",

        # Duplicate data directories
        "data/combined",
        "data/cache",
        "data/historical/btcusd.a/terminal1",
        "data/historical/btcusd.a/terminal2",
        "data/historical/btcusd.a/terminal3",
        "data/historical/btcusd.a/terminal4"
    ]

    # Find existing directories
    redundant_dirs = []
    for dir_path in massive_redundant_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            redundant_dirs.append(dir_path)

    return redundant_dirs

def get_duplicate_model_files() -> List[str]:
    """
    Get a list of duplicate model files with wrong naming patterns.

    Returns:
        List of file paths to remove
    """
    duplicate_files = [
        # Duplicate ARIMA files with wrong naming (4GB+ total)
        "models/arima_M5_BTCUSD.a_M5.pkl.pkl",
        "models/arima_M15_BTCUSD.a_M15.pkl.pkl",
        "models/arima_M30_BTCUSD.a_M30.pkl.pkl",
        "models/arima_H1_BTCUSD.a_H1.pkl.pkl",
        "models/arima_H4_BTCUSD.a_H4.pkl.pkl",

        # Duplicate LSTM models with wrong naming
        "models/lstm_M5_BTCUSD.a_M5",
        "models/lstm_M15_BTCUSD.a_M15",
        "models/lstm_M30_BTCUSD.a_M30",
        "models/lstm_H1_BTCUSD.a_H1",
        "models/lstm_H4_BTCUSD.a_H4",

        # Duplicate model directories with wrong naming
        "models/lstm_BTCUSD.a_M5_BTCUSD.a_M5",
        "models/lstm_BTCUSD.a_M15_BTCUSD.a_M15",
        "models/lstm_BTCUSD.a_M30_BTCUSD.a_M30",
        "models/lstm_BTCUSD.a_H1_BTCUSD.a_H1",
        "models/lstm_BTCUSD.a_H4_BTCUSD.a_H4"
    ]

    # Find existing files/directories
    duplicate_items = []
    for item_path in duplicate_files:
        if os.path.exists(item_path):
            duplicate_items.append(item_path)

    return duplicate_items

def calculate_directory_size(path: str) -> int:
    """Calculate total size of directory in bytes."""
    total_size = 0
    try:
        for dirpath, _, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, IOError):
                    pass
    except (OSError, IOError):
        pass
    return total_size

def format_size(size_bytes: int) -> str:
    """Format size in bytes to human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"

def main():
    """Main function."""
    args = parse_args()

    logger.info("🔍 Analyzing folder size and redundancy...")

    # Get all types of redundant items
    redundant_files = get_redundant_files()
    massive_dirs = get_massive_redundant_directories()
    duplicate_files = get_duplicate_model_files()

    # Calculate sizes
    total_redundant_size = 0

    # Calculate size of massive directories
    massive_dirs_size = 0
    for dir_path in massive_dirs:
        if os.path.exists(dir_path):
            dir_size = calculate_directory_size(dir_path)
            massive_dirs_size += dir_size
            logger.info(f"📁 {dir_path}: {format_size(dir_size)}")

    # Calculate size of duplicate files
    duplicate_files_size = 0
    for file_path in duplicate_files:
        if os.path.exists(file_path):
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                duplicate_files_size += file_size
            elif os.path.isdir(file_path):
                dir_size = calculate_directory_size(file_path)
                duplicate_files_size += dir_size

    total_redundant_size = massive_dirs_size + duplicate_files_size

    # Print summary
    logger.info("\n" + "="*60)
    logger.info("📊 REDUNDANCY ANALYSIS SUMMARY")
    logger.info("="*60)
    logger.info(f"🚨 Massive redundant directories: {len(massive_dirs)} ({format_size(massive_dirs_size)})")
    logger.info(f"📄 Duplicate model files: {len(duplicate_files)} ({format_size(duplicate_files_size)})")
    logger.info(f"🗑️  Small redundant files: {len(redundant_files)}")
    logger.info(f"💾 TOTAL SPACE TO RECOVER: {format_size(total_redundant_size)}")
    logger.info("="*60)

    if massive_dirs:
        logger.info("\n🚨 CRITICAL: Massive redundant directories found:")
        for dir_path in massive_dirs:
            if os.path.exists(dir_path):
                size = calculate_directory_size(dir_path)
                logger.info(f"  📁 {dir_path} ({format_size(size)})")

    if duplicate_files:
        logger.info("\n📄 Duplicate model files found:")
        for file_path in duplicate_files:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    logger.info(f"  📄 {file_path} ({format_size(size)})")
                elif os.path.isdir(file_path):
                    size = calculate_directory_size(file_path)
                    logger.info(f"  📁 {file_path} ({format_size(size)})")

    if redundant_files:
        logger.info("\n🗑️ Small redundant files found:")
        for file in redundant_files:
            logger.info(f"  📄 {file}")

    # Confirm removal
    if args.dry_run:
        logger.info("\n🔍 DRY RUN: No files will be removed.")
        return

    if not (massive_dirs or duplicate_files or redundant_files):
        logger.info("✅ No redundant items found.")
        return

    logger.info(f"\n⚠️  WARNING: This will free up {format_size(total_redundant_size)} of space!")

    if not args.confirm:
        confirm = input("\n❓ Do you want to proceed with cleanup? (y/n): ")
        if confirm.lower() != 'y':
            logger.info("❌ Cleanup aborted.")
            return

    # Remove massive directories first (biggest space savings)
    logger.info("\n🗑️ Removing massive redundant directories...")
    for dir_path in massive_dirs:
        try:
            if os.path.exists(dir_path):
                size = calculate_directory_size(dir_path)
                import shutil
                shutil.rmtree(dir_path)
                logger.info(f"✅ Removed directory {dir_path} ({format_size(size)})")
        except Exception as e:
            logger.error(f"❌ Error removing directory {dir_path}: {str(e)}")

    # Remove duplicate files
    logger.info("\n🗑️ Removing duplicate model files...")
    for item_path in duplicate_files:
        try:
            if os.path.exists(item_path):
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path)
                    os.remove(item_path)
                    logger.info(f"✅ Removed file {item_path} ({format_size(size)})")
                elif os.path.isdir(item_path):
                    size = calculate_directory_size(item_path)
                    import shutil
                    shutil.rmtree(item_path)
                    logger.info(f"✅ Removed directory {item_path} ({format_size(size)})")
        except Exception as e:
            logger.error(f"❌ Error removing {item_path}: {str(e)}")

    # Remove small redundant files
    logger.info("\n🗑️ Removing small redundant files...")
    for file in redundant_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                logger.info(f"✅ Removed {file}")
        except Exception as e:
            logger.error(f"❌ Error removing {file}: {str(e)}")

    logger.info(f"\n🎉 Cleanup completed! Freed up approximately {format_size(total_redundant_size)}")
    logger.info("💡 Recommendation: Run model validation to ensure everything still works.")

    logger.info("Cleanup completed.")

if __name__ == "__main__":
    main()
