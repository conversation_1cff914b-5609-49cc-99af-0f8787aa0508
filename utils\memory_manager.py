"""
Memory management utility to monitor and optimize memory usage.
"""

import os
import sys
import time
import psutil
import logging
import gc
import torch
from typing import Dict, Any, Optional
from dataclasses import dataclass
from config.unified_config import config_manager

logger = logging.getLogger(__name__)

@dataclass
class MemoryStats:
    total: float
    used: float
    free: float
    percent: float
    process_memory: float

class MemoryManager:
    def __init__(self, max_memory_percent: float = 85.0, max_memory_mb: Optional[float] = None):
        self.config = config_manager
        self.max_memory_percent = max_memory_percent
        self.max_memory_mb = max_memory_mb
        self.cleanup_threshold = max_memory_percent * 0.9  # Start cleanup at 90% of max

    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics."""
        memory = psutil.virtual_memory()
        process = psutil.Process(os.getpid())

        return MemoryStats(
            total=memory.total / (1024**3),  # Convert to GB
            used=memory.used / (1024**3),
            free=memory.free / (1024**3),
            percent=memory.percent,
            process_memory=process.memory_info().rss / (1024**3)
        )

    def check_memory_usage(self) -> bool:
        """Check if memory usage is within limits."""
        stats = self.get_memory_stats()

        # Check if we should use percentage or absolute value
        if self.max_memory_mb is not None:
            # Convert process memory from GB to MB
            process_memory_mb = stats.process_memory * 1024
            logger.debug(f"Memory usage: {process_memory_mb:.2f} MB, limit: {self.max_memory_mb:.2f} MB")
            return process_memory_mb < self.max_memory_mb
        else:
            # Use percentage
            logger.debug(f"Memory usage: {stats.percent:.2f}%, limit: {self.max_memory_percent:.2f}%")
            return stats.percent < self.max_memory_percent

    def cleanup_memory(self) -> Dict[str, Any]:
        """Perform memory cleanup operations."""
        stats_before = self.get_memory_stats()

        # Clear PyTorch cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # Force garbage collection
        gc.collect()

        # Clear Python cache
        for module in list(sys.modules.values()):
            if hasattr(module, '__dict__'):
                module.__dict__.clear()

        stats_after = self.get_memory_stats()

        return {
            'before': {
                'total': stats_before.total,
                'used': stats_before.used,
                'free': stats_before.free,
                'percent': stats_before.percent,
                'process': stats_before.process_memory
            },
            'after': {
                'total': stats_after.total,
                'used': stats_after.used,
                'free': stats_after.free,
                'percent': stats_after.percent,
                'process': stats_after.process_memory
            },
            'reclaimed': {
                'total': stats_before.used - stats_after.used,
                'process': stats_before.process_memory - stats_after.process_memory
            }
        }

    def monitor_memory(self, interval: int = 60) -> None:
        """Monitor memory usage and perform cleanup if needed."""
        while True:
            stats = self.get_memory_stats()

            if stats.percent > self.cleanup_threshold:
                logger.warning(f"High memory usage detected: {stats.percent}%")
                cleanup_results = self.cleanup_memory()
                logger.info(f"Memory cleanup results: {cleanup_results}")

            time.sleep(interval)

    def optimize_memory(self) -> Dict[str, Any]:
        """Optimize memory usage for the application."""
        # Set PyTorch memory allocation
        if torch.cuda.is_available():
            torch.cuda.set_per_process_memory_fraction(
                self.max_memory_percent / 100,
                torch.cuda.current_device()
            )

        # Set process memory limits
        process = psutil.Process(os.getpid())
        try:
            process.memory_limit(self.max_memory_percent)
        except Exception as e:
            logger.warning(f"Could not set process memory limit: {str(e)}")

        return {
            'max_memory_percent': self.max_memory_percent,
            'cleanup_threshold': self.cleanup_threshold,
            'current_stats': self.get_memory_stats().__dict__
        }

def main():
    """Run memory management checks and optimizations."""
    manager = MemoryManager()

    # Check initial memory state
    initial_stats = manager.get_memory_stats()
    logger.info(f"Initial memory stats: {initial_stats}")

    # Optimize memory settings
    optimization_results = manager.optimize_memory()
    logger.info(f"Memory optimization results: {optimization_results}")

    # Start memory monitoring
    manager.monitor_memory()

if __name__ == "__main__":
    main()