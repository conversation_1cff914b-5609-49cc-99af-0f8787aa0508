# BTCUSD Trading Bot - Trading Strategy Guide

## Introduction

This guide provides a detailed explanation of the trading strategy implemented in the BTCUSD Trading Bot. The strategy combines predictions from multiple machine learning models to generate trading signals and includes sophisticated risk management and position sizing techniques.

## Strategy Overview

The trading strategy in this system follows these key principles:

1. **Multi-Model Ensemble**: Combines predictions from three different ML models (LSTM, TFT, ARIMA)
2. **Multi-Timeframe Analysis**: Analyzes market data across multiple timeframes (M5, M15, M30, H1, D1)
3. **Adaptive Model Weighting**: Dynamically adjusts model weights based on recent performance
4. **Risk-Based Position Sizing**: Determines position size based on account risk parameters
5. **Dynamic Stop-Loss and Take-Profit**: Calculates appropriate stop-loss and take-profit levels based on market volatility

## Signal Generation Process

### Model Ensemble

The core of the trading strategy is the ensemble approach implemented in the `TradingStrategy` class. The ensemble combines predictions from all models using a weighted average:

```python
# Calculate weighted ensemble prediction
ensemble_pred = sum(
    pred * self.model_weights[model_name]
    for model_name, pred in predictions.items()
)
```

Initially, models have weights of LSTM (0.4), TFT (0.4), and ARIMA (0.2), but these weights are dynamically adjusted based on recent performance.

### Model Weight Adjustment

The `update_model_weights()` method adjusts model weights based on performance metrics:

```python
# Calculate total performance score for each model
model_scores = {}
for model_name, metrics in performance_metrics.items():
    # Combine different metrics into a single score
    score = (
        0.4 * (1 - metrics.get('rmse', 0)) +  # Lower RMSE is better
        0.3 * metrics.get('directional_accuracy', 0) +  # Higher accuracy is better
        0.3 * (1 - metrics.get('mae', 0))  # Lower MAE is better
    )
    model_scores[model_name] = score

# Normalize scores to sum to 1
total_score = sum(model_scores.values())
if total_score > 0:
    self.model_weights = {
        model_name: score / total_score
        for model_name, score in model_scores.items()
    }
```

This approach ensures that models with better recent performance have more influence on trading decisions.

### Signal Generation

The `generate_signal()` method converts the ensemble prediction into a trading signal:

```python
def generate_signal(self, prediction: float, data: pd.DataFrame) -> Dict:
    """Generate trading signal based on prediction."""
    try:
        # Get current market data
        current_price = data['close'].iloc[-1]

        # Determine signal direction and strength
        signal_strength = abs(prediction)

        # Default signal (no trade)
        signal = {
            'action': 'HOLD',
            'direction': None,
            'entry_price': None,
            'stop_loss': None,
            'take_profit': None,
            'strength': 0.0
        }

        # Generate buy signal
        if prediction > trading_config.BUY_THRESHOLD:
            signal['action'] = 'BUY'
            signal['direction'] = 'LONG'
            signal['entry_price'] = current_price
            signal['stop_loss'] = self.calculate_stop_loss(current_price, 'LONG')
            signal['take_profit'] = self.calculate_take_profit(current_price, 'LONG')
            signal['strength'] = signal_strength

        # Generate sell signal
        elif prediction < trading_config.SELL_THRESHOLD:
            signal['action'] = 'SELL'
            signal['direction'] = 'SHORT'
            signal['entry_price'] = current_price
            signal['stop_loss'] = self.calculate_stop_loss(current_price, 'SHORT')
            signal['take_profit'] = self.calculate_take_profit(current_price, 'SHORT')
            signal['strength'] = signal_strength

        return signal

    except Exception as e:
        logger.error(f"Error generating signal: {str(e)}")
        # Return default hold signal
        return {
            'action': 'HOLD',
            'direction': None,
            'entry_price': None,
            'stop_loss': None,
            'take_profit': None,
            'strength': 0.0
        }
```

The signal includes:
- Action (BUY, SELL, or HOLD)
- Direction (LONG or SHORT)
- Entry price
- Stop-loss level
- Take-profit level
- Signal strength (based on prediction magnitude)

## Risk Management

### Stop-Loss Calculation

The strategy calculates dynamic stop-loss levels based on market volatility using the Average True Range (ATR) indicator:

```python
def calculate_stop_loss(self, entry_price: float, direction: str) -> float:
    """Calculate stop-loss level based on ATR."""
    try:
        # Get ATR value
        atr_value = self.latest_data['atr'].iloc[-1]

        # Calculate stop-loss distance as multiple of ATR
        sl_distance = atr_value * trading_config.SL_ATR_MULTIPLIER

        # Apply minimum stop-loss distance
        sl_distance = max(sl_distance, trading_config.MIN_SL_DISTANCE)

        # Calculate stop-loss level based on direction
        if direction == 'LONG':
            return entry_price - sl_distance
        else:  # SHORT
            return entry_price + sl_distance

    except Exception as e:
        logger.error(f"Error calculating stop-loss: {str(e)}")
        # Fall back to fixed stop-loss
        if direction == 'LONG':
            return entry_price * (1 - trading_config.FIXED_SL_PERCENTAGE)
        else:  # SHORT
            return entry_price * (1 + trading_config.FIXED_SL_PERCENTAGE)
```

### Take-Profit Calculation

Similarly, take-profit levels are calculated based on the risk-reward ratio and stop-loss distance:

```python
def calculate_take_profit(self, entry_price: float, direction: str) -> float:
    """Calculate take-profit level based on risk-reward ratio."""
    try:
        # Get stop-loss level
        sl_level = self.calculate_stop_loss(entry_price, direction)

        # Calculate stop-loss distance
        sl_distance = abs(entry_price - sl_level)

        # Calculate take-profit distance based on risk-reward ratio
        tp_distance = sl_distance * trading_config.RISK_REWARD_RATIO

        # Calculate take-profit level based on direction
        if direction == 'LONG':
            return entry_price + tp_distance
        else:  # SHORT
            return entry_price - tp_distance

    except Exception as e:
        logger.error(f"Error calculating take-profit: {str(e)}")
        # Fall back to fixed take-profit
        if direction == 'LONG':
            return entry_price * (1 + trading_config.FIXED_TP_PERCENTAGE)
        else:  # SHORT
            return entry_price * (1 - trading_config.FIXED_TP_PERCENTAGE)
```

### Position Sizing

The strategy determines position size based on account risk parameters:

```python
def calculate_position_size(self, entry_price: float, stop_loss: float, account_balance: float) -> float:
    """Calculate position size based on risk parameters."""
    try:
        # Calculate risk amount in account currency
        risk_amount = account_balance * trading_config.RISK_PER_TRADE

        # Calculate risk per pip
        risk_per_pip = abs(entry_price - stop_loss)

        # Calculate position size in lots
        position_size = risk_amount / risk_per_pip / 10  # Divide by 10 to convert to lots

        # Apply minimum and maximum position size constraints
        position_size = max(position_size, trading_config.MIN_LOT_SIZE)
        position_size = min(position_size, trading_config.MAX_LOT_SIZE)

        # Round to nearest 0.01 lot
        position_size = round(position_size, 2)

        return position_size

    except Exception as e:
        logger.error(f"Error calculating position size: {str(e)}")
        # Fall back to fixed lot size
        return trading_config.LOT_SIZE
```

## Multi-Timeframe Analysis

The strategy incorporates data from multiple timeframes to make more informed trading decisions:

### Timeframe Weighting

Different timeframes have different weights in the decision-making process:

```python
TIMEFRAME_WEIGHTS = {
    'M5': 0.15,   # 15% weight for 5-minute timeframe
    'M15': 0.20,  # 20% weight for 15-minute timeframe
    'M30': 0.25,  # 25% weight for 30-minute timeframe
    'H1': 0.30,   # 30% weight for 1-hour timeframe
    'D1': 0.10    # 10% weight for daily timeframe
}
```

### Timeframe Alignment

The strategy checks for alignment across timeframes before executing trades:

```python
def check_timeframe_alignment(self, predictions: Dict[str, Dict[str, float]]) -> bool:
    """Check if predictions across timeframes are aligned."""
    try:
        # Count positive and negative predictions
        positive_count = 0
        negative_count = 0

        # Calculate weighted prediction for each timeframe
        for timeframe, timeframe_preds in predictions.items():
            weighted_pred = sum(
                pred * self.model_weights[model_name]
                for model_name, pred in timeframe_preds.items()
            )

            if weighted_pred > 0:
                positive_count += 1
            elif weighted_pred < 0:
                negative_count += 1

        # Check if majority of timeframes agree
        total_timeframes = len(predictions)
        if positive_count >= total_timeframes * trading_config.ALIGNMENT_THRESHOLD:
            return True, 'LONG'
        elif negative_count >= total_timeframes * trading_config.ALIGNMENT_THRESHOLD:
            return True, 'SHORT'
        else:
            return False, None

    except Exception as e:
        logger.error(f"Error checking timeframe alignment: {str(e)}")
        return False, None
```

## Trade Execution

The `TradingBot._execute_trading_logic()` method handles the execution of trading signals:

```python
def _execute_trading_logic(self, signal: Dict) -> bool:
    """Execute trading logic based on signal."""
    try:
        if not self.is_running:
            return False

        # Get current open positions
        open_positions = self.executor.get_open_positions()

        # Check if we've reached maximum positions
        if len(open_positions) >= trading_config.MAX_POSITIONS:
            logger.info("Maximum number of positions reached")
            return False

        # Check if we've reached maximum daily trades
        today_trades = [trade for trade in self.trade_history
                       if trade['timestamp'].date() == datetime.now().date()]
        if len(today_trades) >= trading_config.MAX_DAILY_TRADES:
            logger.info("Maximum number of daily trades reached")
            return False

        # Check daily loss limit
        today_pnl = sum(trade['profit'] for trade in today_trades)
        if today_pnl <= -trading_config.MAX_DAILY_LOSS:
            logger.info("Maximum daily loss reached")
            return False

        # Execute signal
        if signal['action'] == 'BUY':
            # Check if we already have a LONG position
            existing_long = any(pos['type'] == 'buy' for pos in open_positions)
            if existing_long:
                logger.info("Already have a LONG position")
                return False

            # Open LONG position
            ticket = self.executor.open_position(
                symbol=trading_config.SYMBOL,
                order_type='buy',
                volume=trading_config.LOT_SIZE,
                sl=signal['stop_loss'],
                tp=signal['take_profit'],
                comment=f"ML_ENSEMBLE_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            )

            if ticket > 0:
                logger.info(f"Opened LONG position with ticket {ticket}")
                # Record trade
                self.trade_history.append({
                    'ticket': ticket,
                    'timestamp': datetime.now(),
                    'action': 'BUY',
                    'entry_price': signal['entry_price'],
                    'stop_loss': signal['stop_loss'],
                    'take_profit': signal['take_profit'],
                    'lot_size': trading_config.LOT_SIZE,
                    'profit': 0.0,
                    'status': 'OPEN'
                })
                return True
            else:
                logger.error(f"Failed to open LONG position")
                return False

        elif signal['action'] == 'SELL':
            # Similar logic for SELL signals
            # ...

        return False

    except Exception as e:
        logger.error(f"Error executing trading logic: {str(e)}")
        return False
```

## Position Management

### Trailing Stop-Loss

The strategy implements trailing stop-loss to lock in profits:

```python
def update_trailing_stop_loss(self, position: Dict) -> bool:
    """Update trailing stop-loss for an open position."""
    try:
        # Get current price
        current_price = self.latest_data['close'].iloc[-1]

        # Get position details
        ticket = position['ticket']
        entry_price = position['price_open']
        current_sl = position['sl']
        position_type = position['type']

        # Calculate new stop-loss level
        new_sl = current_sl

        if position_type == 'buy':  # LONG position
            # Calculate potential new stop-loss
            potential_sl = current_price - (self.latest_data['atr'].iloc[-1] * trading_config.TRAILING_SL_ATR_MULTIPLIER)

            # Only move stop-loss up, never down
            if potential_sl > current_sl:
                new_sl = potential_sl

        else:  # SHORT position
            # Calculate potential new stop-loss
            potential_sl = current_price + (self.latest_data['atr'].iloc[-1] * trading_config.TRAILING_SL_ATR_MULTIPLIER)

            # Only move stop-loss down, never up
            if potential_sl < current_sl or current_sl == 0:
                new_sl = potential_sl

        # If stop-loss has changed, update it
        if new_sl != current_sl:
            result = self.executor.modify_position(ticket, sl=new_sl)
            if result:
                logger.info(f"Updated trailing stop-loss for ticket {ticket} to {new_sl}")
                return True
            else:
                logger.error(f"Failed to update trailing stop-loss for ticket {ticket}")
                return False

        return False

    except Exception as e:
        logger.error(f"Error updating trailing stop-loss: {str(e)}")
        return False
```

### Partial Position Closing

The strategy can close positions partially to lock in profits:

```python
def close_partial_position(self, position: Dict, percentage: float) -> bool:
    """Close a percentage of an open position."""
    try:
        # Get position details
        ticket = position['ticket']
        volume = position['volume']

        # Calculate volume to close
        volume_to_close = volume * percentage

        # Ensure minimum volume
        if volume_to_close < trading_config.MIN_LOT_SIZE:
            logger.info(f"Volume to close ({volume_to_close}) is below minimum lot size")
            return False

        # Close partial position
        result = self.executor.close_partial_position(ticket, volume_to_close)
        if result:
            logger.info(f"Closed {percentage*100}% of position with ticket {ticket}")
            return True
        else:
            logger.error(f"Failed to close partial position with ticket {ticket}")
            return False

    except Exception as e:
        logger.error(f"Error closing partial position: {str(e)}")
        return False
```

## Market Condition Analysis

The strategy analyzes market conditions to adapt trading parameters:

### Volatility Assessment

```python
def assess_market_volatility(self) -> str:
    """Assess current market volatility level."""
    try:
        # Get ATR and historical volatility
        current_atr = self.latest_data['atr'].iloc[-1]
        historical_atr = self.latest_data['atr'].mean()

        # Calculate volatility ratio
        volatility_ratio = current_atr / historical_atr

        # Classify volatility
        if volatility_ratio < 0.8:
            return 'LOW'
        elif volatility_ratio > 1.2:
            return 'HIGH'
        else:
            return 'NORMAL'

    except Exception as e:
        logger.error(f"Error assessing market volatility: {str(e)}")
        return 'NORMAL'
```

### Trend Identification

```python
def identify_market_trend(self) -> str:
    """Identify current market trend."""
    try:
        # Get moving averages
        short_ma = self.latest_data['close'].rolling(window=20).mean().iloc[-1]
        long_ma = self.latest_data['close'].rolling(window=50).mean().iloc[-1]

        # Get ADX for trend strength
        adx = self.latest_data['adx'].iloc[-1]

        # Determine trend
        if short_ma > long_ma and adx > 25:
            return 'UPTREND'
        elif short_ma < long_ma and adx > 25:
            return 'DOWNTREND'
        else:
            return 'RANGING'

    except Exception as e:
        logger.error(f"Error identifying market trend: {str(e)}")
        return 'RANGING'
```

## Strategy Adaptation

The strategy adapts to different market conditions:

### Parameter Adjustment

```python
def adjust_parameters_for_market_conditions(self):
    """Adjust trading parameters based on market conditions."""
    try:
        # Assess market conditions
        volatility = self.assess_market_volatility()
        trend = self.identify_market_trend()

        # Adjust stop-loss multiplier based on volatility
        if volatility == 'HIGH':
            self.sl_atr_multiplier = trading_config.SL_ATR_MULTIPLIER * 1.5
        elif volatility == 'LOW':
            self.sl_atr_multiplier = trading_config.SL_ATR_MULTIPLIER * 0.8
        else:
            self.sl_atr_multiplier = trading_config.SL_ATR_MULTIPLIER

        # Adjust risk-reward ratio based on trend
        if trend == 'UPTREND' or trend == 'DOWNTREND':
            self.risk_reward_ratio = trading_config.RISK_REWARD_RATIO * 1.2
        else:  # RANGING
            self.risk_reward_ratio = trading_config.RISK_REWARD_RATIO * 0.8

        # Adjust signal thresholds based on market conditions
        if trend == 'RANGING' and volatility == 'HIGH':
            self.buy_threshold = trading_config.BUY_THRESHOLD * 1.3
            self.sell_threshold = trading_config.SELL_THRESHOLD * 1.3
        else:
            self.buy_threshold = trading_config.BUY_THRESHOLD
            self.sell_threshold = trading_config.SELL_THRESHOLD

        logger.info(f"Adjusted parameters for {volatility} volatility and {trend} market")

    except Exception as e:
        logger.error(f"Error adjusting parameters: {str(e)}")
```

## Performance Tracking

The strategy tracks performance metrics to evaluate effectiveness:

```python
def update_performance_metrics(self):
    """Update strategy performance metrics."""
    try:
        # Calculate basic metrics
        total_trades = len(self.trade_history)
        if total_trades == 0:
            return

        closed_trades = [trade for trade in self.trade_history if trade['status'] == 'CLOSED']
        winning_trades = [trade for trade in closed_trades if trade['profit'] > 0]
        losing_trades = [trade for trade in closed_trades if trade['profit'] <= 0]

        # Calculate win rate
        win_rate = len(winning_trades) / len(closed_trades) if closed_trades else 0

        # Calculate profit factor
        gross_profit = sum(trade['profit'] for trade in winning_trades)
        gross_loss = abs(sum(trade['profit'] for trade in losing_trades))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # Calculate average win and loss
        avg_win = gross_profit / len(winning_trades) if winning_trades else 0
        avg_loss = gross_loss / len(losing_trades) if losing_trades else 0

        # Calculate expectancy
        expectancy = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)

        # Update metrics dictionary
        self.performance_metrics = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'expectancy': expectancy,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'net_profit': gross_profit - gross_loss
        }

        logger.info(f"Updated performance metrics: Win rate: {win_rate:.2f}, Profit factor: {profit_factor:.2f}")

    except Exception as e:
        logger.error(f"Error updating performance metrics: {str(e)}")
```

## Best Practices

### Signal Filtering

- Use signal strength thresholds to filter out weak signals
- Confirm signals across multiple timeframes
- Consider market conditions when evaluating signals
- Avoid trading during major news events

### Risk Management

- Never risk more than 2% of account balance per trade
- Use dynamic stop-loss levels based on market volatility
- Implement trailing stop-loss to lock in profits
- Consider partial position closing to secure profits
- Respect daily loss limits

### Strategy Optimization

- Regularly review and adjust model weights
- Optimize parameters based on recent market conditions
- Backtest parameter changes before implementing
- Monitor performance metrics to identify areas for improvement

## Conclusion

The trading strategy implemented in the BTCUSD Trading Bot combines sophisticated machine learning techniques with sound trading principles. By using an ensemble of models, multi-timeframe analysis, and adaptive risk management, the strategy aims to generate consistent profits while managing risk effectively.

The key to success with this strategy is continuous monitoring and adaptation. Market conditions change over time, and the strategy must adapt accordingly. Regular review of performance metrics and model weights is essential to maintain optimal performance.