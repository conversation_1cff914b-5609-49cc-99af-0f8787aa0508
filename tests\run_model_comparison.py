"""
<PERSON><PERSON>t to run model comparison and testing.
"""
import os
import sys
from pathlib import Path
import logging

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from model_comparison import ModelComparison

def main():
    """Run model comparison."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    try:
        # Define models to compare
        models = ['lstm', 'tft', 'arima']

        # Define data path
        data_path = project_root / "data" / "terminal_1" / "M5_data.csv"

        # Initialize comparison framework
        comparison = ModelComparison(models, data_path)

        # Run comparison
        logger.info("Starting model comparison...")
        comparison.run_comparison()
        logger.info("Model comparison completed successfully")

    except Exception as e:
        logger.error(f"Error in model comparison: {str(e)}")
        raise

if __name__ == "__main__":
    main()