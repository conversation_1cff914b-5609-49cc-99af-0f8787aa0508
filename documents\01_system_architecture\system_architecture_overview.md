# Complete System Architecture Overview

## 🏗️ Executive Summary

This document provides a comprehensive analysis of the entire trading bot system architecture, including all components, services, data structures, and their relationships. Based on systematic codebase review conducted on 2025-06-01, this covers the complete system from data collection to trade execution with real-time performance monitoring.

**Last Updated**: 2025-06-02
**Coverage**: Complete codebase architecture analysis
**Status**: Production-ready system with real-time monitoring
**Training Status**: LSTM ✅ Complete, ARIMA ✅ Complete, TFT 🔄 In Progress
**Monitoring Status**: ✅ Real-time model performance monitoring active with statistical significance testing

## 📊 System Architecture Overview

### 🎯 Core System Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    TRADING BOT SYSTEM ARCHITECTURE              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Data Layer    │  │  Service Layer  │  │ Application     │ │
│  │                 │  │                 │  │ Layer           │ │
│  │ • MT5 Connector │  │ • Model Manager │  │ • Trading Bot   │ │
│  │ • Data Collector│  │ • Thread Manager│  │ • Strategy      │ │
│  │ • Preprocessor  │  │ • Memory Manager│  │ • Executor      │ │
│  │ • Cache Manager │  │ • Error Handler │  │ • Signal Gen    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Model Layer    │  │ Monitoring      │  │ Configuration   │ │
│  │                 │  │ Layer           │  │ Layer           │ │
│  │ • LSTM Models   │  │ • Real-time Mon │  │ • Config Manager│ │
│  │ • ARIMA Models  │  │ • Statistical   │  │ • Credentials   │ │
│  │ • TFT Models    │  │ • Significance  │  │ • Validation    │ │
│  │ • Ensemble      │  │ • Model Compare │  │ • Schemas       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 🔄 Data Flow Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   MT5       │───▶│    Data     │───▶│   Model     │───▶│   Trading   │
│ Terminals   │    │ Collection  │    │ Prediction  │    │ Execution   │
│             │    │ & Process   │    │ & Signals   │    │ & Monitor   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Real-time   │    │ Feature     │    │ Ensemble    │    │ Performance │
│ Market Data │    │ Engineering │    │ Combination │    │ Monitoring  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 🏛️ Component Hierarchy & Relationships

### 📁 Directory Structure Analysis

```
trading-bot/
├── 📁 config/                    # Configuration Management
│   ├── 🔧 unified_config.py      # Main configuration manager
│   ├── 🔧 config.py              # Legacy configuration
│   ├── 🔧 service.py             # Configuration service (backwards compatibility)
│   ├── 📄 config.json            # Main configuration file
│   ├── 📄 credentials.py         # MT5 credentials
│   └── 📁 schemas/               # Configuration schemas
├── 📁 data/                      # Data Storage
│   ├── 📁 historical/            # Historical market data
│   │   └── 📁 btcusd.a/          # BTCUSD data files
│   ├── 📁 cache/                 # Cached processed data
│   └── 📁 training/              # Training datasets
├── 📁 models/                    # Model Storage & Implementation
│   ├── 🧠 base_model.py          # Base model interface
│   ├── 🧠 pytorch_lstm_model.py  # LSTM implementation
│   ├── 🧠 tft_model.py           # TFT implementation
│   ├── 🧠 ensemble_arima_model.py# ARIMA ensemble
│   └── 📁 saved_models/          # Trained model files
├── 📁 trading/                   # Trading Logic
│   ├── 🤖 bot.py                 # Main trading bot
│   ├── 📈 strategy.py            # Trading strategy
│   ├── ⚡ executor.py            # Trade execution
│   └── 📡 signal_generator.py    # Signal generation
├── 📁 utils/                     # Utility Services
│   ├── 🔧 model_manager.py       # Model management
│   ├── 🧵 thread_manager.py      # Thread management
│   ├── 💾 memory_manager.py      # Memory management
│   ├── 🔗 mt5_connection_manager.py # MT5 connections
│   ├── 📊 data_preprocessor.py   # Data preprocessing
│   ├── ⚠️ error_handler.py       # Error handling
│   └── 📈 metrics.py             # Performance metrics
├── 📁 monitoring/                # Monitoring & Visualization
│   ├── 📊 performance.py         # Model performance monitoring
│   ├── 📈 progress.py            # Progress visualization
│   └── 📋 resource_tracker.py    # Resource tracking
├── 📁 monitoring_output/         # Monitoring Output
│   ├── 📁 realtime/              # Real-time analysis results
│   └── 📁 model_performance/     # Model performance reports
├── 🔄 start_model_performance_monitoring.py  # Real-time monitoring system
└── 📁 logs/                      # System Logs
    ├── 📄 system.log             # Main system log
    ├── 📄 trading.log            # Trading activity log
    └── 📄 error.log              # Error log
```

## 🗄️ Core Data Structures

### 1. 📊 Market Data Structure

```python
@dataclass
class MarketData:
    """Standardized market data structure"""
    time: datetime
    open: float
    high: float
    low: float
    close: float
    real_volume: int
    tick_volume: int
    spread: float
    
    # Technical indicators (added by preprocessor)
    sma_5: Optional[float] = None
    sma_10: Optional[float] = None
    sma_20: Optional[float] = None
    rsi: Optional[float] = None
    macd: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
```

### 2. 🤖 Model Configuration Structure

```python
@dataclass
class ModelConfig:
    """Configuration for ML models"""
    model_path: str
    input_dim: int
    output_dim: int
    weight: float
    FEATURE_COLUMNS: List[str]
    
    # Common parameters
    sequence_length: int = 288
    batch_size: int = 32
    epochs: int = 100
    patience: int = 10
    learning_rate: float = 0.001
    dropout_rate: float = 0.2
    
    # LSTM specific
    hidden_units: int = 64
    num_layers: int = 2
    dense_units: int = 32
    
    # TFT specific
    hidden_size: int = 32
    attention_head_size: int = 4
    hidden_continuous_size: int = 16
```

### 3. 📡 Trading Signal Structure

```python
@dataclass
class TradingSignal:
    """Comprehensive trading signal structure"""
    timestamp: datetime
    symbol: str
    timeframe: str
    action: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 to 1.0
    price: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    
    # Model predictions
    model_predictions: Dict[str, float]
    ensemble_prediction: float
    signal_strength: float
    
    # Risk management
    position_size: float
    risk_reward_ratio: float
    max_risk_percent: float
    
    # Metadata
    model_weights: Dict[str, float]
    feature_importance: Dict[str, float]
    market_regime: str
    volatility: float
```

## 🔧 Service Layer Architecture

### 1. 🧠 Model Management Service

The ModelManager provides centralized model management with health monitoring:

- **Model Registry**: Maintains loaded models and their health status
- **Model Classes**: LSTM, TFT, ARIMA model implementations
- **Health Monitoring**: Tracks model performance and availability
- **Weight Management**: Dynamic model weight adjustment based on performance

### 2. 🧵 Thread Management Service

Advanced thread management with priority queues:

- **Thread Pool**: Configurable worker threads (default: CPU count * 5)
- **Priority Queue**: Task prioritization and scheduling
- **Performance Monitoring**: Task completion tracking and metrics
- **Resource Management**: Thread lifecycle and cleanup

### 3. 💾 Memory Management Service

Enhanced memory management with adaptive thresholds:

- **Adaptive Thresholds**: Dynamic memory thresholds based on system capacity
- **Component Tracking**: Per-component memory usage monitoring
- **Progressive Cleanup**: Light, moderate, and aggressive cleanup strategies
- **Real-time Monitoring**: Continuous memory usage tracking

## 📊 Real-Time Model Performance Monitoring

### 1. 📈 Real-Time Monitoring Implementation

**Status**: ✅ **ACTIVE** - Currently monitoring 39+ statistically significant model differences

Real-time model performance monitoring system with:

- **Model Prediction Tracking**: Real-time tracking of model predictions vs actual values
- **Statistical Significance Testing**: T-tests and Wilcoxon signed-rank tests
- **Effect Size Analysis**: Cohen's d calculations for practical significance
- **Model Comparison**: Cross-model performance analysis across timeframes
- **No System Resource Monitoring**: Focus on model performance only (as requested)

### 2. 📊 Statistical Analysis System

**Current Findings**: 39 statistically significant model differences identified

Performance analysis includes:

- **T-Test Analysis**: Statistical significance testing (p < 0.05)
- **Effect Size Calculation**: Large effect sizes detected between models
- **Model Comparison**: LSTM vs ARIMA vs TFT across all timeframes
- **Performance Metrics**: MSE, MAE, R² tracking and comparison
- **Continuous Analysis**: 60-second analysis intervals

### 3. 🔍 Model Performance Tracking

**Key Insights**:
- **LSTM vs ARIMA (H1)**: p-value=0.0004, large effect size
- **ARIMA vs TFT (H1)**: p-value=0.0000, large effect size
- **Multiple timeframes**: Consistent statistical differences

Model tracking features:

- **Prediction Accuracy**: Real-time accuracy monitoring
- **Performance Trends**: Historical performance analysis
- **Model Health**: Model availability and performance status
- **Automated Reporting**: JSON reports every 60 seconds

## ⚠️ Error Handling & Recovery

### 1. 🛡️ Enhanced Error Handler

Comprehensive error handling with circuit breakers:

- **Error Categorization**: Connection, data, model, trading, system errors
- **Circuit Breakers**: Fault tolerance with automatic recovery
- **Recovery Strategies**: Category-specific error recovery mechanisms
- **Error History**: Comprehensive error tracking and analysis

### 2. 🔄 Circuit Breaker Pattern

Fault tolerance implementation:

- **Failure Threshold**: Configurable failure limits
- **Recovery Timeout**: Automatic recovery timing
- **State Management**: Closed, open, half-open states
- **Performance Monitoring**: Success/failure rate tracking

## 🔧 Configuration Management

### 1. 🎛️ Unified Configuration Manager

Centralized configuration management:

- **Multi-file Support**: Main and local configuration files
- **Configuration Validation**: Schema-based validation
- **Dynamic Loading**: Runtime configuration updates
- **Environment Support**: Development/production configurations

This comprehensive architecture provides the foundation for understanding the complete system structure, component relationships, and implementation details. The system is designed with modularity, scalability, and real-time monitoring as core principles.
