"""
Multi-Terminal MT5 Connection

This script provides functionality to connect to multiple MT5 terminals
while preserving Algo Trading. It uses a careful approach to avoid the
"automated trading is disabled because the account has been changed" issue.

Key features:
1. Can connect to all 5 terminals sequentially
2. Preserves Algo Trading status
3. Provides terminal, account, and symbol information
4. Can place orders on any connected terminal
5. Never calls mt5.shutdown() to maintain Algo Trading status
"""

import os
import time
import logging
import argparse
import subprocess
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from typing import Dict, Optional, Any, Tuple, List
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import MT5 credentials from config
from config.credentials import MT5_TERMINALS

class MT5MultiConnector:
    """
    MT5 multi-terminal connector that preserves Algo Trading.
    """

    def __init__(self):
        """Initialize the MT5 multi-terminal connector."""
        self.terminals = {}
        self.current_terminal_id = None
        self.initialized = False

    def get_terminal_id_from_path(self, path: str) -> Optional[int]:
        """
        Get terminal ID from path.

        Args:
            path: Terminal path

        Returns:
            int: Terminal ID or None if not found
        """
        path = os.path.normpath(path).lower()

        for terminal_id, config in MT5_TERMINALS.items():
            config_path = os.path.normpath(os.path.dirname(config["path"])).lower()
            if path == config_path:
                return terminal_id

        return None

    def get_current_terminal(self) -> Optional[int]:
        """
        Get the currently initialized terminal ID.

        Returns:
            int: Terminal ID or None if not initialized
        """
        if not mt5.initialize(portable=True):
            return None

        terminal_info = mt5.terminal_info()
        if not terminal_info:
            return None

        return self.get_terminal_id_from_path(terminal_info.path)

    def count_terminal_processes(self) -> int:
        """
        Count the number of MT5 terminal processes currently running.

        Returns:
            int: Number of terminal processes running
        """
        try:
            # Check for terminal64.exe processes
            process = subprocess.Popen(
                ["tasklist", "/FI", "IMAGENAME eq terminal64.exe", "/FO", "CSV"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            stdout, _ = process.communicate()

            # Count the number of lines containing terminal64.exe (excluding header)
            count = stdout.lower().count('"terminal64.exe"')
            logger.info(f"Found {count} terminal64.exe processes running")
            return count
        except Exception as e:
            logger.error(f"Error counting terminal processes: {str(e)}")
            return 0

    def kill_all_terminal_processes(self) -> bool:
        """
        Kill all MT5 terminal processes.

        Returns:
            bool: True if all terminals were killed, False if there was an error
        """
        try:
            # Kill all terminal64.exe processes
            logger.info("Killing all terminal64.exe processes")
            subprocess.call(["taskkill", "/F", "/IM", "terminal64.exe"])
            time.sleep(3)  # Give them time to terminate

            # Verify they were killed
            count = self.count_terminal_processes()
            if count > 0:
                logger.error(f"Failed to kill all terminal processes, {count} still running")
                return False
            else:
                logger.info("All terminal processes killed successfully")
                return True
        except Exception as e:
            logger.error(f"Error killing terminal processes: {str(e)}")
            return False

    def kill_terminal_process(self, terminal_id: int) -> bool:
        """
        Kill the MT5 terminal process if it's running.

        Args:
            terminal_id: ID of the terminal to kill

        Returns:
            bool: True if terminal was killed or wasn't running, False if there was an error
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal {terminal_id} not found in configuration")
            return False

        terminal_config = MT5_TERMINALS[terminal_id]
        terminal_path = terminal_config["path"]
        terminal_dir = os.path.dirname(terminal_path)
        terminal_name = os.path.basename(terminal_path).lower()

        try:
            # We need to be more specific about which terminal process to kill
            # For now, we'll just kill all terminal processes and let the script restart them
            # This is a temporary solution until we can find a better way to identify specific terminals
            return self.kill_all_terminal_processes()

        except Exception as e:
            logger.error(f"Error killing terminal {terminal_id}: {str(e)}")
            return False

    def ensure_terminal_running(self, terminal_id: int, wait_time: int = 15, force_restart: bool = True) -> bool:
        """
        Ensure the MT5 terminal is running.

        Args:
            terminal_id: ID of the terminal to check
            wait_time: Time to wait for terminal to start in seconds
            force_restart: Whether to force a restart of the terminal

        Returns:
            bool: True if terminal is running, False otherwise
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal {terminal_id} not found in configuration")
            return False

        terminal_config = MT5_TERMINALS[terminal_id]
        terminal_path = terminal_config["path"]
        terminal_dir = os.path.dirname(terminal_path)

        # Check if terminal path exists
        if not os.path.exists(terminal_path):
            logger.error(f"Terminal path does not exist: {terminal_path}")
            return False

        # Get the current number of terminal processes
        initial_count = self.count_terminal_processes()
        logger.info(f"Current terminal process count before starting: {initial_count}")

        # If force_restart is True, kill all terminal processes
        if force_restart:
            logger.info(f"Force restarting terminal {terminal_id}")
            self.kill_all_terminal_processes()
            time.sleep(3)  # Give them time to terminate

            # Verify all terminals are killed
            after_kill_count = self.count_terminal_processes()
            if after_kill_count > 0:
                logger.warning(f"Failed to kill all terminal processes, {after_kill_count} still running")
            else:
                logger.info("All terminal processes killed successfully")

        # Terminal is not running, launch it
        logger.info(f"Launching terminal {terminal_id} from {terminal_path}")
        try:
            # Launch the terminal from its directory to ensure proper startup
            # Use start command to ensure the window is visible
            cmd = f'start "MT5 Terminal {terminal_id}" "{terminal_path}"'
            subprocess.Popen(
                cmd,
                shell=True,
                cwd=terminal_dir  # Set working directory to terminal directory
            )

            # Wait for the terminal to start
            logger.info(f"Waiting {wait_time} seconds for terminal {terminal_id} to start...")

            # Check periodically if the terminal has started
            start_time = time.time()
            while time.time() - start_time < wait_time:
                time.sleep(5)  # Check every 5 seconds
                current_count = self.count_terminal_processes()
                if current_count > initial_count:
                    logger.info(f"Terminal {terminal_id} started successfully")
                    logger.info(f"Terminal process count increased from {initial_count} to {current_count}")
                    # Check if window is visible
                    if self.check_terminal_window_exists(terminal_id):
                        logger.info(f"Terminal {terminal_id} window is visible")
                        return True
                    else:
                        logger.warning(f"Terminal {terminal_id} is running but window is not visible")
                        # Return True even if window is not visible, as long as process is running
                        return True

            # If we get here, the terminal didn't start within the wait time
            logger.error(f"Failed to start terminal {terminal_id} within {wait_time} seconds")
            return False

        except Exception as e:
            logger.error(f"Error launching terminal {terminal_id}: {str(e)}")
            return False

    def get_terminal_window_title(self, terminal_id: int) -> str:
        """
        Get the expected window title for a terminal.

        Args:
            terminal_id: ID of the terminal to check

        Returns:
            str: Expected window title or empty string if unknown
        """
        if terminal_id in [1, 2]:
            return "Pepperstone MetaTrader 5"
        elif terminal_id in [3, 4, 5]:
            return "ICMarkets - MetaTrader 5"
        else:
            logger.error(f"Unknown window title for terminal {terminal_id}")
            return ""

    def check_terminal_window_exists(self, terminal_id: int) -> bool:
        """
        Check if the terminal window exists using window title.

        Args:
            terminal_id: ID of the terminal to check

        Returns:
            bool: True if window exists, False otherwise
        """
        try:
            # Get expected window title
            window_title = self.get_terminal_window_title(terminal_id)

            if not window_title:
                return False

            # Use PowerShell to check if window exists
            cmd = f'powershell "Get-Process | Where-Object {{$_.MainWindowTitle -like \'*{window_title}*\'}} | Select-Object MainWindowTitle"'
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                universal_newlines=True
            )
            stdout, _ = process.communicate()

            if window_title in stdout:
                logger.info(f"Terminal {terminal_id} window found")
                return True
            else:
                logger.warning(f"Terminal {terminal_id} window not found")
                return False

        except Exception as e:
            logger.error(f"Error checking terminal {terminal_id} window: {str(e)}")
            return False

    def ensure_terminal_window_visible(self, terminal_id: int) -> bool:
        """
        Ensure the terminal window is visible.

        Args:
            terminal_id: ID of the terminal to check

        Returns:
            bool: True if window is now visible, False otherwise
        """
        # First check if window is already visible
        if self.check_terminal_window_exists(terminal_id):
            return True

        try:
            # Get terminal config
            terminal_config = MT5_TERMINALS[terminal_id]
            terminal_path = terminal_config["path"]
            terminal_dir = os.path.dirname(terminal_path)

            # Try to make the window visible by launching it again
            logger.info(f"Trying to make Terminal {terminal_id} window visible")

            # Use start command to ensure the window is visible
            cmd = f'start "MT5 Terminal {terminal_id}" "{terminal_path}"'
            subprocess.Popen(
                cmd,
                shell=True,
                cwd=terminal_dir
            )

            # Wait a bit and check again
            time.sleep(5)
            return self.check_terminal_window_exists(terminal_id)

        except Exception as e:
            logger.error(f"Error making terminal {terminal_id} window visible: {str(e)}")
            return False

    def connect_to_all_terminals(self, symbol: str = "BTCUSD.a") -> Dict[int, Dict[str, Any]]:
        """
        Connect to all terminals and check their status.

        This method launches all terminals and checks their status.
        It will also attempt to connect to each terminal to get more information,
        but only if it won't disrupt the current connection.

        Args:
            symbol: Symbol to check for each terminal

        Returns:
            Dict[int, Dict[str, Any]]: Dictionary of terminal IDs and their details
        """
        results = {}
        current_terminal_id = self.get_current_terminal()

        # First, kill all existing terminal processes to start fresh
        logger.info("Killing all existing terminal processes before starting")
        initial_count = self.count_terminal_processes()
        if initial_count > 0:
            logger.info(f"Found {initial_count} terminal processes running, killing them all")
            self.kill_all_terminal_processes()
            time.sleep(3)  # Give them time to terminate

        # Verify all terminals are killed
        after_kill_count = self.count_terminal_processes()
        if after_kill_count > 0:
            logger.warning(f"Failed to kill all terminal processes, {after_kill_count} still running")
        else:
            logger.info("All terminal processes killed successfully")

        # Now start each terminal one by one
        logger.info("Starting all 5 terminals sequentially")
        for terminal_id in MT5_TERMINALS:
            logger.info(f"\n{'=' * 50}")
            logger.info(f"Starting Terminal {terminal_id}")
            logger.info(f"{'=' * 50}")

            # Get terminal config
            terminal_config = MT5_TERMINALS[terminal_id]
            terminal_path = terminal_config["path"]
            terminal_dir = os.path.dirname(terminal_path)

            # Get the current number of terminal processes
            before_count = self.count_terminal_processes()
            logger.info(f"Current terminal process count before starting: {before_count}")

            # Launch the terminal directly
            logger.info(f"Launching terminal {terminal_id} from {terminal_path}")
            try:
                # Use start command with a unique title to ensure the window is visible
                cmd = f'start "MT5 Terminal {terminal_id}" "{terminal_path}"'
                subprocess.Popen(
                    cmd,
                    shell=True,
                    cwd=terminal_dir  # Set working directory to terminal directory
                )

                # Wait for the terminal to start
                logger.info(f"Waiting 30 seconds for terminal {terminal_id} to start...")

                # Check periodically if the terminal has started
                running = False
                start_time = time.time()
                while time.time() - start_time < 30:
                    time.sleep(5)  # Check every 5 seconds
                    current_count = self.count_terminal_processes()
                    if current_count > before_count:
                        logger.info(f"Terminal {terminal_id} started successfully")
                        logger.info(f"Terminal process count increased from {before_count} to {current_count}")
                        running = True
                        break

                if not running:
                    logger.error(f"Failed to start terminal {terminal_id} within 30 seconds")
            except Exception as e:
                logger.error(f"Error launching terminal {terminal_id}: {str(e)}")
                running = False

            # Check if terminal window is visible
            window_visible = False
            if running:
                window_visible = self.check_terminal_window_exists(terminal_id)
                if not window_visible:
                    logger.warning(f"Terminal {terminal_id} is running but window is not visible")

            # Initialize results dictionary
            results[terminal_id] = {
                "running": running,
                "window_visible": window_visible,
                "connected": False,
                "path": terminal_config["path"],
                "login": terminal_config["login"],
                "server": terminal_config["server"],
                "algo_trading": None,
                "account_info": None,
                "symbol_info": None
            }

            if running:
                logger.info(f"Terminal {terminal_id} is running")
                logger.info(f"Window visible: {window_visible}")
                logger.info(f"Path: {terminal_config['path']}")
                logger.info(f"Login: {terminal_config['login']}")
                logger.info(f"Server: {terminal_config['server']}")
            else:
                logger.error(f"Failed to start terminal {terminal_id}")

            # Verify the number of running terminals after each launch
            current_count = self.count_terminal_processes()
            logger.info(f"Current terminal process count: {current_count}")

        # Now, get detailed information for the current terminal
        if current_terminal_id:
            logger.info(f"\n{'=' * 50}")
            logger.info(f"Getting detailed information for Terminal {current_terminal_id}")
            logger.info(f"{'=' * 50}")

            # Get terminal info
            terminal_info = mt5.terminal_info()
            if terminal_info:
                results[current_terminal_id]["connected"] = True
                results[current_terminal_id]["algo_trading"] = terminal_info.trade_allowed

                # Display terminal info
                logger.info("\n=== Terminal Information ===")
                logger.info(f"Terminal ID: {current_terminal_id}")
                logger.info(f"Name: {terminal_info.name}")
                logger.info(f"Path: {terminal_info.path}")
                logger.info(f"Connected: {terminal_info.connected}")
                logger.info(f"Algo Trading: {'ENABLED' if terminal_info.trade_allowed else 'DISABLED'}")

                # Get account info
                account_info = self.display_account_info()
                if account_info:
                    results[current_terminal_id]["account_info"] = account_info

                # Get symbol info
                symbol_info = self.display_symbol_info(symbol)
                if symbol_info:
                    results[current_terminal_id]["symbol_info"] = symbol_info
        else:
            logger.warning("Not connected to any terminal")
            logger.warning("Cannot get detailed information without an active connection")

        return results

    def check_terminal(self, terminal_id: int) -> Tuple[bool, bool]:
        """
        Check if a terminal is running and if Algo Trading is enabled.

        Args:
            terminal_id: ID of the terminal to check

        Returns:
            Tuple[bool, bool]: (terminal_running, algo_trading_enabled)
        """
        # First, ensure the terminal is running
        if not self.ensure_terminal_running(terminal_id):
            return False, False

        # Get the current terminal
        current_terminal_id = self.get_current_terminal()

        # If we're already connected to this terminal, check Algo Trading
        if current_terminal_id == terminal_id:
            terminal_info = mt5.terminal_info()
            if terminal_info:
                return True, terminal_info.trade_allowed

        # We're connected to a different terminal or not connected at all
        # We can't check Algo Trading without risking disabling it
        return True, None

    def display_terminal_info(self, terminal_id: Optional[int] = None) -> bool:
        """
        Display terminal information.

        Args:
            terminal_id: Optional terminal ID to check. If None, checks the current terminal.

        Returns:
            bool: True if Algo Trading is enabled, False otherwise
        """
        if terminal_id is not None:
            # Check if we're already connected to this terminal
            current_terminal_id = self.get_current_terminal()
            if current_terminal_id != terminal_id:
                logger.warning(f"Cannot display info for terminal {terminal_id} without connecting to it")
                logger.warning("This would risk disabling Algo Trading")
                logger.warning(f"Currently connected to terminal {current_terminal_id}")
                return False

        terminal_info = mt5.terminal_info()
        if not terminal_info:
            logger.error("Could not get terminal info")
            return False

        # Get terminal ID from path
        detected_terminal_id = self.get_terminal_id_from_path(terminal_info.path)
        terminal_id_str = f"{detected_terminal_id}" if detected_terminal_id else "Unknown"

        logger.info("\n=== Terminal Information ===")
        logger.info(f"Terminal ID: {terminal_id_str}")
        logger.info(f"Name: {terminal_info.name}")
        logger.info(f"Path: {terminal_info.path}")
        logger.info(f"Connected: {terminal_info.connected}")
        logger.info(f"Algo Trading: {'ENABLED' if terminal_info.trade_allowed else 'DISABLED'}")

        return terminal_info.trade_allowed

    def display_account_info(self) -> Optional[Dict[str, Any]]:
        """
        Display account information.

        Returns:
            Optional[Dict[str, Any]]: Account information or None if not available
        """
        account_info = mt5.account_info()
        if not account_info:
            logger.error("Could not get account info")
            return None

        logger.info("\n=== Account Information ===")
        logger.info(f"Login: {account_info.login}")
        logger.info(f"Server: {account_info.server}")
        logger.info(f"Currency: {account_info.currency}")
        logger.info(f"Balance: {account_info.balance}")
        logger.info(f"Equity: {account_info.equity}")
        logger.info(f"Margin: {account_info.margin}")
        logger.info(f"Margin Free: {account_info.margin_free}")
        logger.info(f"Margin Level: {account_info.margin_level}%")
        logger.info(f"Leverage: 1:{account_info.leverage}")

        # Convert to dictionary for easier use
        account_dict = {
            "login": account_info.login,
            "server": account_info.server,
            "currency": account_info.currency,
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "margin_free": account_info.margin_free,
            "margin_level": account_info.margin_level,
            "leverage": account_info.leverage
        }

        return account_dict

    def display_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Display comprehensive symbol information.

        Args:
            symbol: Symbol to get information for (e.g., "EURUSD", "BTCUSD.a")

        Returns:
            Optional[Dict[str, Any]]: Symbol information or None if not available
        """
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            logger.warning(f"Symbol {symbol} not found")
            return None

        # Get the latest tick for more accurate spread calculation
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            real_spread = round((tick.ask - tick.bid) / symbol_info.point)
        else:
            real_spread = symbol_info.spread

        # Calculate spread in pips and dollars
        spread_in_pips = real_spread / 10  # Convert points to pips
        spread_in_dollars = real_spread * symbol_info.trade_tick_value

        logger.info(f"\n=== Symbol Information for {symbol} ===")
        logger.info(f"Bid: {symbol_info.bid}")
        logger.info(f"Ask: {symbol_info.ask}")
        logger.info(f"Last: {tick.last if tick else 'N/A'}")
        logger.info(f"Spread: {real_spread} points ({spread_in_pips:.1f} pips, ${spread_in_dollars:.2f})")
        logger.info(f"Digits: {symbol_info.digits}")
        logger.info(f"Point: {symbol_info.point}")

        # Trading specifications
        logger.info(f"\n--- Trading Specifications ---")
        logger.info(f"Volume Min: {symbol_info.volume_min}")
        logger.info(f"Volume Max: {symbol_info.volume_max}")
        logger.info(f"Volume Step: {symbol_info.volume_step}")
        logger.info(f"Contract Size: {symbol_info.trade_contract_size}")
        logger.info(f"Tick Value: {symbol_info.trade_tick_value}")
        logger.info(f"Tick Size: {symbol_info.trade_tick_size}")

        # Trading hours and session info
        logger.info(f"\n--- Trading Sessions ---")
        logger.info(f"Trade Mode: {symbol_info.trade_mode}")
        logger.info(f"Trade Time: {symbol_info.time}")
        logger.info(f"Session Deals: {symbol_info.session_deals}")
        logger.info(f"Session Volume: {symbol_info.session_volume}")
        logger.info(f"Session Turnover: {symbol_info.session_turnover}")

        # Convert to dictionary for easier use
        symbol_dict = {
            "name": symbol_info.name,
            "bid": symbol_info.bid,
            "ask": symbol_info.ask,
            "last": tick.last if tick else None,
            "spread": real_spread,
            "spread_pips": spread_in_pips,
            "spread_dollars": spread_in_dollars,
            "digits": symbol_info.digits,
            "point": symbol_info.point,
            "volume_min": symbol_info.volume_min,
            "volume_max": symbol_info.volume_max,
            "volume_step": symbol_info.volume_step,
            "contract_size": symbol_info.trade_contract_size,
            "tick_value": symbol_info.trade_tick_value,
            "tick_size": symbol_info.trade_tick_size,
            "trade_mode": symbol_info.trade_mode,
            "session_deals": symbol_info.session_deals,
            "session_volume": symbol_info.session_volume,
            "session_turnover": symbol_info.session_turnover
        }

        return symbol_dict

    def connect(self, terminal_id: int) -> bool:
        """
        Connect to a specific terminal.

        Args:
            terminal_id: ID of the terminal to connect to

        Returns:
            bool: True if connected successfully, False otherwise
        """
        if terminal_id not in MT5_TERMINALS:
            logger.error(f"Terminal {terminal_id} not found in configuration")
            return False

        # Ensure terminal is running
        if not self.ensure_terminal_running(terminal_id):
            logger.error(f"Terminal {terminal_id} is not running")
            return False

        # Get the current terminal
        current_terminal_id = self.get_current_terminal()

        # If we're already connected to this terminal, we're done
        if current_terminal_id == terminal_id:
            logger.info(f"Already connected to terminal {terminal_id}")
            self.current_terminal_id = terminal_id
            return True

        # We're connected to a different terminal or not connected at all
        # We need to initialize the connection to the new terminal
        terminal_config = MT5_TERMINALS[terminal_id]

        # Initialize MT5 with minimal parameters
        logger.info(f"Connecting to terminal {terminal_id} with minimal parameters")
        if not mt5.initialize(
            path=terminal_config["path"],
            portable=True  # CRITICAL: Must be True to preserve algorithmic trading
        ):
            logger.error(f"Failed to connect to terminal {terminal_id}: {mt5.last_error()}")
            return False

        # Verify connection
        terminal_info = mt5.terminal_info()
        if not terminal_info:
            logger.error(f"Failed to get terminal info for terminal {terminal_id}")
            return False

        # Update current terminal ID
        self.current_terminal_id = terminal_id
        logger.info(f"Connected to terminal {terminal_id}")

        return True

    def is_connected(self) -> bool:
        """
        Check if connected to any terminal.

        Returns:
            bool: True if connected, False otherwise
        """
        return mt5.initialize(portable=True)

    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """
        Get account information.

        Returns:
            Optional[Dict[str, Any]]: Account information or None if not available
        """
        account_info = mt5.account_info()
        if not account_info:
            logger.error("Could not get account info")
            return None

        # Convert to dictionary for easier use
        account_dict = {
            "login": account_info.login,
            "server": account_info.server,
            "currency": account_info.currency,
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "margin_free": account_info.margin_free,
            "margin_level": account_info.margin_level,
            "leverage": account_info.leverage
        }

        return account_dict

    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get symbol information.

        Args:
            symbol: Symbol to get information for (e.g., "EURUSD", "BTCUSD.a")

        Returns:
            Optional[Dict[str, Any]]: Symbol information or None if not available
        """
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            logger.warning(f"Symbol {symbol} not found")
            return None

        # Get the latest tick for more accurate spread calculation
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            real_spread = round((tick.ask - tick.bid) / symbol_info.point)
        else:
            real_spread = symbol_info.spread

        # Calculate spread in pips and dollars
        spread_in_pips = real_spread / 10  # Convert points to pips
        spread_in_dollars = real_spread * symbol_info.trade_tick_value

        # Convert to dictionary for easier use
        symbol_dict = {
            "name": symbol_info.name,
            "bid": symbol_info.bid,
            "ask": symbol_info.ask,
            "last": tick.last if tick else None,
            "spread": real_spread,
            "spread_pips": spread_in_pips,
            "spread_dollars": spread_in_dollars,
            "digits": symbol_info.digits,
            "point": symbol_info.point,
            "point_value": symbol_info.trade_tick_value / symbol_info.trade_tick_size,
            "volume_min": symbol_info.volume_min,
            "volume_max": symbol_info.volume_max,
            "volume_step": symbol_info.volume_step,
            "contract_size": symbol_info.trade_contract_size,
            "tick_value": symbol_info.trade_tick_value,
            "tick_size": symbol_info.trade_tick_size
        }

        return symbol_dict

    def get_historical_data(self, symbol: str, timeframe: str, bars: int = 500) -> Optional[pd.DataFrame]:
        """
        Get historical data for a symbol.

        Args:
            symbol: Symbol to get data for (e.g., "EURUSD", "BTCUSD.a")
            timeframe: Timeframe (e.g., "M5", "H1")
            bars: Number of bars to get

        Returns:
            Optional[pd.DataFrame]: Historical data or None if not available
        """
        # Convert timeframe string to MT5 timeframe
        timeframe_map = {
            "M1": mt5.TIMEFRAME_M1,
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            "D1": mt5.TIMEFRAME_D1,
            "W1": mt5.TIMEFRAME_W1,
            "MN1": mt5.TIMEFRAME_MN1
        }

        if timeframe not in timeframe_map:
            logger.error(f"Invalid timeframe: {timeframe}")
            return None

        mt5_timeframe = timeframe_map[timeframe]

        # Get historical data
        rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, bars)
        if rates is None or len(rates) == 0:
            logger.error(f"Failed to get historical data for {symbol} {timeframe}")
            return None

        # Convert to DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)

        return df

    def get_positions(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get open positions.

        Args:
            symbol: Optional symbol to filter positions

        Returns:
            List[Dict[str, Any]]: List of open positions
        """
        if symbol:
            positions = mt5.positions_get(symbol=symbol)
        else:
            positions = mt5.positions_get()

        if positions is None or len(positions) == 0:
            return []

        # Convert to list of dictionaries
        positions_list = []
        for position in positions:
            position_dict = {
                "ticket": position.ticket,
                "symbol": position.symbol,
                "type": "BUY" if position.type == mt5.POSITION_TYPE_BUY else "SELL",
                "volume": position.volume,
                "open_price": position.price_open,
                "current_price": position.price_current,
                "sl": position.sl,
                "tp": position.tp,
                "profit": position.profit,
                "comment": position.comment
            }
            positions_list.append(position_dict)

        return positions_list

    def get_history_orders(self, symbol: Optional[str] = None, days: int = 7) -> List[Dict[str, Any]]:
        """
        Get history orders.

        Args:
            symbol: Optional symbol to filter orders
            days: Number of days to look back

        Returns:
            List[Dict[str, Any]]: List of history orders
        """
        from_date = datetime.now() - timedelta(days=days)
        to_date = datetime.now()

        if symbol:
            orders = mt5.history_orders_get(from_date, to_date, symbol=symbol)
        else:
            orders = mt5.history_orders_get(from_date, to_date)

        if orders is None or len(orders) == 0:
            return []

        # Convert to list of dictionaries
        orders_list = []
        for order in orders:
            order_dict = {
                "ticket": order.ticket,
                "symbol": order.symbol,
                "type": "BUY" if order.type == mt5.ORDER_TYPE_BUY else "SELL",
                "volume": order.volume_initial,
                "open_price": order.price_open,
                "close_price": order.price_current,
                "sl": order.sl,
                "tp": order.tp,
                "profit": order.profit,
                "comment": order.comment,
                "open_time": datetime.fromtimestamp(order.time_setup),
                "close_time": datetime.fromtimestamp(order.time_done) if order.time_done > 0 else None
            }
            orders_list.append(order_dict)

        return orders_list

    def place_order(self, symbol: str, order_type: str, volume: float,
                   stop_loss: float = 0.0, take_profit: float = 0.0,
                   price: float = 0.0, comment: str = "") -> Optional[int]:
        """
        Place an order.

        Args:
            symbol: Symbol to trade (e.g., "EURUSD", "BTCUSD.a")
            order_type: Order type ("BUY" or "SELL")
            volume: Order volume
            price: Order price (0 for market orders)
            sl: Stop loss price
            tp: Take profit price
            comment: Order comment

        Returns:
            bool: True if order placed successfully, False otherwise
        """
        # Check if Algo Trading is enabled
        terminal_info = mt5.terminal_info()
        if not terminal_info or not terminal_info.trade_allowed:
            logger.error("Cannot place order: Algo Trading is disabled")
            return False

        # Get symbol info
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            logger.warning(f"Symbol {symbol} not found")
            return False

        # Prepare order request
        if order_type.upper() == "BUY":
            mt5_order_type = mt5.ORDER_TYPE_BUY
            if price == 0.0:
                price = mt5.symbol_info_tick(symbol).ask
        elif order_type.upper() == "SELL":
            mt5_order_type = mt5.ORDER_TYPE_SELL
            if price == 0.0:
                price = mt5.symbol_info_tick(symbol).bid
        else:
            logger.error(f"Invalid order type: {order_type}")
            return None

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": mt5_order_type,
            "price": price,
            "sl": stop_loss,
            "tp": take_profit,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC
        }

        # Send order
        logger.info(f"Placing order: {order_type} {volume} {symbol} @ {price}")
        result = mt5.order_send(request)
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.error(f"Order failed: {result.retcode}, {result.comment}")
            return None

        logger.info(f"Order placed successfully: {result.order}")
        return result.order

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Multi-Terminal MT5 Connection")
    parser.add_argument("--terminal", type=int, help="Terminal ID to check (1-5)")
    parser.add_argument("--all", action="store_true", help="Check all terminals")
    parser.add_argument("--symbol", type=str, default="BTCUSD.a", help="Symbol to check")
    parser.add_argument("--order", action="store_true", help="Place a test order")
    parser.add_argument("--type", type=str, default="BUY", help="Order type (BUY or SELL)")
    parser.add_argument("--volume", type=float, default=0.01, help="Order volume")
    args = parser.parse_args()

    # Create MT5 multi-connector
    connector = MT5MultiConnector()

    if args.all:
        # Start all terminals and get their status
        logger.info("Starting all 5 MT5 terminals and checking their status")
        logger.info("This will kill any existing terminal processes and start them fresh")
        terminal_details = connector.connect_to_all_terminals(args.symbol)

        # Display summary
        logger.info("\n=== Terminal Status Summary ===")
        running_count = sum(1 for details in terminal_details.values() if details["running"])
        visible_count = sum(1 for details in terminal_details.values() if details["window_visible"])
        connected_count = sum(1 for details in terminal_details.values() if details["connected"])

        logger.info(f"Total terminals: {len(terminal_details)}")
        logger.info(f"Running terminals: {running_count}")
        logger.info(f"Visible terminals: {visible_count}")
        logger.info(f"Connected terminals: {connected_count}")

        # Add note about window visibility
        if running_count > 0 and visible_count == 0:
            logger.info("\nNote: Terminals are running but windows are not visible.")
            logger.info("This is a system-specific issue and does not affect the functionality.")
            logger.info("The terminals are still running in the background and can be used for trading.")

        # Display detailed status for each terminal
        for terminal_id, details in terminal_details.items():
            status = "✓ Running" if details["running"] else "✗ Not Running"
            visible = "✓ Visible" if details["window_visible"] else "✗ Not Visible"
            connected = "✓ Connected" if details["connected"] else "✗ Not Connected"
            algo_trading = "✓ Enabled" if details["algo_trading"] else "✗ Disabled" if details["algo_trading"] is not None else "? Unknown"

            logger.info(f"\nTerminal {terminal_id}: {status}, {visible}, {connected}, Algo Trading: {algo_trading}")
            logger.info(f"Login: {details['login']}, Server: {details['server']}")

            # Display account info if available
            if details["account_info"]:
                account = details["account_info"]
                logger.info(f"Balance: {account['balance']}, Equity: {account['equity']}")

            # Display symbol info if available
            if details["symbol_info"]:
                symbol_info = details["symbol_info"]
                logger.info(f"Symbol: {args.symbol}, Bid: {symbol_info['bid']}, Ask: {symbol_info['ask']}")
                logger.info(f"Spread: {symbol_info['spread']} points ({symbol_info['spread_pips']:.1f} pips, ${symbol_info['spread_dollars']:.2f})")

        # Get the current terminal
        current_terminal_id = connector.get_current_terminal()
        if current_terminal_id:
            logger.info(f"\nCurrently connected to terminal {current_terminal_id}")

            # Place a test order if requested
            if args.order and terminal_details[current_terminal_id]["algo_trading"]:
                logger.info("\n=== Placing Order ===")
                order_ticket = connector.place_order(args.symbol, args.type, args.volume, comment="Test order")
                if order_ticket:
                    logger.info(f"Order placed successfully: Ticket {order_ticket}")
                else:
                    logger.error("Failed to place order")
        else:
            logger.warning("\nNot connected to any terminal")
            logger.warning("Please restart the script to connect to a terminal")

    elif args.terminal:
        # Check specific terminal
        logger.info(f"Checking terminal {args.terminal}")

        # Ensure terminal is running
        running, _ = connector.check_terminal(args.terminal)

        if running:
            logger.info(f"Terminal {args.terminal} is running")

            # Get the current terminal
            current_terminal_id = connector.get_current_terminal()

            if current_terminal_id == args.terminal:
                logger.info(f"Connected to terminal {args.terminal}")

                # Display terminal info
                algo_trading_enabled = connector.display_terminal_info()

                # Display account info
                connector.display_account_info()

                # Display symbol info
                connector.display_symbol_info(args.symbol)

                # Place a test order if requested
                if args.order and algo_trading_enabled:
                    logger.info("\n=== Placing Order ===")
                    order_ticket = connector.place_order(args.symbol, args.type, args.volume, comment="Test order")
                    if order_ticket:
                        logger.info(f"Order placed successfully: Ticket {order_ticket}")
                    else:
                        logger.error("Failed to place order")
            else:
                logger.warning(f"Connected to terminal {current_terminal_id}, not terminal {args.terminal}")
                logger.warning("Cannot switch terminals without risking disabling Algo Trading")
                logger.warning("Please restart the script with the terminal already initialized")
        else:
            logger.error(f"Terminal {args.terminal} is not running")

    else:
        # No terminal specified, check current terminal
        current_terminal_id = connector.get_current_terminal()

        if current_terminal_id:
            logger.info(f"Currently connected to terminal {current_terminal_id}")

            # Display terminal info
            algo_trading_enabled = connector.display_terminal_info()

            # Display account info
            connector.display_account_info()

            # Display symbol info
            connector.display_symbol_info(args.symbol)

            # Place a test order if requested
            if args.order and algo_trading_enabled:
                logger.info("\n=== Placing Order ===")
                order_ticket = connector.place_order(args.symbol, args.type, args.volume, comment="Test order")
                if order_ticket:
                    logger.info(f"Order placed successfully: Ticket {order_ticket}")
                else:
                    logger.error("Failed to place order")
        else:
            logger.warning("Not connected to any terminal")
            logger.warning("Please specify a terminal with --terminal or --all")

    # Keep the connection open
    logger.info("\nKeeping MT5 connection open to preserve Algo Trading")
    logger.info("You can now use this connection for other operations")

    # Add note about checking terminals manually
    if args.all:
        logger.info("\nTo check the terminals manually:")
        logger.info("1. Open Task Manager and verify that terminal64.exe processes are running")
        logger.info("2. You may need to manually open the terminal windows from their installation directories")
        logger.info("3. Terminal paths:")
        for terminal_id in MT5_TERMINALS:
            terminal_path = MT5_TERMINALS[terminal_id]["path"]
            logger.info(f"   - Terminal {terminal_id}: {terminal_path}")

if __name__ == "__main__":
    main()
