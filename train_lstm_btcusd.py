#!/usr/bin/env python
"""
LSTM Model Training Script for BTCUSD.a

This script trains LSTM models on BTCUSD.a data for multiple timeframes
using PyTorch with GPU acceleration.
"""

import os
import logging
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
# Removed train_test_split import - using proper temporal splitting instead
from pathlib import Path
import json
from datetime import datetime

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/train_lstm_btcusd.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import required modules
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

def load_data(timeframe, data_dir='data/historical/btcusd.a'):
    """Load data for a specific timeframe."""
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def preprocess_data(df, sequence_length=60, target_column='close',
                   feature_columns=None, test_size=0.2):
    """Preprocess data for LSTM training with enhanced error handling."""
    # Set default feature columns if not provided
    if feature_columns is None:
        feature_columns = ['open', 'high', 'low', 'close', 'real_volume']

    # Validate input data
    if df is None or df.empty:
        raise ValueError("Input dataframe is None or empty")

    # Check if required columns exist
    missing_cols = [col for col in feature_columns + [target_column] if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing columns in dataframe: {missing_cols}")

    # Check for sufficient data
    if len(df) < sequence_length + 1:
        raise ValueError(f"Insufficient data: need at least {sequence_length + 1} rows, got {len(df)}")

    # Remove any rows with NaN values
    df_clean = df[feature_columns + [target_column]].dropna()
    if len(df_clean) < len(df):
        logger.warning(f"Removed {len(df) - len(df_clean)} rows with NaN values")

    if len(df_clean) < sequence_length + 1:
        raise ValueError(f"Insufficient clean data after removing NaN: {len(df_clean)} rows")

    # Extract features and target
    X = df_clean[feature_columns].values
    y = df_clean[target_column].values.reshape(-1, 1)

    # Scale features and target
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()

    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    # Create sequences more efficiently
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    logger.info(f"Created {len(X_sequences)} sequences from {len(df_clean)} data points")

    # Split into train and test sets using proper temporal splitting
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]

    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def train_lstm_model(timeframe, feature_columns=None, target_column='close',
                    sequence_length=60, hidden_units=64, num_layers=2,
                    dropout_rate=0.2, learning_rate=0.001, epochs=100,
                    batch_size=32, validation_split=0.1, test_size=0.2,
                    random_state=42, use_gpu=True):
    """Train LSTM model for a specific timeframe."""
    try:
        # Configure GPU
        gpu_info = get_gpu_info()
        if gpu_info['gpu_available'] and use_gpu:
            logger.info(f"GPU is available: {gpu_info['gpu_devices']}")
            configure_gpu_memory()
            device = select_device(use_gpu=True)
            logger.info(f"Using device: {device}")
        else:
            if not gpu_info['gpu_available']:
                logger.warning("No GPU available, using CPU for training")
            elif not use_gpu:
                logger.warning("GPU available but use_gpu=False, using CPU for training")
            device = torch.device('cpu')

        # Load data
        df = load_data(timeframe)
        if df is None:
            logger.error(f"Failed to load data for timeframe {timeframe}")
            return None

        # Preprocess data
        try:
            X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data(
                df, sequence_length, target_column, feature_columns, test_size
            )
        except ValueError as e:
            logger.error(f"Data preprocessing failed for {timeframe}: {str(e)}")
            return None

        # Convert data to PyTorch tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train)
        X_test_tensor = torch.FloatTensor(X_test)
        y_test_tensor = torch.FloatTensor(y_test)

        # Create datasets and data loaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        # Split training data for validation
        train_size = int((1 - validation_split) * len(train_dataset))
        val_size = len(train_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            train_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(random_state)
        )

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size)
        test_loader = DataLoader(test_dataset, batch_size=batch_size)

        # Get input shape
        _, timesteps, features = X_train.shape

        # Create model
        model = LSTMModel(
            input_dim=features,
            hidden_dim=hidden_units,
            num_layers=num_layers,
            output_dim=1,
            dropout_rate=dropout_rate
        )

        # Create trainer
        trainer = LSTMTrainer(
            model=model,
            learning_rate=learning_rate,
            device=device
        )

        # Train model
        logger.info(f"Training PyTorch LSTM model for BTCUSD.a {timeframe} on {device}")
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=epochs,
            patience=10,
            verbose=True
        )

        # Evaluate model
        test_loss = trainer.evaluate(test_loader)
        logger.info(f"Test loss: {test_loss}")

        # Make predictions
        y_pred = trainer.predict(test_loader)

        # Inverse transform predictions and actual values
        y_pred_inv = y_scaler.inverse_transform(y_pred)
        y_test_inv = y_scaler.inverse_transform(y_test)

        # Calculate metrics
        mse = np.mean((y_pred_inv - y_test_inv) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_pred_inv - y_test_inv))

        # Calculate R² (coefficient of determination)
        y_mean = np.mean(y_test_inv)
        ss_total = np.sum((y_test_inv - y_mean) ** 2)
        ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
        r2 = 1 - (ss_residual / ss_total)

        logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

        # Save model with error handling
        try:
            model_name = f"lstm_BTCUSD.a_{timeframe}"
            model_dir = Path("models") / model_name
            model_dir.mkdir(parents=True, exist_ok=True)

            # Save model
            model.save(str(model_dir))
            logger.info(f"Model weights saved to {model_dir}")

            # Save scalers
            scalers = {'X_scaler': X_scaler, 'y_scaler': y_scaler}
            torch.save(scalers, model_dir / "scalers.pt")
            logger.info(f"Scalers saved to {model_dir / 'scalers.pt'}")

            # Save configuration
            config = {
                'model_type': 'pytorch_lstm',
                'symbol': 'BTCUSD.a',
                'timeframe': timeframe,
                'sequence_length': sequence_length,
                'feature_columns': feature_columns,
                'target_column': target_column,
                'hidden_units': hidden_units,
                'num_layers': num_layers,
                'dropout_rate': dropout_rate,
                'learning_rate': learning_rate,
                'input_shape': (None, timesteps, features),
                'device': str(device),
                'gpu_available': gpu_info['gpu_available'],
                'gpu_used': gpu_info['gpu_available'] and use_gpu,
                'pytorch_version': torch.__version__,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'epochs_trained': len(history['train_loss']),
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2)
                }
            }

            # Save configuration as JSON
            with open(model_dir / "config.json", "w") as f:
                json.dump(config, f, indent=4)

            logger.info(f"Configuration saved to {model_dir / 'config.json'}")
            logger.info(f"Model successfully saved to {model_dir}")

        except Exception as e:
            logger.error(f"Error saving model for {timeframe}: {str(e)}")
            # Continue execution even if saving fails

        return {
            'model': model,
            'history': history,
            'metrics': {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2)
            },
            'config': config
        }

    except Exception as e:
        logger.error(f"Error training LSTM model for {timeframe}: {str(e)}", exc_info=True)
        return None

def main():
    """Main function to train LSTM models for all timeframes."""
    # Define timeframes
    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']

    # Define feature columns
    feature_columns = ['open', 'high', 'low', 'close', 'real_volume']

    # Store metrics for all timeframes
    all_metrics = {}

    # Train models for all timeframes
    for timeframe in timeframes:
        logger.info(f"Processing timeframe: {timeframe}")

        result = train_lstm_model(
            timeframe=timeframe,
            feature_columns=feature_columns,
            target_column='close',
            sequence_length=60,
            hidden_units=64,
            num_layers=2,
            dropout_rate=0.2,
            learning_rate=0.001,
            epochs=100,
            batch_size=32,
            validation_split=0.1,
            test_size=0.2,
            random_state=42,
            use_gpu=True
        )

        if result:
            logger.info(f"Successfully trained LSTM model for BTCUSD.a {timeframe}")
            logger.info(f"Metrics: MSE={result['metrics']['mse']:.6f}, RMSE={result['metrics']['rmse']:.6f}, MAE={result['metrics']['mae']:.6f}, R2={result['metrics'].get('r2', 0.0):.6f}")

            # Store metrics for this timeframe
            all_metrics[timeframe] = result['metrics']
        else:
            logger.error(f"Failed to train LSTM model for BTCUSD.a {timeframe}")

    # Save all metrics to a summary file
    os.makedirs('metrics', exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_file = f"metrics/lstm_BTCUSD.a_summary_{timestamp}.json"

    with open(summary_file, 'w') as f:
        json.dump({
            'model_type': 'lstm',
            'symbol': 'BTCUSD.a',
            'timeframes': timeframes,
            'metrics': all_metrics
        }, f, indent=4)

    logger.info(f"Metrics summary saved to {summary_file}")

    # Print comparison table
    logger.info("Metrics comparison across timeframes:")
    logger.info(f"{'Timeframe':<10} {'MSE':<15} {'RMSE':<15} {'MAE':<15} {'R2':<15}")
    logger.info("-" * 70)

    for tf in timeframes:
        if tf in all_metrics:
            metrics = all_metrics[tf]
            r2_value = metrics.get('r2', 0.0)  # Use 0.0 as default if r2 is not present
            logger.info(f"{tf:<10} {metrics['mse']:<15.6f} {metrics['rmse']:<15.6f} {metrics['mae']:<15.6f} {r2_value:<15.6f}")
        else:
            logger.info(f"{tf:<10} {'Failed':<15} {'Failed':<15} {'Failed':<15} {'Failed':<15}")

if __name__ == "__main__":
    main()
