"""
GPU Check Utility

This script checks if PyTorch can access a GPU and performs a simple test computation.
"""
import torch

print("PyTorch version:", torch.__version__)
print("CUDA Available:", torch.cuda.is_available())

if torch.cuda.is_available():
    print("CUDA Version:", torch.version.cuda)
    print("GPU Device Count:", torch.cuda.device_count())
    print("GPU Device Name:", torch.cuda.get_device_name(0))

    # Get GPU memory information
    try:
        print("GPU Memory Allocated:", torch.cuda.memory_allocated(0) / (1024 ** 2), "MB")
        print("GPU Memory Reserved:", torch.cuda.memory_reserved(0) / (1024 ** 2), "MB")
    except Exception as e:
        print("Error getting GPU memory info:", e)

    # Test GPU with a simple computation
    print("\nRunning matrix multiplication test on GPU...")
    a = torch.tensor([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], device='cuda')
    b = torch.tensor([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]], device='cuda')

    # Warm up
    for _ in range(5):
        c = torch.matmul(a, b)

    # Benchmark
    import time
    start_time = time.time()
    iterations = 1000
    for _ in range(iterations):
        c = torch.matmul(a, b)
    torch.cuda.synchronize()  # Wait for GPU to finish
    end_time = time.time()

    print("Matrix multiplication result on GPU:", c.cpu().numpy())
    print(f"GPU Time for {iterations} iterations: {(end_time - start_time) * 1000:.2f} ms")

    # Compare with CPU
    print("\nRunning matrix multiplication test on CPU...")
    a_cpu = a.cpu()
    b_cpu = b.cpu()

    # Warm up
    for _ in range(5):
        c_cpu = torch.matmul(a_cpu, b_cpu)

    # Benchmark
    start_time = time.time()
    for _ in range(iterations):
        c_cpu = torch.matmul(a_cpu, b_cpu)
    end_time = time.time()

    print("Matrix multiplication result on CPU:", c_cpu.numpy())
    print(f"CPU Time for {iterations} iterations: {(end_time - start_time) * 1000:.2f} ms")

else:
    print("No GPU available, using CPU instead")

    # Test CPU with a simple computation
    a = torch.tensor([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    b = torch.tensor([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]])
    c = torch.matmul(a, b)
    print("Matrix multiplication result on CPU:", c.numpy())
