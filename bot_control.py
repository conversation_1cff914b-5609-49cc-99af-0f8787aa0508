"""
Command-line interface for controlling trading bots.
"""
import sys
import logging
import argparse
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/bot_control.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import unified configuration manager
from config.unified_config import config_manager

# Import necessary managers
from utils.enhanced_error_handler import EnhancedErrorHandler
error_handler = EnhancedErrorHandler()

from utils.enhanced_memory_manager import enhanced_memory_manager
memory_manager = enhanced_memory_manager

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32,
    thread_name_prefix="Main"
)

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
mt5_manager = MT5ConnectionManager(config_manager)

# Import TradingBotManager
from main import TradingBotManager

def create_manager():
    """Create and return a TradingBotManager instance."""
    return TradingBotManager(
        config_manager=config_manager,
        error_handler=error_handler,
        mt5_manager=mt5_manager,
        thread_manager=thread_manager,
        memory_manager=memory_manager
    )

def start_all_bots():
    """Start all trading bots."""
    logger.info("Initializing bot manager to start all bots...")
    
    # Create a manager instance
    manager = create_manager()
    
    # Start all bots
    logger.info("Starting all trading bots...")
    success = manager.start_all_bots()
    
    if success:
        logger.info("All trading bots started successfully.")
    else:
        logger.error("Failed to start all trading bots.")
        manager.shutdown()
    
    return success

def stop_all_bots():
    """Stop all running trading bots."""
    logger.info("Initializing bot manager to stop all bots...")
    
    # Create a manager instance
    manager = create_manager()
    
    # Stop all bots
    logger.info("Stopping all trading bots...")
    success = manager.stop_all_bots()
    
    if success:
        logger.info("All trading bots stopped successfully.")
    else:
        logger.error("Failed to stop all trading bots.")
    
    # Perform full shutdown
    logger.info("Performing full shutdown...")
    manager.shutdown()
    
    return success

def stop_specific_bot(terminal_id):
    """Stop a specific trading bot."""
    logger.info(f"Initializing bot manager to stop bot for terminal {terminal_id}...")
    
    # Create a manager instance
    manager = create_manager()
    
    # Check if the bot exists
    if terminal_id not in manager.bots:
        logger.error(f"No bot found for terminal {terminal_id}")
        return False
    
    # Stop the specific bot
    logger.info(f"Stopping bot for terminal {terminal_id}...")
    try:
        manager.bots[terminal_id].stop()
        logger.info(f"Bot for terminal {terminal_id} stopped successfully.")
        return True
    except Exception as e:
        logger.error(f"Failed to stop bot for terminal {terminal_id}: {e}")
        return False

def main():
    """Main function to parse command-line arguments and control bots."""
    parser = argparse.ArgumentParser(description="Control trading bots")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Start command
    start_parser = subparsers.add_parser("start", help="Start trading bots")
    
    # Stop command
    stop_parser = subparsers.add_parser("stop", help="Stop trading bots")
    stop_parser.add_argument("--terminal", type=str, help="Terminal ID to stop (if not specified, stops all bots)")
    
    # Status command (placeholder for future implementation)
    status_parser = subparsers.add_parser("status", help="Check status of trading bots")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Execute command
    if args.command == "start":
        success = start_all_bots()
        return 0 if success else 1
    
    elif args.command == "stop":
        if args.terminal:
            success = stop_specific_bot(args.terminal)
        else:
            success = stop_all_bots()
        return 0 if success else 1
    
    elif args.command == "status":
        logger.info("Status command not yet implemented")
        return 0
    
    else:
        parser.print_help()
        return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"Error in bot control: {e}", exc_info=True)
        sys.exit(1)
