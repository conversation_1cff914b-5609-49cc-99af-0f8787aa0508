"""
Main configuration file for the BTCUSD.a trading bot.
Contains all trading parameters, model configurations, and system settings.
"""

from typing import Dict, List, Any
from dataclasses import dataclass, field
import os
import MetaTrader5 as mt5
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# Environment configuration
ENV = os.getenv('TRADING_BOT_ENV', 'development')

@dataclass
class MT5TerminalConfig:
    """Configuration for a single MT5 terminal."""
    login: str
    password: str
    server: str
    path: str

@dataclass
class MT5Config:
    """Configuration for MT5 connection management."""
    max_connections: int
    timeout: int
    retry_interval: int
    terminals: Dict[str, Dict[str, str]]

@dataclass
class StrategyConfig:
    """Configuration for trading strategy."""
    symbol: str
    timeframes: List[str]
    sequence_length: int
    lot_size: float
    max_positions: int
    stop_loss_pips: int
    take_profit_pips: int
    max_spread_pips: int
    risk_per_trade: float
    max_daily_loss: float
    max_daily_trades: int
    cooldown_period: int

@dataclass
class ModelConfig:
    """Configuration for a single model."""
    model_path: str
    input_dim: int
    output_dim: int
    weight: float
    FEATURE_COLUMNS: List[str] = field(default_factory=list)

    # Common parameters
    sequence_length: int = 288
    batch_size: int = 32
    epochs: int = 100
    patience: int = 10
    learning_rate: float = 0.001
    dropout_rate: float = 0.2

    # LSTM specific
    hidden_units: int = 64
    num_layers: int = 2
    dense_units: int = 32

    # TFT specific
    hidden_size: int = 32
    attention_head_size: int = 4
    hidden_continuous_size: int = 16
    max_encoder_length: int = 100
    max_prediction_length: int = 20

    # ARIMA specific
    p: int = 1
    d: int = 1
    q: int = 1
    seasonal_p: int = 0
    seasonal_d: int = 0
    seasonal_q: int = 0
    seasonal_m: int = 0
    use_seasonal: bool = False
    auto_arima: bool = True

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.model_path:
            raise ValueError("Model path cannot be empty")
        if self.input_dim <= 0:
            raise ValueError("Input dimension must be positive")
        if self.output_dim <= 0:
            raise ValueError("Output dimension must be positive")
        if not 0 <= self.weight <= 1:
            raise ValueError("Weight must be between 0 and 1")
        if not self.FEATURE_COLUMNS:
            raise ValueError("Feature columns cannot be empty")

@dataclass
class TradingConfig:
    """Main trading configuration."""
    mt5: MT5Config
    strategy: StrategyConfig
    models: Dict[str, ModelConfig]
    data_base_path: str
    models_base_path: str
    confidence_threshold: float
    update_interval: int
    max_memory_usage: float
    log_level: str
    debug_mode: bool

    def to_dict(self) -> Dict[str, Any]:
        """Convert the config to a dictionary."""
        return {
            'mt5': {
                'max_connections': self.mt5.max_connections,
                'timeout': self.mt5.timeout,
                'retry_interval': self.mt5.retry_interval,
                'terminals': self.mt5.terminals
            },
            'strategy': {
                'symbol': self.strategy.symbol,
                'timeframes': self.strategy.timeframes,
                'sequence_length': self.strategy.sequence_length,
                'lot_size': self.strategy.lot_size,
                'max_positions': self.strategy.max_positions,
                'stop_loss_pips': self.strategy.stop_loss_pips,
                'take_profit_pips': self.strategy.take_profit_pips,
                'max_spread_pips': self.strategy.max_spread_pips,
                'risk_per_trade': self.strategy.risk_per_trade,
                'max_daily_loss': self.strategy.max_daily_loss,
                'max_daily_trades': self.strategy.max_daily_trades,
                'cooldown_period': self.strategy.cooldown_period
            },
            'models': {name: {
                'model_path': model.model_path,
                'input_dim': model.input_dim,
                'output_dim': model.output_dim,
                'weight': model.weight,
                'FEATURE_COLUMNS': model.FEATURE_COLUMNS,
                'sequence_length': model.sequence_length,
                'batch_size': model.batch_size,
                'epochs': model.epochs,
                'patience': model.patience,
                'learning_rate': model.learning_rate,
                'dropout_rate': model.dropout_rate
            } for name, model in self.models.items()},
            'data_base_path': self.data_base_path,
            'models_base_path': self.models_base_path,
            'confidence_threshold': self.confidence_threshold,
            'update_interval': self.update_interval,
            'max_memory_usage': self.max_memory_usage,
            'log_level': self.log_level,
            'debug_mode': self.debug_mode
        }

class ConfigurationManager:
    """Manages configuration loading and validation for the trading system."""

    _instance = None
    _config = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigurationManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_path: str = 'config/config.json'):
        if self._config is None:
            self.config_path = config_path
            self._load_config()

    def _load_config(self):
        try:
            with open(self.config_path, 'r') as f:
                config_data = json.load(f)

            # Create MT5Config
            mt5_config = MT5Config(
                max_connections=config_data['mt5']['max_connections'],
                timeout=config_data['mt5']['timeout'],
                retry_interval=config_data['mt5']['retry_interval'],
                terminals=config_data['mt5']['terminals']
            )

            # Create StrategyConfig
            strategy_config = StrategyConfig(
                symbol=config_data['strategy']['symbol'],
                timeframes=config_data['strategy']['timeframes'],
                sequence_length=config_data['strategy']['sequence_length'],
                lot_size=config_data['strategy']['lot_size'],
                max_positions=config_data['strategy']['max_positions'],
                stop_loss_pips=config_data['strategy']['stop_loss_pips'],
                take_profit_pips=config_data['strategy']['take_profit_pips'],
                max_spread_pips=config_data['strategy']['max_spread_pips'],
                risk_per_trade=config_data['strategy']['risk_per_trade'],
                max_daily_loss=config_data['strategy']['max_daily_loss'],
                max_daily_trades=config_data['strategy']['max_daily_trades'],
                cooldown_period=config_data['strategy']['cooldown_period']
            )

            # Create ModelConfigs
            models_config = {}
            for model_name, model_data in config_data['models'].items():
                models_config[model_name] = ModelConfig(
                    model_path=model_data['model_path'],
                    input_dim=model_data['input_dim'],
                    output_dim=model_data['output_dim'],
                    weight=model_data['weight'],
                    FEATURE_COLUMNS=model_data.get('FEATURE_COLUMNS', [])
                )

            # Create TradingConfig
            self._config = TradingConfig(
                mt5=mt5_config,
                strategy=strategy_config,
                models=models_config,
                data_base_path=config_data['data_base_path'],
                models_base_path=config_data['models_base_path'],
                confidence_threshold=config_data['confidence_threshold'],
                update_interval=config_data['update_interval'],
                max_memory_usage=config_data['max_memory_usage'],
                log_level=config_data['log_level'],
                debug_mode=config_data['debug_mode']
            )

            logger.info("Configuration loaded successfully")

        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            raise

    def get_config(self) -> TradingConfig:
        return self._config

    def get_mt5_config(self) -> MT5Config:
        return self._config.mt5

    def get_strategy_config(self) -> StrategyConfig:
        return self._config.strategy

    def get_data_base_path(self) -> str:
        return self._config.data_base_path

    def get_models_base_path(self) -> str:
        return self._config.models_base_path

    def get_model_config(self, model_name: str) -> ModelConfig:
        return self._config.models.get(model_name)

    def get_all_model_configs(self) -> Dict[str, ModelConfig]:
        return self._config.models

    def validate(self) -> bool:
        """Validate the configuration."""
        try:
            # Basic validation
            if not self._config:
                logger.error("Configuration not loaded")
                return False

            # Validate MT5 terminals
            if not self._config.mt5.terminals:
                logger.error("No MT5 terminals configured")
                return False

            # Validate strategy
            if not self._config.strategy.timeframes:
                logger.error("No timeframes configured")
                return False

            # Validate models
            if not self._config.models:
                logger.error("No models configured")
                return False

            # Validate paths
            data_path = Path(self._config.data_base_path)
            if not data_path.exists():
                logger.warning(f"Data base path does not exist: {data_path}")
                data_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created data base path: {data_path}")

            models_path = Path(self._config.models_base_path)
            if not models_path.exists():
                logger.warning(f"Models base path does not exist: {models_path}")
                models_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created models base path: {models_path}")

            logger.info("Configuration validation successful")
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

if __name__ == "__main__":
    # Test configuration loading and validation
    print("Testing configuration...")

    try:
        config_manager = ConfigurationManager()
        if config_manager.validate():
            print("Configuration is valid")
            trading_config = config_manager.get_config()
            print("Trading configuration loaded successfully")

            # Print some config details
            print(f"\nMT5 Terminals: {len(trading_config.mt5.terminals)}")
            print(f"Strategy Symbol: {trading_config.strategy.symbol}")
            print(f"Models:")
            for model_name, model_config in trading_config.models.items():
                print(f"  {model_name}: {model_config.model_path}")
        else:
            print("Configuration validation failed")
    except Exception as e:
        print(f"Error: {e}")

# Export the ConfigurationManager class
__all__ = ['ConfigurationManager']

# Log environment
print(f"Running in {ENV} environment")

# MT5 Terminal configurations
MT5_TERMINALS: Dict[str, Dict[str, str]] = {
    "terminal1": {
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
        "login": "",
        "password": "",
        "server": ""
    },
    "terminal2": {
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
        "login": "",
        "password": "",
        "server": ""
    },
    "terminal3": {
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
        "login": "",
        "password": "",
        "server": ""
    },
    "terminal4": {
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
        "login": "",
        "password": "",
        "server": ""
    },
    "terminal5": {
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
        "login": "",
        "password": "",
        "server": ""
    }
}

# Timeframe mapping
TIMEFRAME_MAPPING: Dict[str, int] = {
    "M5": mt5.TIMEFRAME_M5,
    "M15": mt5.TIMEFRAME_M15,
    "M30": mt5.TIMEFRAME_M30
}

# Feature engineering parameters
FEATURE_PARAMS: Dict[str, Dict[str, float]] = {
    "rsi": {"period": 14},
    "macd": {"fast": 12, "slow": 26, "signal": 9},
    "bollinger_bands": {"period": 20, "std_dev": 2},
    "atr": {"period": 14},
    "adx": {"period": 14},
    "obv": {}
}

# Adjust configurations based on environment
env = os.getenv("TRADING_BOT_ENV", "development").lower()
# These settings will be applied after config is loaded
# They are commented out to avoid errors since they reference undefined variables
# if env == "production":
#     trading_config.lot_size = 0.1
#     trading_config.max_positions = 3
#     # Use system config from unified config
#     system_config = {'LOG_LEVEL': "WARNING"}
# elif env == "testing":
#     trading_config.lot_size = 0.01
#     trading_config.max_positions = 1
#     # Use system config from unified config
#     system_config = {'LOG_LEVEL': "DEBUG"}

# Export configuration instances
config_manager = ConfigurationManager()
trading_config = config_manager.get_config()

# Timeframe mapping
TIMEFRAME_MAPPING = {
    'M1': mt5.TIMEFRAME_M1,
    'M5': mt5.TIMEFRAME_M5,
    'M15': mt5.TIMEFRAME_M15,
    'M30': mt5.TIMEFRAME_M30,
    'H1': mt5.TIMEFRAME_H1,
    'H4': mt5.TIMEFRAME_H4,
    'D1': mt5.TIMEFRAME_D1,
    'W1': mt5.TIMEFRAME_W1,
    'MN1': mt5.TIMEFRAME_MN1
}

# Export system configuration
system_config = {
    'DATA_DIR': 'data',
    'MODEL_DIR': 'models',
    'LOG_FILE': 'trading_bot.log',
    'MAX_MEMORY_USAGE': 0.8,  # 80% of available memory
    'LOG_LEVEL': 'INFO',
    'DEBUG_MODE': ENV == 'development'
}

# Export model configuration
model_config = trading_config.models