"""
Main configuration file for the BTCUSD.a trading bot.
Contains all trading parameters, model configurations, and system settings.
"""

from typing import Dict, List, Any
from dataclasses import dataclass, field
import os
import MetaTrader5 as mt5
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# Environment configuration
ENV = os.getenv('TRADING_BOT_ENV', 'development')

@dataclass
class MT5TerminalConfig:
    """Configuration for a single MT5 terminal."""
    login: str
    password: str
    server: str
    path: str

@dataclass
class MT5Config:
    """Configuration for MT5 connection management."""
    max_connections: int
    timeout: int
    retry_interval: int
    terminals: Dict[str, Dict[str, str]]

@dataclass
class StrategyConfig:
    """Configuration for trading strategy."""
    symbol: str
    timeframes: List[str]
    sequence_length: int
    lot_size: float
    max_positions: int
    stop_loss_pips: int
    take_profit_pips: int
    max_spread_pips: int
    risk_per_trade: float
    max_daily_loss: float
    max_daily_trades: int
    cooldown_period: int

@dataclass
class ModelConfig:
    """Configuration for a single model."""
    model_path: str
    input_dim: int
    output_dim: int
    weight: float
    FEATURE_COLUMNS: List[str] = field(default_factory=list)

    # Common parameters
    sequence_length: int = 288
    batch_size: int = 32
    epochs: int = 100
    patience: int = 10
    learning_rate: float = 0.001
    dropout_rate: float = 0.2

    # LSTM specific
    hidden_units: int = 64
    num_layers: int = 2
    dense_units: int = 32

    # TFT specific
    hidden_size: int = 32
    attention_head_size: int = 4
    hidden_continuous_size: int = 16
    max_encoder_length: int = 100
    max_prediction_length: int = 20

    # ARIMA specific
    p: int = 1
    d: int = 1
    q: int = 1
    seasonal_p: int = 0
    seasonal_d: int = 0
    seasonal_q: int = 0
    seasonal_m: int = 0
    use_seasonal: bool = False
    auto_arima: bool = True

    def __post_init__(self):
        """Validate configuration parameters."""
        if not self.model_path:
            raise ValueError("Model path cannot be empty")
        if self.input_dim <= 0:
            raise ValueError("Input dimension must be positive")
        if self.output_dim <= 0:
            raise ValueError("Output dimension must be positive")
        if not 0 <= self.weight <= 1:
            raise ValueError("Weight must be between 0 and 1")
        if not self.FEATURE_COLUMNS:
            raise ValueError("Feature columns cannot be empty")

# TradingConfig is now imported from unified_config to avoid duplication
# This ensures consistency across the entire codebase
from .unified_config import TradingConfig

# ConfigurationManager is now imported from unified_config to avoid duplication
# This ensures consistency across the entire codebase
from .unified_config import UnifiedConfigManager as ConfigurationManager

if __name__ == "__main__":
    # Test configuration loading and validation
    print("Testing configuration...")

    try:
        config_manager = ConfigurationManager()
        if config_manager.validate():
            print("Configuration is valid")
            trading_config = config_manager.get_config()
            print("Trading configuration loaded successfully")

            # Print some config details
            print(f"\nMT5 Terminals: {len(trading_config.mt5.terminals)}")
            print(f"Strategy Symbol: {trading_config.strategy.symbol}")
            print(f"Models:")
            for model_name, model_config in trading_config.models.items():
                print(f"  {model_name}: {model_config.model_path}")
        else:
            print("Configuration validation failed")
    except Exception as e:
        print(f"Error: {e}")

# Export the ConfigurationManager class
__all__ = ['ConfigurationManager']

# Log environment
print(f"Running in {ENV} environment")

# MT5 Terminal configurations are now imported from credentials
# This ensures consistency across the entire codebase
try:
    from .credentials import MT5_TERMINALS
except ImportError:
    # Fallback for development/testing
    MT5_TERMINALS: Dict[str, Dict[str, str]] = {}

# Feature engineering parameters
FEATURE_PARAMS: Dict[str, Dict[str, float]] = {
    "rsi": {"period": 14},
    "macd": {"fast": 12, "slow": 26, "signal": 9},
    "bollinger_bands": {"period": 20, "std_dev": 2},
    "atr": {"period": 14},
    "adx": {"period": 14},
    "obv": {}
}

# Adjust configurations based on environment
env = os.getenv("TRADING_BOT_ENV", "development").lower()
# These settings will be applied after config is loaded
# They are commented out to avoid errors since they reference undefined variables
# if env == "production":
#     trading_config.lot_size = 0.1
#     trading_config.max_positions = 3
#     # Use system config from unified config
#     system_config = {'LOG_LEVEL': "WARNING"}
# elif env == "testing":
#     trading_config.lot_size = 0.01
#     trading_config.max_positions = 1
#     # Use system config from unified config
#     system_config = {'LOG_LEVEL': "DEBUG"}

# Timeframe mapping - standardized across the codebase
TIMEFRAME_MAPPING = {
    'M1': mt5.TIMEFRAME_M1,
    'M5': mt5.TIMEFRAME_M5,
    'M15': mt5.TIMEFRAME_M15,
    'M30': mt5.TIMEFRAME_M30,
    'H1': mt5.TIMEFRAME_H1,
    'H4': mt5.TIMEFRAME_H4,
    'D1': mt5.TIMEFRAME_D1,
    'W1': mt5.TIMEFRAME_W1,
    'MN1': mt5.TIMEFRAME_MN1
}

# Export configuration instances for backwards compatibility
config_manager = ConfigurationManager()
trading_config = config_manager.get_config()
system_config = config_manager.get_system_config()
model_config = trading_config.models