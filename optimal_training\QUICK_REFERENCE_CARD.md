# Quick Reference Card - Optimal Model Training

## 🎯 Performance Summary (M5 Timeframe)

| Rank | Model | R² | Accuracy | Command | Time |
|------|-------|----|---------|---------|----- |
| **1st** | **LSTM** | **0.9999** | **99.99%** | `train_all_lstm_models.bat` | 15m |
| **2nd** | **ARIMA+LSTM** | **0.9986** | **99.86%** | `train_all_arima_lstm_ensemble.bat` | 45m |
| **3rd** | **ARIMA** | **0.9784** | **97.84%** | `train_all_arima_models.bat` | 30m |
| **4th** | **ARIMA+TFT** | **0.6243** | **62.43%** | `train_all_arima_tft_ensemble.bat` | 35m |
| **5th** | **TFT** | **0.5289** | **52.89%** | `train_all_tft_models.bat` | 15m |

## ⚡ Quick Commands

### Production Ready (R² > 0.97)
```bash
# Best Performance (15 min)
train_all_lstm_models.bat

# Ultimate Ensemble (45 min)
train_all_arima_lstm_ensemble.bat

# Traditional Excellence (30 min)
train_all_arima_models.bat
```

### Research Models (R² 0.5-0.7)
```bash
# Modern Deep Learning (15 min)
train_all_tft_models.bat

# Hybrid Approach (35 min)
train_all_arima_tft_ensemble.bat
```

## 🔧 Critical Parameters

### LSTM (R² = 0.999+)
```bash
python train_lstm_single.py --timeframe M5 --hidden-units 64 --num-layers 2 --dropout-rate 0.2 --learning-rate 0.001 --epochs 100 --batch-size 32
```

### ARIMA (R² = 0.978+)
```bash
python train_arima_single.py --timeframe M5 --auto-arima --max-rows 50000 --data-selection all --use-ensemble --ensemble-models 5
```

### TFT (R² = 0.529+)
```bash
python train_tft_pytorch.py --timeframe M5 --hidden-dim 64 --num-heads 4 --num-layers 2 --dropout-rate 0.1 --epochs 5 --batch-size 32
```

### TFT+ARIMA (R² = 0.624+)
```bash
python train_tft_arima_single.py --timeframe M5 --hidden-size 64 --attention-head-size 4 --arima-window 10000 --epochs 5 --batch-size 32
```

## 📊 Latest Metrics (Collection Times)

| Model | Collection Time | R² | RMSE | Status |
|-------|----------------|----|----- |--------|
| LSTM | 2025-05-26 12:38:27 | 0.9999 | 247.58 | ✅ Production |
| ARIMA+LSTM | 2025-05-26 11:41:19 | 0.9986 | 581.00 | ✅ Champion |
| ARIMA | 2025-05-25 12:13:23 | 0.9784 | 2,306.45 | ✅ Production |
| ARIMA+TFT | 2025-05-26 08:38:48 | 0.6243 | 9,616.43 | ⚠️ Research |
| TFT | 2025-05-26 08:06:21 | 0.5289 | 10,768.43 | ⚠️ Research |

## 💻 Hardware Requirements

### Minimum
- GPU: NVIDIA GTX 1060 (6GB)
- RAM: 16GB
- Storage: 10GB

### Recommended
- GPU: NVIDIA RTX 2070+ (8GB+)
- RAM: 32GB
- Storage: 20GB SSD

## 📦 Dependencies
```bash
pip install torch==2.6.0+cu118 pmdarima==2.0.3 statsmodels==0.14.0 numpy==1.24.3 pandas==2.0.3 scikit-learn==1.3.0
```

## ✅ Success Criteria

| Model | M5 | M15 | M30 | H1 | H4 |
|-------|----|----|-----|----|----|
| LSTM | >0.999 | >0.999 | >0.999 | >0.998 | >0.995 |
| ARIMA+LSTM | >0.998 | >0.996 | >0.993 | >0.986 | >0.948 |
| ARIMA | >0.975 | >0.970 | >0.965 | >0.940 | >0.915 |
| ARIMA+TFT | >0.620 | >0.615 | >0.610 | >0.605 | >0.595 |
| TFT | >0.520 | >0.515 | >0.505 | >0.495 | >0.475 |

## 🔍 Validation Commands
```bash
# Test ensemble
python test_lstm_arima_ensemble.py

# Compare all models
python compare_all_models.py --output-dir results

# Check model loading
python -c "from models.pytorch_lstm_model import LSTMModel; print('OK')"
```

## 🚨 Troubleshooting

### GPU Issues
```bash
python -c "import torch; print(torch.cuda.is_available())"
```

### Memory Issues
- Reduce batch_size from 32 to 16
- Use gradient checkpointing
- Close other applications

### Performance Issues
- Check data quality (no missing values)
- Verify exact parameter configuration
- Monitor training/validation loss patterns

## 📁 Documentation Files

1. **00_MASTER_INDEX** - Complete overview
2. **01_LSTM_MODEL** - Best performer (R² = 0.999+)
3. **02_ARIMA_MODEL** - Traditional excellence (R² = 0.978+)
4. **03_TFT_MODEL** - Modern approach (R² = 0.529+)
5. **04_ARIMA_LSTM_ENSEMBLE** - Ultimate champion (R² = 0.998+)
6. **05_ARIMA_TFT_ENSEMBLE** - Hybrid research (R² = 0.624+)

Each file contains:
- Latest performance metrics with timestamps
- Exact configuration parameters
- Step-by-step replication instructions
- AI assistant prompts for recreation
- Troubleshooting guides

## 🎯 Recommended Workflow

### For Production
1. `train_all_lstm_models.bat` (15 min)
2. `train_all_arima_lstm_ensemble.bat` (45 min)
3. Validate with `python test_lstm_arima_ensemble.py`

### For Research
1. Train all models: 80 minutes total
2. Compare performance: `python compare_all_models.py`
3. Analyze results and optimize

### For Quick Testing
1. `train_all_lstm_models.bat` (15 min)
2. Immediate R² = 0.999+ performance
