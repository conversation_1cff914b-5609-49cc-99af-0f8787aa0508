@echo off
echo ===================================================
echo ENABLE ALGO TRADING IN MT5 TERMINALS
echo ===================================================
echo.
echo This script will:
echo 1. Open all MT5 terminals
echo 2. Provide instructions for enabling Algo Trading
echo 3. Check if Algo Trading is enabled
echo.
echo IMPORTANT: You must manually enable Algo Trading in each terminal!
echo.
pause

echo.
echo Opening all MT5 terminals...
python scripts/open_mt5_terminals.py
echo.
echo Please enable Algo Trading in each terminal by clicking the 'Algo Trading' button.
echo When all terminals are enabled, press any key to continue.
echo.
pause

echo.
echo Checking Algo Trading status in all terminals...
python scripts/check_algo_trading.py
echo.
echo If any terminals still have Algo Trading disabled, please enable it manually.
echo.
pause
