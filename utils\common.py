"""
Common utility functions for the trading bot.

This module provides common utility functions used across the trading bot system,
including standardized path handling, terminal ID normalization, and other
helper functions.
"""

import os
import logging
from pathlib import Path
from typing import Union, Optional, Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

def normalize_terminal_id(terminal_id) -> str:
    """
    Ensure terminal ID is consistently a string.
    
    Args:
        terminal_id: Terminal ID (can be int or str)
        
    Returns:
        str: Terminal ID as string
    """
    return str(terminal_id)

def ensure_directory_exists(path: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path
        
    Returns:
        Path: Path object for the directory
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj

def get_timeframe_minutes(timeframe: str) -> int:
    """
    Convert timeframe string to minutes.
    
    Args:
        timeframe: Timeframe string (e.g., 'M1', 'H4', 'D1')
        
    Returns:
        int: Timeframe in minutes
    """
    if not timeframe:
        return 0
        
    # Handle standard timeframes
    if timeframe == 'M1':
        return 1
    elif timeframe == 'M5':
        return 5
    elif timeframe == 'M15':
        return 15
    elif timeframe == 'M30':
        return 30
    elif timeframe == 'H1':
        return 60
    elif timeframe == 'H4':
        return 240
    elif timeframe == 'D1':
        return 1440
    elif timeframe == 'W1':
        return 10080
    elif timeframe == 'MN1':
        return 43200
        
    # Handle custom format (e.g., 'M2', 'H2')
    try:
        if timeframe.startswith('M'):
            return int(timeframe[1:])
        elif timeframe.startswith('H'):
            return int(timeframe[1:]) * 60
        elif timeframe.startswith('D'):
            return int(timeframe[1:]) * 1440
    except ValueError:
        logger.warning(f"Could not parse timeframe: {timeframe}")
        return 0
        
    logger.warning(f"Unknown timeframe format: {timeframe}")
    return 0

def format_path_with_context(base_path: Union[str, Path], 
                           terminal_id: Optional[Union[str, int]] = None,
                           timeframe: Optional[str] = None,
                           filename: Optional[str] = None) -> Path:
    """
    Format a path with context information.
    
    Args:
        base_path: Base path
        terminal_id: Optional terminal ID
        timeframe: Optional timeframe
        filename: Optional filename
        
    Returns:
        Path: Formatted path
    """
    path = Path(base_path)
    
    if terminal_id is not None:
        path = path / normalize_terminal_id(terminal_id)
        
        if timeframe is not None:
            path = path / timeframe
            
    if filename is not None:
        path = path / filename
        
    return path

def safe_cast(val, to_type, default=None):
    """
    Safely cast a value to a specified type.
    
    Args:
        val: Value to cast
        to_type: Type to cast to
        default: Default value if casting fails
        
    Returns:
        The cast value or default if casting fails
    """
    try:
        return to_type(val)
    except (ValueError, TypeError):
        return default

def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two dictionaries recursively.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary (values override dict1)
        
    Returns:
        Dict: Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
            
    return result
