# BTCUSD Trading Bot - Model Training Guide

## Introduction

This guide provides detailed information about the machine learning models used in the BTCUSD Trading Bot, how they are trained, and how to optimize their performance. The trading bot uses an ensemble of three different models to generate trading signals, each with its own strengths and characteristics.

## Models Overview

The trading bot employs three different machine learning models:

1. **LSTM (Long Short-Term Memory)**: A recurrent neural network architecture designed to model temporal sequences and their long-range dependencies.
2. **TFT (Temporal Fusion Transformer)**: A state-of-the-art architecture that combines high-performance multi-horizon forecasting with interpretable insights.
3. **ARIMA (AutoRegressive Integrated Moving Average)**: A statistical time series forecasting method that captures linear temporal dependencies in financial data.

## Data Preparation for Training

### Data Collection

Before training, historical data is collected from MT5 terminals using the `MT5DataCollector` class. The data includes:

- OHLCV (Open, High, Low, Close, Volume) data
- Multiple timeframes (M5, M15, M30, H1, D1)
- Sufficient historical periods (typically several months to years)

### Preprocessing Steps

The `DataPreprocessor` class performs several key transformations:

1. **Technical Indicators**: Calculates various indicators including:
   - RSI (Relative Strength Index)
   - MACD (Moving Average Convergence Divergence)
   - Bollinger Bands
   - ATR (Average True Range)
   - ADX (Average Directional Index)
   - OBV (On Balance Volume)

2. **Feature Engineering**:
   - Price-based features (returns, log returns, volatility)
   - Momentum indicators
   - Volume-based features

3. **Normalization**: Scales features to appropriate ranges using methods like:
   - Min-Max scaling
   - Z-score normalization
   - Robust scaling

4. **Sequence Creation**: For time series models (LSTM, GRU, TFT), creates sequences of specified length.

5. **Train-Validation-Test Split**: Typically uses:
   - 70% for training
   - 20% for validation
   - 10% for testing

## Training Process

### Base Training Workflow

All models follow a similar training workflow implemented in the `BaseModel` class:

1. **Initialization**: Set up model architecture and hyperparameters
2. **Data Loading**: Load preprocessed training and validation data
3. **Model Building**: Construct the model architecture
4. **Training**: Fit the model to training data with validation
5. **Evaluation**: Assess model performance on test data
6. **Saving**: Store the trained model for future use

### Model-Specific Training

#### LSTM Model

```python
# LSTM training parameters from config
lstm_params = {
    'input_dim': 5,  # Number of input features
    'hidden_dim': 64,  # Number of hidden units in LSTM layers
    'num_layers': 2,  # Number of LSTM layers
    'output_dim': 1,  # Number of output features
    'dropout_rate': 0.2,  # Dropout rate for regularization
    'learning_rate': 0.001,
    'batch_size': 32,
    'epochs': 100,
    'patience': 10
}
```

The LSTM model uses PyTorch and implements:
- Multiple LSTM layers with specified hidden dimensions
- Dropout for regularization
- Linear layers for final prediction
- Adam optimizer with configurable learning rate
- Early stopping based on validation loss
- GPU acceleration when available

The LSTM model is implemented in `models/pytorch_lstm_model.py` and follows a simple architecture:
```python
class LSTMModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers, output_dim, dropout_rate=0.2):
        super(LSTMModel, self).__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.output_dim = output_dim
        self.dropout_rate = dropout_rate

        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0
        )

        # Output layer
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        # LSTM forward pass
        lstm_out, _ = self.lstm(x)

        # Get the last time step's output
        y_pred = self.fc(lstm_out[:, -1, :])

        return y_pred
```



#### TFT Model

```python
# TFT training parameters from config
tft_params = {
    'hidden_size': 128,
    'num_heads': 8,
    'num_layers': 3,
    'dropout_rate': 0.1,
    'learning_rate': 0.001,
    'batch_size': 64,
    'epochs': 100,
    'patience': 10,
    'max_encoder_length': 100,
    'max_prediction_length': 1
}
```

The TFT model uses PyTorch Lightning and pytorch_forecasting, implementing:
- Variable selection networks for feature selection
- Gated residual networks for processing
- Multi-head attention for temporal patterns
- Quantile loss for probabilistic forecasting

#### ARIMA Model

```python
# Standard ARIMA training parameters from config
arima_params = {
    'p': 1,
    'd': 1,
    'q': 1,
    'seasonal_p': 0,
    'seasonal_d': 0,
    'seasonal_q': 0,
    'seasonal_m': 0,
    'use_seasonal': False,
    'auto_arima': True,
    'use_exog': False,
    'forecast_steps': 1
}

# Ensemble ARIMA training parameters from config
ensemble_arima_params = {
    'n_models': 5,
    'd': 1,
    'seasonal': False,
    'seasonal_m': 0,
    'use_exog': True,
    'max_rows': 50000,
    'data_selection': 'all'
}
```

#### Standard ARIMA Model

The standard ARIMA model uses statistical time series forecasting with:
- Configurable autoregressive (p), differencing (d), and moving average (q) parameters
- Optional seasonal components
- Automatic parameter selection with auto_arima
- Support for exogenous variables
- Confidence intervals for predictions

#### Ensemble ARIMA Model

The ensemble ARIMA model combines multiple ARIMA models for improved performance:
- Multiple base models with different configurations
- Meta-model for intelligent combination of predictions
- Advanced feature engineering with over 80 engineered features
- Feature selection to identify the most important predictors
- Data scaling for improved numerical stability

### Training ARIMA Models

ARIMA models must be trained before use. You can train them using the following methods:

#### Method 1: Using the train_arima_single.py script

```bash
# Train standard ARIMA model for BTCUSD.a M5 with auto parameter selection
python train_arima_single.py --timeframe M5 --target-column close --auto-arima

# Train standard ARIMA model with specific parameters
python train_arima_single.py --timeframe M5 --target-column close --p 2 --d 1 --q 2

# Train ensemble ARIMA model
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --use-ensemble --ensemble-models 5

# Train with data selection options
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --max-rows 50000 --data-selection all
```

#### Method 2: Using the train_all_arima_models.bat script

To train ARIMA models for all timeframes at once:

```bash
# On Windows:
train_all_arima_models.bat
```

This script will train ARIMA models for M5, M15, M30, H1, and H4 timeframes using auto parameter selection and the ensemble approach.

#### Data Selection Options

The ARIMA training script supports several data selection strategies:

- **--max-rows**: Maximum number of rows to use for training (0 for all data)
- **--data-selection**: Method for selecting data
  - `recent`: Use only the most recent data (default)
  - `all`: Use all available data
  - `sample`: Sample data at regular intervals
- **--sample-interval**: Interval for sampling data (only used with --data-selection=sample)

#### Important Notes

- ARIMA models must be trained before they can be used for prediction
- If an ARIMA model is not trained, it will return zeros for predictions
- The warning "Model not trained, returning zeros" indicates that the model needs to be trained
- ARIMA models are saved in the models directory with the .pkl extension
- Training ARIMA models is typically faster than training neural network models
- Ensemble ARIMA models provide better performance but require more computational resources
- Feature engineering is automatically applied during training

## Hyperparameter Optimization

The trading bot supports hyperparameter optimization for all models:

### Optimization Methods

1. **Grid Search**: Systematically works through multiple combinations of parameter values
2. **Random Search**: Samples parameter values from specified distributions
3. **Bayesian Optimization**: Uses Bayesian methods to find optimal parameters more efficiently

### Key Parameters to Optimize

#### For Neural Networks (LSTM, TFT)
- Number of layers and units
- Dropout rate
- Learning rate
- Batch size
- Sequence length

#### For ARIMA Models
- p, d, q parameters
- Seasonal components
- Exogenous variables
- Differencing method
- Information criteria (AIC, BIC)

### Example Optimization Workflow

1. Define parameter search space
2. Set up cross-validation strategy
3. Execute optimization algorithm
4. Select best parameters
5. Retrain model with optimal parameters
6. Evaluate on test set

## Model Evaluation

The trading bot evaluates models using several metrics:

### Regression Metrics
- **RMSE (Root Mean Squared Error)**: Measures the average magnitude of prediction errors
- **MAE (Mean Absolute Error)**: Measures the average absolute difference between predictions and actual values
- **R² (Coefficient of Determination)**: Indicates the proportion of variance in the dependent variable predictable from the independent variables

### Directional Metrics
- **Directional Accuracy**: Percentage of correct predictions of price movement direction
- **Precision**: Ratio of correct positive predictions to total positive predictions
- **Recall**: Ratio of correct positive predictions to all actual positives
- **F1 Score**: Harmonic mean of precision and recall

### Financial Metrics
- **Profit Factor**: Ratio of gross profit to gross loss
- **Sharpe Ratio**: Risk-adjusted return measure
- **Maximum Drawdown**: Maximum observed loss from a peak to a trough

## Continuous Learning

The trading bot implements continuous learning to adapt to changing market conditions:

### Retraining Schedule

- **Periodic Retraining**: Models are retrained at regular intervals (e.g., weekly or monthly)
- **Performance-Based Retraining**: Models are retrained when performance metrics deteriorate
- **Data-Based Retraining**: Models are retrained when sufficient new data is available

### Model Decay Detection

The system monitors for model decay using:

- **Prediction Error Tracking**: Monitors increasing error trends
- **Distribution Shift Detection**: Detects changes in feature distributions
- **Performance Metrics Monitoring**: Tracks deterioration in financial performance

## Ensemble Strategy

The trading bot uses an ensemble approach to combine predictions from all models:

### Weighting Methods

- **Equal Weighting**: All models have the same influence
- **Performance-Based Weighting**: Models with better recent performance get higher weights
- **Adaptive Weighting**: Weights are dynamically adjusted based on ongoing performance

### Weight Calculation

The `TradingStrategy.update_model_weights()` method calculates weights based on:

```python
# Calculate total performance score for each model
model_scores = {}
for model_name, metrics in performance_metrics.items():
    # Combine different metrics into a single score
    score = (
        0.4 * (1 - metrics.get('rmse', 0)) +  # Lower RMSE is better
        0.3 * metrics.get('directional_accuracy', 0) +  # Higher accuracy is better
        0.3 * (1 - metrics.get('mae', 0))  # Lower MAE is better
    )
    model_scores[model_name] = score

# Normalize scores to sum to 1
total_score = sum(model_scores.values())
if total_score > 0:
    self.model_weights = {
        model_name: score / total_score
        for model_name, score in model_scores.items()
    }
```

## Best Practices

### Data Quality

- Use sufficient historical data (at least 1-2 years)
- Ensure data is clean and free of errors
- Handle missing values appropriately
- Normalize features consistently

### Training Process

- Use appropriate validation strategies (e.g., time series cross-validation)
- Implement early stopping to prevent overfitting
- Save model checkpoints during training
- Log training metrics for analysis

### Model Selection

- Consider computational requirements for each model
- Balance complexity with performance
- Evaluate models on multiple metrics
- Test models on out-of-sample data

### Hyperparameter Tuning

- Start with reasonable default values
- Use systematic optimization approaches
- Consider the trade-off between training time and performance
- Validate optimized parameters on different time periods

## Troubleshooting

### Common Issues

1. **Overfitting**
   - **Symptoms**: Good training performance but poor validation/test performance
   - **Solutions**: Increase regularization, reduce model complexity, use more training data

2. **Underfitting**
   - **Symptoms**: Poor performance on both training and validation data
   - **Solutions**: Increase model complexity, add more features, tune hyperparameters

3. **Vanishing/Exploding Gradients**
   - **Symptoms**: Training loss becomes NaN or doesn't converge
   - **Solutions**: Use gradient clipping, adjust learning rate, use batch normalization

4. **Data Leakage**
   - **Symptoms**: Unrealistically good performance
   - **Solutions**: Ensure proper train-test split, avoid look-ahead bias

5. **Poor Generalization**
   - **Symptoms**: Model performs well in backtesting but poorly in live trading
   - **Solutions**: Use more diverse training data, implement walk-forward optimization

## Recommended Model Combinations

Based on our testing, here are some recommended model combinations for different scenarios:

1. **Balanced Approach**: Use all three models with equal weights (0.33 each)
2. **Trending Markets**: LSTM (0.6) + TFT (0.4)
3. **Ranging Markets**: ARIMA (0.6) + LSTM (0.4)
4. **Volatile Markets**: TFT (0.6) + ARIMA (0.4)
5. **Conservative Approach**: ARIMA (0.5) + LSTM (0.3) + TFT (0.2)

## Conclusion

Effective model training is crucial for the performance of the BTCUSD Trading Bot. By understanding the training process, optimizing hyperparameters, and implementing continuous learning, you can maximize the system's trading performance and adaptability to changing market conditions.

Remember that no model is perfect, and the ensemble approach helps mitigate the weaknesses of individual models. Regular monitoring and retraining are essential to maintain performance over time.