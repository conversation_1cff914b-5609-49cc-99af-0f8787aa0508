"""
TFT Model Trainer

This module provides utilities for training Temporal Fusion Transformer models for the trading bot.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import base trainer
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.model_trainer import ModelTrainer
from config.unified_config import UnifiedConfigManager
from config.tft_model_config import get_tft_config

class TFTTrainer(ModelTrainer):
    """Class for training Temporal Fusion Transformer models."""

    def __init__(self, config_manager: Optional[UnifiedConfigManager] = None):
        """
        Initialize the TFTTrainer.

        Args:
            config_manager: Configuration manager instance
        """
        super().__init__(config_manager)

    def train(
        self,
        symbol: str,
        timeframe: str,
        model_name: str = 'tft',
        sequence_length: int = 60,
        feature_columns: Optional[List[str]] = None,
        target_column: str = 'close',
        test_size: float = 0.2,
        random_state: int = 42,
        epochs: int = 100,
        batch_size: int = 32,
        hidden_size: int = 64,
        dropout_rate: float = 0.1,
        learning_rate: float = 0.001,
        file_format: str = 'csv',
        data_dir: Optional[str] = None,
        use_gpu: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Train a TFT model.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            model_name: Name of the model
            sequence_length: Length of input sequences
            feature_columns: Columns to use as features (if None, use OHLCV)
            target_column: Column to use as target
            test_size: Proportion of data to use for testing
            random_state: Random state for reproducibility
            epochs: Number of training epochs
            batch_size: Batch size for training
            hidden_size: Size of hidden layers
            dropout_rate: Dropout rate
            learning_rate: Learning rate
            file_format: File format ('csv' or 'parquet')
            data_dir: Data directory (if None, use config)
            use_gpu: Whether to use GPU for training (if available)

        Returns:
            Dictionary with training results or None if training failed
        """
        try:
            # Try to import PyTorch and PyTorch Forecasting
            try:
                import torch
                import pytorch_lightning as pl
                from pytorch_forecasting import TemporalFusionTransformer, TimeSeriesDataSet
                from pytorch_forecasting.data import GroupNormalizer
                from pytorch_forecasting.metrics import QuantileLoss
                from pytorch_lightning.callbacks import EarlyStopping, LearningRateMonitor
                from pytorch_lightning.loggers import TensorBoardLogger

                # Try to import GPU configuration utilities
                try:
                    from utils.torch_gpu_config import get_gpu_info
                    # Check GPU availability
                    gpu_info = get_gpu_info()
                    gpu_available = gpu_info['gpu_available']
                    if gpu_available and use_gpu:
                        logger.info(f"GPU is available: {gpu_info['gpu_devices']}")
                        logger.info("Using GPU for TFT model training")
                    else:
                        logger.warning("No GPU available or use_gpu=False, using CPU for training")
                except ImportError:
                    # If tf_gpu_config is not available, check GPU directly with PyTorch
                    gpu_available = torch.cuda.is_available() and use_gpu
                    if gpu_available:
                        logger.info(f"GPU is available: {torch.cuda.get_device_name(0)}")
                        logger.info("Using GPU for TFT model training")
                    else:
                        logger.warning("No GPU available or use_gpu=False, using CPU for training")
            except ImportError as e:
                logger.error(f"Required packages not installed: {str(e)}")
                logger.error("Please install PyTorch and PyTorch Forecasting: pip install torch pytorch_forecasting pytorch_lightning")
                return None

            # Load data
            df = self.load_data(symbol, timeframe, file_format, data_dir)
            if df is None:
                return None

            # Ensure time column exists and is datetime
            if 'time' not in df.columns:
                logger.error("Time column not found in data")
                return None

            df['time'] = pd.to_datetime(df['time'])

            # Set default feature columns if not provided
            if feature_columns is None:
                feature_columns = ['open', 'high', 'low', 'close', 'tick_volume']

            # Ensure all required columns exist
            for col in feature_columns + [target_column]:
                if col not in df.columns:
                    logger.error(f"Column '{col}' not found in data")
                    return None

            # Add time index for TFT
            df['time_idx'] = range(len(df))
            df['group'] = 0  # Single group for now

            # Create TimeSeriesDataSet
            max_encoder_length = sequence_length
            max_prediction_length = 1  # Single-step prediction

            training_cutoff = int(len(df) * (1 - test_size))

            # Create training dataset
            training = TimeSeriesDataSet(
                data=df[:training_cutoff],
                time_idx="time_idx",
                target=target_column,
                group_ids=["group"],
                min_encoder_length=max_encoder_length // 2,
                max_encoder_length=max_encoder_length,
                min_prediction_length=1,
                max_prediction_length=max_prediction_length,
                static_categoricals=[],
                static_reals=[],
                time_varying_known_categoricals=[],
                time_varying_known_reals=[],
                time_varying_unknown_categoricals=[],
                time_varying_unknown_reals=feature_columns,
                target_normalizer=GroupNormalizer(
                    groups=["group"], transformation="softplus"
                ),
                add_relative_time_idx=True,
                add_target_scales=True,
                add_encoder_length=True,
                randomize_length=True,
                random_state=random_state,
            )

            # Create validation dataset
            validation = TimeSeriesDataSet.from_dataset(
                training, df[training_cutoff:], predict=False, stop_randomization=True
            )

            # Create data loaders
            train_dataloader = training.to_dataloader(
                train=True, batch_size=batch_size, num_workers=0
            )
            val_dataloader = validation.to_dataloader(
                train=False, batch_size=batch_size, num_workers=0
            )

            # Get TFT configuration
            tft_config = get_tft_config(
                timeframe=timeframe,
                terminal_id="1",  # Default terminal ID
                symbol=symbol,
                input_dim=len(feature_columns),
                sequence_length=sequence_length,
                custom_params={
                    "hidden_size": hidden_size,
                    "dropout_rate": dropout_rate,
                    "learning_rate": learning_rate,
                    "epochs": epochs,
                    "batch_size": batch_size
                }
            )

            # Create TFT model
            tft = TemporalFusionTransformer.from_dataset(
                training,
                learning_rate=learning_rate,
                hidden_size=hidden_size,
                attention_head_size=tft_config.get("attention_head_size", 4),
                dropout=dropout_rate,
                hidden_continuous_size=tft_config.get("hidden_continuous_size", 16),
                loss=QuantileLoss(),
                log_interval=10,
                reduce_on_plateau_patience=3,
            )

            # Create PyTorch Lightning trainer
            logger_dir = Path("lightning_logs")
            logger_dir.mkdir(parents=True, exist_ok=True)

            logger_name = f"{model_name}_{symbol}_{timeframe}"

            # Configure GPU settings for PyTorch Lightning
            gpu_settings = {}
            if torch.cuda.is_available() and use_gpu:
                # Use GPU
                if hasattr(pl.Trainer, 'devices'):  # PyTorch Lightning >= 1.6
                    gpu_settings = {
                        'accelerator': 'gpu',
                        'devices': 1,
                    }
                else:  # PyTorch Lightning < 1.6
                    gpu_settings = {
                        'gpus': 1,
                    }

                # Set precision to 16 if GPU supports it for faster training
                if torch.cuda.get_device_capability()[0] >= 7:  # Volta or newer architecture
                    gpu_settings['precision'] = 16
                    logger.info("Using mixed precision (16-bit) for faster training")
            else:
                # Use CPU
                if hasattr(pl.Trainer, 'devices'):  # PyTorch Lightning >= 1.6
                    gpu_settings = {
                        'accelerator': 'cpu',
                    }
                else:  # PyTorch Lightning < 1.6
                    gpu_settings = {
                        'gpus': 0,
                    }

            # Create trainer with appropriate GPU settings
            trainer = pl.Trainer(
                max_epochs=epochs,
                gradient_clip_val=0.1,
                limit_train_batches=50,  # Comment in for fast epoch training
                callbacks=[
                    EarlyStopping(
                        monitor="val_loss", patience=10, mode="min"
                    ),
                    LearningRateMonitor(logging_interval="epoch"),
                ],
                logger=TensorBoardLogger(logger_dir, name=logger_name),
                **gpu_settings
            )

            # Train model
            logger.info(f"Training TFT model for {symbol} {timeframe}")
            trainer.fit(
                tft,
                train_dataloaders=train_dataloader,
                val_dataloaders=val_dataloader,
            )

            # Evaluate model
            best_model_path = trainer.checkpoint_callback.best_model_path
            best_tft = TemporalFusionTransformer.load_from_checkpoint(best_model_path)

            # Make predictions
            predictions = best_tft.predict(val_dataloader, return_y=True)

            # Extract predictions and actual values
            y_pred = predictions.output.detach().cpu().numpy()
            y_true = predictions.y.detach().cpu().numpy()

            # Calculate metrics
            mse = np.mean((y_pred - y_true) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y_pred - y_true))

            # Calculate R² (coefficient of determination)
            y_mean = np.mean(y_true)
            ss_total = np.sum((y_true - y_mean) ** 2)
            ss_residual = np.sum((y_true - y_pred) ** 2)
            r2 = 1 - (ss_residual / ss_total)

            logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

            # Save model
            model_dir = Path(self.model_dir) / f"{model_name}_{symbol}_{timeframe}"
            model_dir.mkdir(parents=True, exist_ok=True)

            model_path = model_dir / "model.ckpt"
            trainer.save_checkpoint(model_path)

            # Save configuration
            config = {
                'model_type': 'tft',
                'symbol': symbol,
                'timeframe': timeframe,
                'sequence_length': sequence_length,
                'feature_columns': feature_columns,
                'target_column': target_column,
                'hidden_size': hidden_size,
                'dropout_rate': dropout_rate,
                'learning_rate': learning_rate,
                'gpu_used': torch.cuda.is_available() and use_gpu,
                'gpu_info': {
                    'available': torch.cuda.is_available(),
                    'device_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None',
                    'device_count': torch.cuda.device_count(),
                    'pytorch_version': torch.__version__,
                },
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2)
                }
            }

            # Save configuration
            import json
            with open(model_dir / "config.json", "w") as f:
                json.dump(config, f, indent=4)

            logger.info(f"Model saved to {model_dir}")

            return {
                'model': best_tft,
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2)
                },
                'config': config
            }

        except Exception as e:
            logger.error(f"Error training TFT model: {str(e)}")
            return None
