"""
Configuration file for the Temporal Fusion Transformer (TFT) model.
This file contains default configurations for different timeframes.
"""
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

def get_tft_config(
    timeframe: str = 'M5',
    terminal_id: str = '1',
    symbol: str = 'BTCUSD.a',
    input_dim: int = 5,
    sequence_length: int = 60,
    custom_params: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Get the configuration for the TFT model based on the timeframe.

    Args:
        timeframe: Trading timeframe (M5, M15, M30, H1, H4)
        terminal_id: Terminal ID
        symbol: Trading symbol
        input_dim: Number of input features
        sequence_length: Length of input sequences
        custom_params: Custom parameters to override defaults

    Returns:
        Dict containing the model configuration
    """
    # Base configuration common to all timeframes
    base_config = {
        'model_name': 'tft',
        'timeframe': timeframe,
        'terminal_id': terminal_id,
        'symbol': symbol,
        'input_dim': input_dim,
        'output_dim': 1,
        'sequence_length': sequence_length,
        'models_base_path': 'models'
    }

    # Timeframe-specific configurations
    timeframe_configs = {
        'M5': {
            'hidden_size': 64,  # Optimal for M5 timeframe
            'attention_head_size': 4,
            'dropout_rate': 0.1,
            'num_lstm_layers': 2,  # Optimal for M5 timeframe
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 5,  # Optimal for TFT models
            'patience': 15,
        },
        'M15': {
            'hidden_size': 128,
            'attention_head_size': 4,
            'dropout_rate': 0.15,
            'num_lstm_layers': 2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 5,  # Optimal for TFT models
            'patience': 15,
        },
        'M30': {
            'hidden_size': 128,
            'attention_head_size': 4,
            'dropout_rate': 0.15,
            'num_lstm_layers': 2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 5,  # Optimal for TFT models
            'patience': 15,
        },
        'H1': {
            'hidden_size': 128,
            'attention_head_size': 4,
            'dropout_rate': 0.2,
            'num_lstm_layers': 2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 5,  # Optimal for TFT models
            'patience': 15,
        },
        'H4': {
            'hidden_size': 128,
            'attention_head_size': 4,
            'dropout_rate': 0.2,
            'num_lstm_layers': 2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 5,  # Optimal for TFT models
            'patience': 15,
        }
    }

    # Get the configuration for the specified timeframe
    if timeframe in timeframe_configs:
        config = {**base_config, **timeframe_configs[timeframe]}
    else:
        logger.warning(f"No specific configuration for timeframe {timeframe}, using M5 configuration")
        config = {**base_config, **timeframe_configs['M5']}

    # Override with custom parameters if provided
    if custom_params:
        config.update(custom_params)

    return config
