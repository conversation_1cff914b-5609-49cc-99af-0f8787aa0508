"""
Optimized data processing manager.
Handles efficient data loading, preprocessing, and feature engineering with chunking and parallelization.
"""
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Union, Optional, Tuple, Callable, Any
from datetime import datetime, timedelta
import gc
import os
import time
from functools import partial
from concurrent.futures import ThreadPoolExecutor
import traceback
import pickle
from pathlib import Path
from sklearn.preprocessing import MinMaxScaler
from config.unified_config import UnifiedConfigManager as ConfigurationManager

logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Optimized data processing manager that handles efficient data loading,
    preprocessing, and feature engineering with chunking and parallelization.

    Features:
    - Chunked data loading to prevent memory issues
    - Parallel data processing for performance
    - Incremental data updates for real-time processing
    - Data caching for frequently accessed datasets
    - Memory-efficient operations
    """

    def __init__(self,
                 config_manager: ConfigurationManager,
                 chunk_size: int = 10000,
                 max_workers: int = None,
                 enable_caching: bool = True,
                 memory_limit_mb: int = 2048,
                 memory_manager = None):
        """
        Initialize the data processor.

        Args:
            config_manager: Instance of ConfigurationManager
            chunk_size: Size of chunks for data processing
            max_workers: Maximum number of worker threads (defaults to CPU count)
            enable_caching: Whether to enable data caching
            memory_limit_mb: Memory limit in MB for data processing
            memory_manager: Optional memory manager for integration
        """
        self.config_manager = config_manager
        self.chunk_size = chunk_size
        self.max_workers = max_workers or os.cpu_count()
        self.enable_caching = enable_caching
        self.memory_limit_mb = memory_limit_mb
        self.memory_manager = memory_manager

        # Get cache directory from config using standardized paths
        try:
            # Try to use the unified config manager for standardized paths
            from config.unified_config import UnifiedConfigManager
            config_manager = UnifiedConfigManager()
            self.cache_dir = config_manager.get_data_path() / "cache"
        except (ImportError, AttributeError):
            # Fallback to original path construction if unified config not available
            base_path = Path(self.config_manager.get_data_base_path())
            self.cache_dir = base_path / "cache"

        # Create cache directory if it doesn't exist
        if self.enable_caching:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Cache directory set to: {self.cache_dir}")
        else:
            logger.info("Caching is disabled.")

        # Initialize cache
        self.data_cache = {}

        # Register with memory manager if provided
        if self.memory_manager and hasattr(self.memory_manager, 'register_component'):
            try:
                self.memory_manager.register_component(
                    "data_processor",
                    cleanup_handlers={
                        "light": lambda component_name: self.clear_cache(specific_key=None) or 0,
                        "moderate": lambda component_name: self.clear_cache(specific_key=None) or 0,
                        "aggressive": lambda component_name: (self.clear_cache(specific_key=None), gc.collect(), 0)[2]
                    }
                )
                logger.info("DataProcessor registered with memory manager")
            except Exception as e:
                logger.warning(f"Failed to register DataProcessor with memory manager: {str(e)}")

        logger.info(f"DataProcessor initialized with chunk_size={chunk_size}, max_workers={self.max_workers}")

    def process_dataframe(
        self,
        df: pd.DataFrame,
        pipeline: List[Callable],
        chunk_processing: bool = True,
        parallel: bool = True,
        cache_key: Optional[str] = None,
        force_reprocess: bool = False
    ) -> pd.DataFrame:
        """
        Process a dataframe using a pipeline of functions with chunking and parallelization.

        Args:
            df: Input dataframe
            pipeline: List of processing functions that take a dataframe and return a dataframe
            chunk_processing: Whether to process data in chunks
            parallel: Whether to process chunks in parallel
            cache_key: Key for caching the processed result
            force_reprocess: Whether to force reprocessing even if cached result exists

        Returns:
            pd.DataFrame: Processed dataframe
        """
        start_time = time.time()
        logger.info(f"Processing dataframe with shape {df.shape}")

        # Check cache
        if cache_key and self.enable_caching and not force_reprocess:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info(f"Using cached result for {cache_key}")
                return cached_result

        # Check memory limit
        memory_usage_mb = df.memory_usage(deep=True).sum() / (1024 * 1024)
        logger.info(f"Input dataframe memory usage: {memory_usage_mb:.2f} MB")

        if memory_usage_mb > self.memory_limit_mb:
            logger.warning(f"Dataframe memory usage ({memory_usage_mb:.2f} MB) exceeds limit ({self.memory_limit_mb} MB)")
            if not chunk_processing:
                logger.warning("Enabling chunk processing due to memory constraints")
                chunk_processing = True

        # Process dataframe
        if chunk_processing:
            result = self._process_in_chunks(df, pipeline, parallel)
        else:
            result = self._apply_pipeline(df, pipeline)

        # Cache result
        if cache_key and self.enable_caching:
            self._store_in_cache(cache_key, result)

        elapsed_time = time.time() - start_time
        logger.info(f"Processed dataframe in {elapsed_time:.2f}s, result shape: {result.shape}")

        return result

    def _process_in_chunks(
        self,
        df: pd.DataFrame,
        pipeline: List[Callable],
        parallel: bool = True
    ) -> pd.DataFrame:
        """
        Process a dataframe in chunks.

        Args:
            df: Input dataframe
            pipeline: List of processing functions
            parallel: Whether to process chunks in parallel

        Returns:
            pd.DataFrame: Processed dataframe
        """
        total_rows = len(df)
        chunk_count = (total_rows + self.chunk_size - 1) // self.chunk_size  # Ceiling division

        logger.info(f"Processing {total_rows} rows in {chunk_count} chunks")

        if parallel and chunk_count > 1:
            # Process chunks in parallel
            with ThreadPoolExecutor(max_workers=min(self.max_workers, chunk_count)) as executor:
                futures = []
                for i in range(0, total_rows, self.chunk_size):
                    chunk = df.iloc[i:i+self.chunk_size].copy()
                    futures.append(executor.submit(self._apply_pipeline, chunk, pipeline))

                # Collect results
                chunks = []
                for future in futures:
                    try:
                        result = future.result()
                        chunks.append(result)
                    except Exception as e:
                        logger.error(f"Error processing chunk: {str(e)}\n{traceback.format_exc()}")

                # Concatenate chunks
                if chunks:
                    result = pd.concat(chunks, axis=0, ignore_index=False)
                    return result
                else:
                    raise ValueError("All chunks failed to process")
        else:
            # Process chunks sequentially
            chunks = []
            for i in range(0, total_rows, self.chunk_size):
                chunk = df.iloc[i:i+self.chunk_size].copy()
                try:
                    processed_chunk = self._apply_pipeline(chunk, pipeline)
                    chunks.append(processed_chunk)

                    # Clean up to release memory
                    del chunk
                    gc.collect()
                except Exception as e:
                    logger.error(f"Error processing chunk: {str(e)}\n{traceback.format_exc()}")

            # Concatenate chunks
            if chunks:
                result = pd.concat(chunks, axis=0, ignore_index=False)
                return result
            else:
                raise ValueError("All chunks failed to process")

    def _apply_pipeline(self, df: pd.DataFrame, pipeline: List[Callable]) -> pd.DataFrame:
        """
        Apply a pipeline of functions to a dataframe.

        Args:
            df: Input dataframe
            pipeline: List of processing functions

        Returns:
            pd.DataFrame: Processed dataframe
        """
        result = df.copy()
        for i, func in enumerate(pipeline):
            try:
                result = func(result)
            except Exception as e:
                logger.error(f"Error applying function {i}: {str(e)}\n{traceback.format_exc()}")
                raise
        return result

    def process_incremental_data(
        self,
        new_data: pd.DataFrame,
        existing_data: Optional[pd.DataFrame],
        pipeline: List[Callable],
        drop_duplicates: bool = True,
        max_rows: Optional[int] = None
    ) -> pd.DataFrame:
        """
        Process incremental data and merge with existing data.

        Args:
            new_data: New data to process
            existing_data: Existing processed data
            pipeline: List of processing functions
            drop_duplicates: Whether to drop duplicate rows
            max_rows: Maximum number of rows to keep in the result

        Returns:
            pd.DataFrame: Updated processed data
        """
        # Process new data
        processed_new_data = self.process_dataframe(
            new_data,
            pipeline,
            chunk_processing=True,
            parallel=True
        )

        # Merge with existing data
        if existing_data is not None and not existing_data.empty:
            result = pd.concat([existing_data, processed_new_data], axis=0)

            # Drop duplicates if requested
            if drop_duplicates:
                result = result.drop_duplicates()

            # Sort by index
            if isinstance(result.index, pd.DatetimeIndex):
                result = result.sort_index()

            # Limit rows if requested
            if max_rows and len(result) > max_rows:
                result = result.iloc[-max_rows:]
        else:
            result = processed_new_data

        return result

    def preprocess_features(
        self,
        df: pd.DataFrame,
        feature_cols: List[str],
        target_col: Optional[str] = None,
        sequence_length: int = 60,
        step_size: int = 1,
        normalization: str = 'standard',
        cache_key: Optional[str] = None
    ) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        Preprocess features for model input with sequences.

        Args:
            df: Input dataframe
            feature_cols: List of feature columns
            target_col: Target column (None for inference)
            sequence_length: Length of sequences
            step_size: Step size for sliding window
            normalization: Normalization method ('standard', 'minmax', or None)
            cache_key: Key for caching the processed result

        Returns:
            Tuple[np.ndarray, Optional[np.ndarray]]: (X, y) arrays
        """
        # Check cache
        if cache_key and self.enable_caching:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info(f"Using cached result for {cache_key}")
                return cached_result

        # Extract features
        X_data = df[feature_cols].values

        # Normalize features
        if normalization == 'standard':
            # Standardize to mean=0, std=1
            mean = np.mean(X_data, axis=0)
            std = np.std(X_data, axis=0)
            X_data = (X_data - mean) / (std + 1e-8)  # Add small epsilon to avoid division by zero
        elif normalization == 'minmax':
            # Scale to range [0, 1]
            min_vals = np.min(X_data, axis=0)
            max_vals = np.max(X_data, axis=0)
            X_data = (X_data - min_vals) / (max_vals - min_vals + 1e-8)

        # Create sequences
        X_sequences = []
        y_values = []

        for i in range(0, len(X_data) - sequence_length, step_size):
            X_sequences.append(X_data[i:i+sequence_length])
            if target_col:
                y_values.append(df[target_col].iloc[i+sequence_length])

        X_sequences = np.array(X_sequences)

        if target_col:
            y_values = np.array(y_values)
            result = (X_sequences, y_values)
        else:
            result = (X_sequences, None)

        # Cache result
        if cache_key and self.enable_caching:
            self._store_in_cache(cache_key, result)

        return result

    def _get_from_cache(self, key: str) -> Any:
        """
        Get data from cache.

        Args:
            key: Cache key

        Returns:
            Any: Cached data or None if not found
        """
        # Check memory cache
        if key in self.data_cache:
            return self.data_cache[key]

        # Check disk cache
        if self.enable_caching:
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        result = pickle.load(f)
                    return result
                except Exception as e:
                    logger.error(f"Error loading from cache: {str(e)}")

        return None

    def _store_in_cache(self, key: str, data: Any) -> None:
        """
        Store data in cache.

        Args:
            key: Cache key
            data: Data to cache
        """
        # Store in memory cache
        self.data_cache[key] = data

        # Store in disk cache
        if self.enable_caching:
            cache_file = self.cache_dir / f"{key}.pkl"
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(data, f)
            except Exception as e:
                logger.error(f"Error storing in cache: {str(e)}")

    def clear_cache(self, key: Optional[str] = None) -> None:
        """
        Clear cache.

        Args:
            key: Specific cache key to clear (None for all)
        """
        if key:
            # Clear specific key
            if key in self.data_cache:
                del self.data_cache[key]

            if self.enable_caching:
                cache_file = self.cache_dir / f"{key}.pkl"
                if cache_file.exists():
                    cache_file.unlink()
        else:
            # Clear all cache
            self.data_cache.clear()

            if self.enable_caching:
                for cache_file in self.cache_dir.glob("*.pkl"):
                    cache_file.unlink()

        # Force garbage collection
        gc.collect()

    def get_cache_size(self) -> Dict[str, int]:
        """
        Get cache size.

        Returns:
            Dict[str, int]: Cache size information
        """
        memory_cache_size = len(self.data_cache)

        disk_cache_size = 0
        disk_cache_bytes = 0
        if self.enable_caching and self.cache_dir.exists():
            cache_files = list(self.cache_dir.glob("*.pkl"))
            disk_cache_size = len(cache_files)
            disk_cache_bytes = sum(f.stat().st_size for f in cache_files)

        return {
            "memory_cache_items": memory_cache_size,
            "disk_cache_items": disk_cache_size,
            "disk_cache_mb": disk_cache_bytes / (1024 * 1024)
        }

    def feature_extraction(
        self,
        df: pd.DataFrame,
        price_col: str = 'close',
        volume_col: Optional[str] = 'volume',
        window_sizes: List[int] = [5, 10, 20, 50, 100]
    ) -> pd.DataFrame:
        """
        Extract common technical indicators and features.

        Args:
            df: Input dataframe with price data
            price_col: Column name for price data
            volume_col: Column name for volume data (None if not available)
            window_sizes: List of window sizes for indicators

        Returns:
            pd.DataFrame: Dataframe with added features
        """
        result = df.copy()

        # Basic price features
        price = result[price_col]

        for window in window_sizes:
            # Moving averages
            result[f'ma_{window}'] = price.rolling(window=window).mean()

            # Standard deviation (volatility)
            result[f'std_{window}'] = price.rolling(window=window).std()

            # Exponential moving average
            result[f'ema_{window}'] = price.ewm(span=window, adjust=False).mean()

            # Relative Strength Index (RSI)
            delta = price.diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=window).mean()
            avg_loss = loss.rolling(window=window).mean()
            rs = avg_gain / (avg_loss + 1e-10)  # Avoid division by zero
            result[f'rsi_{window}'] = 100 - (100 / (1 + rs))

            # Rate of change
            result[f'roc_{window}'] = price.pct_change(periods=window) * 100

            # Bollinger Bands
            if f'ma_{window}' in result and f'std_{window}' in result:
                result[f'bb_upper_{window}'] = result[f'ma_{window}'] + 2 * result[f'std_{window}']
                result[f'bb_lower_{window}'] = result[f'ma_{window}'] - 2 * result[f'std_{window}']

            # MACD
            if window == 26:  # Traditional MACD parameters
                ema_12 = price.ewm(span=12, adjust=False).mean()
                ema_26 = price.ewm(span=26, adjust=False).mean()
                result['macd'] = ema_12 - ema_26
                result['macd_signal'] = result['macd'].ewm(span=9, adjust=False).mean()
                result['macd_hist'] = result['macd'] - result['macd_signal']

        # Volume-based features
        if volume_col and volume_col in df.columns:
            volume = result[volume_col]

            # Volume moving average
            for window in window_sizes:
                result[f'volume_ma_{window}'] = volume.rolling(window=window).mean()

            # Price-volume trend
            result['pvt'] = (price.pct_change() * volume).cumsum()

            # On-balance volume
            obv = (np.sign(price.diff()) * volume).fillna(0).cumsum()
            result['obv'] = obv

        # Price momentum
        for period in [1, 3, 5, 10, 20]:
            result[f'return_{period}d'] = price.pct_change(periods=period)

        # Drop rows with NaN values
        result = result.dropna()

        return result