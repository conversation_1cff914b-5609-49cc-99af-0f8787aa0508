"""
Test script for the LSTMModel class.
"""
import torch
import numpy as np
from models.pytorch_lstm_model import LSTMModel

def test_lstm_model():
    """Test the LSTMModel class."""
    # Create a model
    input_dim = 5
    hidden_dim = 64
    num_layers = 2
    output_dim = 1
    dropout_rate = 0.2
    
    model = LSTMModel(
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        output_dim=output_dim,
        dropout_rate=dropout_rate
    )
    
    # Create some test data
    batch_size = 1
    sequence_length = 60
    X = np.random.random((batch_size, sequence_length, input_dim)).astype(np.float32)
    
    # Test the predict method
    try:
        predictions = model.predict(X)
        print(f"Predictions shape: {predictions.shape}")
        print(f"Predictions: {predictions}")
        print("Test passed!")
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    test_lstm_model()
