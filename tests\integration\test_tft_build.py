"""
Simple script to test building the TFT model without training.
"""
import logging
import os
import numpy as np
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_tft_build.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress TensorFlow warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"  # Force CPU

# Import the TFT model and configuration
from models.tft_model import TFTModel
from config.tft_model_config import get_tft_config

def test_tft_build():
    """
    Test building the TFT model without training.
    """
    logger.info("Testing TFT model build")
    
    # Create synthetic data
    sequence_length = 60
    input_dim = 5
    X = np.random.random((100, sequence_length, input_dim))
    
    # Get model configuration
    model_config = get_tft_config(
        timeframe='M5',
        terminal_id='1',
        symbol='BTCUSD.a',
        input_dim=input_dim,
        sequence_length=sequence_length,
        custom_params={
            'hidden_size': 16,  # Very small for testing
            'attention_head_size': 2,  # Very small for testing
            'num_lstm_layers': 1,  # Very small for testing
            'FEATURE_COLUMNS': ['open', 'high', 'low', 'close', 'volume']
        }
    )
    
    # Initialize model
    logger.info(f"Initializing TFT model with config: {model_config}")
    model = TFTModel(model_config)
    
    # Build model
    logger.info("Building TFT model")
    model.build()
    
    # Test prediction
    logger.info("Testing prediction")
    y_pred = model.predict(X[:1])
    logger.info(f"Prediction shape: {y_pred.shape}")
    
    logger.info("TFT model build test completed successfully")

if __name__ == "__main__":
    test_tft_build()
