"""
Model Trainer Module

This module provides utilities for training different types of models
for the trading bot.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import pickle
import json
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from sklearn.preprocessing import StandardScaler
# Removed train_test_split import - using proper temporal splitting instead

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import configuration manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.unified_config import UnifiedConfigManager

class ModelTrainer:
    """Base class for training models."""

    def __init__(self, config_manager: Optional[UnifiedConfigManager] = None):
        """
        Initialize the ModelTrainer.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or UnifiedConfigManager()

        # Create model directory if it doesn't exist
        self.model_dir = Path(self.config_manager.get_model_directory())
        self.model_dir.mkdir(parents=True, exist_ok=True)

    def load_data(
        self,
        symbol: str,
        timeframe: str,
        file_format: str = 'csv',
        data_dir: Optional[str] = None
    ) -> Optional[pd.DataFrame]:
        """
        Load data for training.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            file_format: File format ('csv' or 'parquet')
            data_dir: Data directory (if None, use config)

        Returns:
            DataFrame with data or None if loading failed
        """
        try:
            # Get data directory
            if data_dir is None:
                data_dir = self.config_manager.get_data_directory()

            # Construct file path
            file_path = Path(data_dir) / f"{symbol}_{timeframe}.{file_format}"

            # Load data
            if file_format.lower() == 'csv':
                df = pd.read_csv(file_path)
            elif file_format.lower() == 'parquet':
                df = pd.read_parquet(file_path)
            else:
                logger.error(f"Unsupported file format: {file_format}")
                return None

            # Convert time column to datetime if it exists
            if 'time' in df.columns:
                df['time'] = pd.to_datetime(df['time'])

            return df

        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return None

    def preprocess_data(
        self,
        df: pd.DataFrame,
        sequence_length: int = 60,
        target_column: str = 'close',
        feature_columns: Optional[List[str]] = None,
        test_size: float = 0.2,
        random_state: int = 42
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, StandardScaler, StandardScaler]:
        """
        Preprocess data for training.

        Args:
            df: DataFrame with data
            sequence_length: Length of input sequences
            target_column: Column to use as target
            feature_columns: Columns to use as features (if None, use OHLCV)
            test_size: Proportion of data to use for testing
            random_state: Random state for reproducibility

        Returns:
            Tuple of (X_train, X_test, y_train, y_test, X_scaler, y_scaler)
        """
        # Set default feature columns if not provided
        if feature_columns is None:
            feature_columns = ['open', 'high', 'low', 'close', 'tick_volume']

        # Extract features and target
        X = df[feature_columns].values
        y = df[target_column].values.reshape(-1, 1)

        # Scale features and target
        X_scaler = StandardScaler()
        y_scaler = StandardScaler()

        X_scaled = X_scaler.fit_transform(X)
        y_scaled = y_scaler.fit_transform(y)

        # Create sequences
        X_sequences = []
        y_sequences = []

        for i in range(len(X_scaled) - sequence_length):
            X_sequences.append(X_scaled[i:i+sequence_length])
            y_sequences.append(y_scaled[i+sequence_length])

        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)

        # Split into train and test sets using proper temporal splitting
        split_idx = int(len(X_sequences) * (1 - test_size))
        X_train = X_sequences[:split_idx]
        X_test = X_sequences[split_idx:]
        y_train = y_sequences[:split_idx]
        y_test = y_sequences[split_idx:]

        return X_train, X_test, y_train, y_test, X_scaler, y_scaler

    def save_model(
        self,
        model: Any,
        model_name: str,
        model_type: str,
        symbol: str,
        timeframe: str,
        X_scaler: StandardScaler,
        y_scaler: StandardScaler,
        config: Dict[str, Any]
    ) -> bool:
        """
        Save a trained model and its configuration.

        Args:
            model: Trained model
            model_name: Name of the model
            model_type: Type of the model (e.g., 'lstm', 'arima', 'tft')
            symbol: Trading symbol
            timeframe: Timeframe
            X_scaler: Feature scaler
            y_scaler: Target scaler
            config: Model configuration

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create model directory
            model_path = self.model_dir / f"{model_name}_{symbol}_{timeframe}"
            model_path.mkdir(parents=True, exist_ok=True)

            # Save model based on type
            if model_type == 'lstm':
                # Save PyTorch LSTM model
                model_file = model_path / "model.pt"
                import torch
                torch.save(model.state_dict(), model_file)

                # Save scalers
                scaler_file = model_path / "scalers.pkl"
                with open(scaler_file, 'wb') as f:
                    pickle.dump({'X_scaler': X_scaler, 'y_scaler': y_scaler}, f)

            elif model_type == 'tft':
                # For PyTorch models, use PyTorch Lightning's save_checkpoint
                try:
                    import pytorch_lightning as pl
                    if isinstance(model, pl.LightningModule):
                        model_file = model_path / "model.ckpt"
                        trainer = pl.Trainer()
                        trainer.save_checkpoint(model_file)
                    else:
                        # Try to save using the model's save method
                        model_file = model_path / "model.pt"
                        model.save(model_file)
                except ImportError:
                    logger.warning("PyTorch Lightning not installed. Trying alternative save method.")
                    model_file = model_path / "model.pt"
                    try:
                        import torch
                        torch.save(model.state_dict(), model_file)
                    except (ImportError, AttributeError):
                        logger.error("Failed to save TFT model. PyTorch not installed or model doesn't support state_dict().")
                        return False

            elif model_type == 'arima':
                # Save ARIMA model
                model_file = model_path / "model.pkl"
                with open(model_file, 'wb') as f:
                    pickle.dump(model, f)

                # Save scalers if they exist
                if X_scaler is not None and y_scaler is not None:
                    scaler_file = model_path / "scalers.pkl"
                    with open(scaler_file, 'wb') as f:
                        pickle.dump({'X_scaler': X_scaler, 'y_scaler': y_scaler}, f)

            else:
                # Generic save for other model types
                model_file = model_path / "model.pkl"
                with open(model_file, 'wb') as f:
                    pickle.dump(model, f)

                # Save scalers
                scaler_file = model_path / "scalers.pkl"
                with open(scaler_file, 'wb') as f:
                    pickle.dump({'X_scaler': X_scaler, 'y_scaler': y_scaler}, f)

            # Save configuration
            config_file = model_path / "config.json"
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)

            logger.info(f"Saved model {model_name} for {symbol} {timeframe}")
            return True

        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
            return False

    def load_model(
        self,
        model_name: str,
        symbol: str,
        timeframe: str
    ) -> Tuple[Optional[Any], Optional[Dict[str, StandardScaler]], Optional[Dict[str, Any]]]:
        """
        Load a trained model and its configuration.

        Args:
            model_name: Name of the model
            symbol: Trading symbol
            timeframe: Timeframe

        Returns:
            Tuple of (model, scalers, config) or (None, None, None) if loading failed
        """
        try:
            # Get model directory
            model_path = self.model_dir / f"{model_name}_{symbol}_{timeframe}"

            if not model_path.exists():
                logger.error(f"Model {model_name} for {symbol} {timeframe} not found")
                return None, None, None

            # Load configuration
            config_file = model_path / "config.json"
            with open(config_file, 'r') as f:
                config = json.load(f)

            # Get model type
            model_type = config.get('model_type', '').lower()

            # Load model based on type
            if model_type == 'lstm':
                # Load PyTorch LSTM model
                try:
                    import torch
                    from models.pytorch_lstm_model import LSTMModel

                    # Create a new model instance with the same configuration
                    model = LSTMModel(config)
                    model.build()  # Initialize the model architecture

                    # Load the saved state dictionary
                    model_file = model_path / "model.pt"
                    model.load_state_dict(torch.load(str(model_file)))
                    model.eval()  # Set to evaluation mode

                    # Load scalers
                    scaler_file = model_path / "scalers.pkl"
                    with open(scaler_file, 'rb') as f:
                        scalers = pickle.load(f)
                except ImportError:
                    logger.error("PyTorch not installed. Cannot load LSTM model.")
                    return None, None, None
                except Exception as e:
                    logger.error(f"Error loading LSTM model: {str(e)}")
                    return None, None, None

            elif model_type == 'tft':
                # Load PyTorch model
                try:
                    from pytorch_forecasting import TemporalFusionTransformer
                    model_file = model_path / "model.ckpt"

                    if model_file.exists():
                        model = TemporalFusionTransformer.load_from_checkpoint(str(model_file))
                    else:
                        # Try alternative file formats
                        model_file_pt = model_path / "model.pt"
                        if model_file_pt.exists():
                            import torch
                            model = torch.load(str(model_file_pt))
                        else:
                            logger.error(f"TFT model file not found at {model_file} or {model_file_pt}")
                            return None, None, None

                    # TFT models typically don't use separate scalers
                    scalers = {}
                except ImportError:
                    logger.error("PyTorch Forecasting not installed. Cannot load TFT model.")
                    return None, None, None

            elif model_type == 'arima':
                # Load ARIMA model
                model_file = model_path / "model.pkl"
                with open(model_file, 'rb') as f:
                    model = pickle.load(f)

                # Load scalers if they exist
                scalers = {}
                scaler_file = model_path / "scalers.pkl"
                if scaler_file.exists():
                    with open(scaler_file, 'rb') as f:
                        scalers = pickle.load(f)

            else:
                # Generic load for other model types
                model_file = model_path / "model.pkl"
                with open(model_file, 'rb') as f:
                    model = pickle.load(f)

                # Load scalers
                scalers = {}
                scaler_file = model_path / "scalers.pkl"
                if scaler_file.exists():
                    with open(scaler_file, 'rb') as f:
                        scalers = pickle.load(f)

            logger.info(f"Loaded model {model_name} for {symbol} {timeframe}")
            return model, scalers, config

        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return None, None, None
