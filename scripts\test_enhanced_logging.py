"""
Test script for the enhanced logging implementation.
"""
import os
import sys
import logging
import time
import threading
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_enhanced_logging():
    """Test the enhanced logging implementation."""
    try:
        from utils.enhanced_logging import (
            EnhancedLogger,
            LogLevel,
            LogCategory,
            LogContext,
            PerformanceMetric,
            debug,
            info,
            warning,
            error,
            critical,
            set_context,
            with_context,
            start_operation,
            complete_operation,
            get_performance_summary
        )
        
        logger.info("Testing enhanced logging implementation")
        
        # Get the singleton instance
        enhanced_logger = EnhancedLogger.get_instance()
        
        # Test 1: Basic logging
        logger.info("Test 1: Basic logging")
        
        debug("This is a debug message")
        info("This is an info message")
        warning("This is a warning message")
        error("This is an error message")
        critical("This is a critical message")
        
        # Test 2: Logging with context
        logger.info("\nTest 2: Logging with context")
        
        # Set context
        set_context(
            user_id="test_user",
            session_id="test_session",
            component="test_component",
            operation="test_operation",
            category=LogCategory.TRADING
        )
        
        # Log with context
        info("This is an info message with context")
        
        # Log with additional context
        info("This is an info message with additional context", 
             extra_field="extra_value", another_field=123)
        
        # Test 3: Performance tracking
        logger.info("\nTest 3: Performance tracking")
        
        # Start operation
        metric = start_operation("test_operation", input_size=100)
        
        # Simulate work
        time.sleep(0.5)
        
        # Complete operation
        complete_operation(metric)
        
        # Get performance summary
        summary = get_performance_summary()
        logger.info(f"Performance summary: {summary}")
        
        # Test 4: Decorator usage
        logger.info("\nTest 4: Decorator usage")
        
        # Define decorated function
        @with_context(component="test_component", operation="decorated_operation")
        def decorated_function(sleep_time=0.2):
            logger.info(f"Decorated function running, sleeping for {sleep_time}s")
            time.sleep(sleep_time)
            return "Decorated function result"
        
        # Call decorated function
        result = decorated_function()
        logger.info(f"Decorated function result: {result}")
        
        # Test 5: Error logging
        logger.info("\nTest 5: Error logging")
        
        # Log an error with exception info
        try:
            # Raise an exception
            raise ValueError("Test exception")
        except Exception as e:
            error("An error occurred", exc_info=True)
        
        # Test 6: Concurrent logging
        logger.info("\nTest 6: Concurrent logging")
        
        # Define a function that logs from a thread
        def log_from_thread(thread_id):
            # Set context for this thread
            set_context(
                correlation_id=f"thread-{thread_id}",
                component="thread_test",
                operation=f"thread_operation_{thread_id}"
            )
            
            # Log from thread
            info(f"Logging from thread {thread_id}")
            
            # Start and complete an operation
            metric = start_operation(f"thread_operation_{thread_id}")
            time.sleep(random.uniform(0.1, 0.3))
            complete_operation(metric)
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=log_from_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Get updated performance summary
        summary = get_performance_summary()
        logger.info(f"Updated performance summary: {summary}")
        
        # Test 7: Log file verification
        logger.info("\nTest 7: Log file verification")
        
        # Check if log files exist
        log_dir = Path("logs")
        application_log = log_dir / "application.log"
        error_log = log_dir / "error.log"
        structured_log = log_dir / "structured.json"
        
        logger.info(f"Application log exists: {application_log.exists()}")
        logger.info(f"Error log exists: {error_log.exists()}")
        logger.info(f"Structured log exists: {structured_log.exists()}")
        
        # Read a few lines from structured log if it exists
        if structured_log.exists():
            with open(structured_log, "r") as f:
                lines = f.readlines()
                logger.info(f"Structured log contains {len(lines)} entries")
                if lines:
                    logger.info(f"First structured log entry: {lines[0]}")
        
        logger.info("Enhanced logging test passed")
        return True
        
    except Exception as e:
        logger.error(f"Error testing enhanced logging: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_enhanced_logging():
        logger.info("Enhanced logging test passed")
        sys.exit(0)
    else:
        logger.error("Enhanced logging test failed")
        sys.exit(1)
