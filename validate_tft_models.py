#!/usr/bin/env python
"""
TFT Model Validation Script

This script validates TFT model training results and checks model availability.
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

def validate_tft_models():
    """Validate TFT model training results."""
    try:
        print("[INFO] Starting TFT model validation...")
        
        # Check if models exist
        timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
        existing_models = []
        model_metrics = {}
        
        for tf in timeframes:
            model_path = f'models/tft_BTCUSD.a_{tf}'
            if os.path.exists(model_path):
                existing_models.append(tf)
                print(f'[FOUND] TFT model found: {tf}')
                
                # Try to load metrics
                metrics_pattern = f'metrics/tft_BTCUSD.a_{tf}_*.json'
                import glob
                metrics_files = glob.glob(metrics_pattern)
                if metrics_files:
                    # Get the latest metrics file
                    latest_metrics = max(metrics_files, key=os.path.getctime)
                    try:
                        with open(latest_metrics, 'r') as f:
                            metrics = json.load(f)
                            model_metrics[tf] = metrics
                            r2 = metrics.get('r2', 0)
                            rmse = metrics.get('rmse', 0)
                            print(f'[METRICS] {tf}: R² = {r2:.4f}, RMSE = {rmse:.2f}')
                    except Exception as e:
                        print(f'[WARNING] Could not load metrics for {tf}: {e}')
            else:
                print(f'[MISSING] TFT model missing: {tf}')

        print(f'[INFO] Models available: {len(existing_models)}/5 timeframes')
        
        # Validate performance
        if len(existing_models) >= 3:
            print('[SUCCESS] TFT training SUCCESS - Sufficient models available!')
            print('[INFO] Expected Performance Range: R² = 0.48-0.53 (48-53% accuracy)')
            
            # Calculate average performance
            if model_metrics:
                avg_r2 = sum(m.get('r2', 0) for m in model_metrics.values()) / len(model_metrics)
                avg_rmse = sum(m.get('rmse', 0) for m in model_metrics.values()) / len(model_metrics)
                print(f'[PERFORMANCE] Average R² = {avg_r2:.4f}, Average RMSE = {avg_rmse:.2f}')
                
                # Check if performance meets expectations
                if avg_r2 >= 0.45:
                    print('[EXCELLENT] Performance exceeds expectations!')
                elif avg_r2 >= 0.30:
                    print('[GOOD] Performance meets minimum requirements')
                else:
                    print('[WARNING] Performance below expectations')
            
            print('[NOTE] TFT models are experimental and may need further optimization')
            
            # Save validation report
            validation_report = {
                'timestamp': datetime.now().isoformat(),
                'total_models': len(timeframes),
                'successful_models': len(existing_models),
                'success_rate': len(existing_models) / len(timeframes),
                'model_metrics': model_metrics,
                'validation_status': 'SUCCESS'
            }
            
            os.makedirs('validation_reports', exist_ok=True)
            report_file = f'validation_reports/tft_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_file, 'w') as f:
                json.dump(validation_report, f, indent=4)
            print(f'[REPORT] Validation report saved to {report_file}')
            
            return 0
        else:
            print('[WARNING] Insufficient models trained')
            print('[HELP] At least 3 out of 5 timeframes should be successfully trained')
            
            # Save failure report
            validation_report = {
                'timestamp': datetime.now().isoformat(),
                'total_models': len(timeframes),
                'successful_models': len(existing_models),
                'success_rate': len(existing_models) / len(timeframes),
                'model_metrics': model_metrics,
                'validation_status': 'INSUFFICIENT_MODELS'
            }
            
            os.makedirs('validation_reports', exist_ok=True)
            report_file = f'validation_reports/tft_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_file, 'w') as f:
                json.dump(validation_report, f, indent=4)
            print(f'[REPORT] Validation report saved to {report_file}')
            
            return 1

    except Exception as e:
        print(f'[ERROR] VALIDATION FAILED: {str(e)}')
        
        # Save error report
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'validation_status': 'ERROR',
            'error_message': str(e)
        }
        
        os.makedirs('validation_reports', exist_ok=True)
        report_file = f'validation_reports/tft_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(validation_report, f, indent=4)
        print(f'[REPORT] Error report saved to {report_file}')
        
        return 1

if __name__ == "__main__":
    exit_code = validate_tft_models()
    sys.exit(exit_code)
