"""
Test script for the integration between the Intelligent Cache System and Enhanced Memory Manager.
"""
import os
import sys
import logging
import time
import numpy as np
import pandas as pd
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_cache_memory_integration():
    """Test the integration between the Intelligent Cache System and Enhanced Memory Manager."""
    try:
        # Import necessary modules
        from utils.enhanced_memory_manager import (
            enhanced_memory_manager,
            get_memory_stats,
            check_memory,
            cleanup_memory,
            CleanupLevel
        )
        
        from utils.intelligent_cache import (
            IntelligentCache,
            CacheTier,
            get_cache,
            get_global_cache
        )
        
        logger.info("Testing cache-memory integration")
        
        # Get initial memory stats
        initial_stats = get_memory_stats()
        logger.info(f"Initial memory stats: {initial_stats['process']['percent']:.2f}% used")
        
        # Get global cache
        cache = get_global_cache()
        
        # Store some data in cache
        logger.info("Storing data in cache")
        
        # Small item
        cache.set("small_item", "x" * 1024)  # 1KB
        
        # Medium item
        medium_data = {"data": [i for i in range(10000)]}
        cache.set("medium_item", medium_data)  # ~100KB
        
        # Large item
        large_data = np.random.random((1000, 100)).tolist()  # ~800KB
        cache.set("large_item", large_data)
        
        # Very large item
        very_large_data = np.random.random((2000, 200)).tolist()  # ~3.2MB
        cache.set("very_large_item", very_large_data)
        
        # Check memory stats after caching
        after_cache_stats = get_memory_stats()
        logger.info(f"Memory stats after caching: {after_cache_stats['process']['percent']:.2f}% used")
        
        # Check cache stats
        cache_stats = cache.get_stats()
        logger.info(f"Cache stats: {cache_stats}")
        
        # Check if cache components are registered with memory manager
        components = [comp for comp in after_cache_stats['components'].keys() 
                     if comp.startswith('cache_')]
        logger.info(f"Cache components registered with memory manager: {components}")
        
        # Create memory pressure
        logger.info("Creating memory pressure")
        pressure_data = []
        for i in range(10):
            # Each array is about 8MB
            pressure_data.append(np.random.random((1000, 1000)))
            
            # Check memory status
            status = check_memory()
            logger.info(f"Memory status after pressure {i+1}: {status}")
            
            # If we reach HIGH or CRITICAL status, test cache behavior
            if status in ("HIGH", "CRITICAL"):
                # Try to cache another large item
                test_data = np.random.random((500, 500)).tolist()  # ~2MB
                cache.set("pressure_test_item", test_data)
                
                # Check where the item was stored
                cache_stats = cache.get_stats()
                logger.info(f"Cache stats under {status} pressure: {cache_stats}")
                
                # Try to retrieve the item
                retrieved = cache.get("pressure_test_item")
                logger.info(f"Retrieved pressure test item: {len(retrieved) if retrieved else 'None'}")
                
                break
        
        # Perform memory cleanup
        logger.info("Testing memory cleanup")
        
        # Light cleanup
        freed_light = cleanup_memory(CleanupLevel.LIGHT)
        logger.info(f"Light cleanup freed: {freed_light.get('total', 0) / (1024 * 1024):.2f} MB")
        
        # Check cache stats after light cleanup
        cache_stats_after_light = cache.get_stats()
        logger.info(f"Cache stats after light cleanup: {cache_stats_after_light}")
        
        # Moderate cleanup
        freed_moderate = cleanup_memory(CleanupLevel.MODERATE)
        logger.info(f"Moderate cleanup freed: {freed_moderate.get('total', 0) / (1024 * 1024):.2f} MB")
        
        # Check cache stats after moderate cleanup
        cache_stats_after_moderate = cache.get_stats()
        logger.info(f"Cache stats after moderate cleanup: {cache_stats_after_moderate}")
        
        # Aggressive cleanup
        freed_aggressive = cleanup_memory(CleanupLevel.AGGRESSIVE)
        logger.info(f"Aggressive cleanup freed: {freed_aggressive.get('total', 0) / (1024 * 1024):.2f} MB")
        
        # Check cache stats after aggressive cleanup
        cache_stats_after_aggressive = cache.get_stats()
        logger.info(f"Cache stats after aggressive cleanup: {cache_stats_after_aggressive}")
        
        # Final memory stats
        final_stats = get_memory_stats()
        logger.info(f"Final memory stats: {final_stats['process']['percent']:.2f}% used")
        
        # Test named caches with memory manager integration
        logger.info("Testing named caches with memory manager integration")
        
        # Create a named cache
        named_cache = get_cache("test_integration")
        
        # Store some data
        named_cache.set("test_key", np.random.random((100, 100)).tolist())
        
        # Check if the named cache is registered with memory manager
        final_components = [comp for comp in get_memory_stats()['components'].keys() 
                           if comp.startswith('cache_')]
        logger.info(f"Final cache components: {final_components}")
        
        # Check if cache_test_integration is in the components
        assert any(comp.startswith('cache_test_integration') for comp in final_components), \
            "Named cache not registered with memory manager"
        
        logger.info("Cache-memory integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"Error testing cache-memory integration: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_cache_memory_integration():
        logger.info("Cache-memory integration test passed")
        sys.exit(0)
    else:
        logger.error("Cache-memory integration test failed")
        sys.exit(1)
