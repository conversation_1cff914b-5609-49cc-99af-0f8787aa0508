@echo off
echo ============================================================================
echo Training All Models - BTCUSD.a Multi-Timeframe Training
echo ============================================================================
echo.
echo This script will train LSTM, ARIMA, and TFT models for all timeframes
echo Timeframes: M5, M15, M30, H1, H4
echo.

REM Check Python availability
echo Checking Python environment...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not available or not in PATH
    echo Please ensure Python is installed and accessible
    pause
    exit /b 1
)

REM Check if data files exist
echo Checking data files...
if not exist "data\historical\btcusd.a\BTCUSD.a_M5.parquet" (
    echo ERROR: Required data files not found in data\historical\btcusd.a\
    echo Please ensure data files are present before training
    pause
    exit /b 1
)

REM Create necessary directories
if not exist "models" mkdir models
if not exist "metrics" mkdir metrics
if not exist "logs" mkdir logs

echo.
echo ============================================================================
echo Starting Model Training Process
echo ============================================================================

REM Train LSTM models
echo.
echo [1/3] Training LSTM models...
echo ----------------------------------------
python train_lstm_btcusd.py
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: LSTM training encountered errors
) else (
    echo SUCCESS: LSTM models trained successfully
)

REM Train ARIMA models
echo.
echo [2/3] Training ARIMA models...
echo ----------------------------------------
if exist "train_all_arima_models.bat" (
    call train_all_arima_models.bat
    if %ERRORLEVEL% NEQ 0 (
        echo WARNING: ARIMA training encountered errors
    ) else (
        echo SUCCESS: ARIMA models trained successfully
    )
) else (
    echo WARNING: ARIMA training script not found, skipping...
)

REM Train TFT models
echo.
echo [3/3] Training TFT models...
echo ----------------------------------------
if exist "train_all_tft_models.bat" (
    call train_all_tft_models.bat
    if %ERRORLEVEL% NEQ 0 (
        echo WARNING: TFT training encountered errors
    ) else (
        echo SUCCESS: TFT models trained successfully
    )
) else if exist "train_all_tft_models.sh" (
    bash train_all_tft_models.sh
    if %ERRORLEVEL% NEQ 0 (
        echo WARNING: TFT training encountered errors
    ) else (
        echo SUCCESS: TFT models trained successfully
    )
) else (
    echo WARNING: TFT training script not found, skipping...
)

echo.
echo ============================================================================
echo Training Process Completed
echo ============================================================================
echo.
echo Check the logs directory for detailed training logs
echo Check the models directory for saved model files
echo Check the metrics directory for performance metrics
echo.
pause
