#!/usr/bin/env python
"""
Environment Setup Script for Trading Bot

This script helps set up the Python environment with the correct package versions
to avoid compatibility issues, especially between numpy, pmdarima, and tensorflow.

It installs packages in the correct order to ensure compatibility.

Usage:
    python setup_environment.py

Requirements:
    - Python 3.8+ installed
    - pip package manager
"""

import os
import sys
import subprocess
import platform
import time

def print_step(step_num, message):
    """Print a formatted step message."""
    print(f"\n[Step {step_num}] {message}")
    print("=" * (len(message) + 10))

def run_command(command, description=None):
    """Run a shell command and print its output."""
    if description:
        print(f"{description}...")

    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
            universal_newlines=True
        )

        # Print output in real-time
        for line in process.stdout:
            print(line.strip())

        process.wait()

        if process.returncode != 0:
            print(f"Command failed with return code {process.returncode}")
            for line in process.stderr:
                print(line.strip())
            return False
        return True
    except Exception as e:
        print(f"Error executing command: {e}")
        return False

def create_virtual_env():
    """Create and activate a virtual environment."""
    env_name = "trading_bot_env"

    # Check if virtual environment already exists
    if os.path.exists(env_name):
        print(f"Virtual environment '{env_name}' already exists.")
        return env_name

    # Create virtual environment
    print(f"Creating virtual environment '{env_name}'...")
    run_command(f"python -m venv {env_name}", "Creating virtual environment")

    print(f"\nVirtual environment '{env_name}' created successfully!")
    return env_name

def get_activate_command(env_name):
    """Get the command to activate the virtual environment based on the OS."""
    if platform.system() == "Windows":
        return f"{env_name}\\Scripts\\activate"
    else:
        return f"source {env_name}/bin/activate"

def main():
    """Main function to set up the environment."""
    print("\n" + "=" * 80)
    print("Trading Bot Environment Setup".center(80))
    print("=" * 80)

    # Step 1: Check Python version
    print_step(1, "Checking Python version")
    python_version = platform.python_version()
    print(f"Python version: {python_version}")

    major, minor, _ = map(int, python_version.split('.'))
    if major < 3 or (major == 3 and minor < 8):
        print("Error: Python 3.8 or higher is required.")
        sys.exit(1)

    # Step 2: Ask user if they want to use a virtual environment
    print_step(2, "Virtual Environment Setup")
    print("It's recommended to use a virtual environment to avoid dependency conflicts.")
    use_venv = input("Do you want to create a virtual environment? (y/n): ").lower().strip() == 'y'

    if use_venv:
        # Create virtual environment
        env_name = create_virtual_env()

        # Provide activation instructions
        activate_cmd = get_activate_command(env_name)
        print("\n" + "=" * 80)
        print("IMPORTANT: You need to activate the virtual environment".center(80))
        print("=" * 80)
        print(f"\nRun the following command to activate the environment:")
        print(f"\n    {activate_cmd}")
        print("\nThen run this script again:")
        print("\n    python setup_environment.py")
        print("\nExiting now. Please run the script again after activating the environment.")
        return

    # Check if we're in a virtual environment
    in_venv = sys.prefix != sys.base_prefix
    if not in_venv:
        print("\nWARNING: You are not running in a virtual environment.")
        print("This may cause dependency conflicts with your global Python installation.")
        proceed = input("Do you want to proceed anyway? (y/n): ").lower().strip() == 'y'
        if not proceed:
            print("Exiting. Please create and activate a virtual environment before running this script.")
            return

    # Step 3: Uninstall potentially conflicting packages
    print_step(3, "Uninstalling potentially conflicting packages")
    run_command("pip uninstall -y pmdarima numpy tensorflow tensorflow-intel bayesian-optimization", "Uninstalling packages")

    # Step 4: Install core dependencies first
    print_step(4, "Installing core dependencies")
    core_deps = [
        "numpy==1.23.5",
        "scipy==1.10.1",
        "pandas==2.0.3",
        "scikit-learn==1.3.0"
    ]
    for dep in core_deps:
        run_command(f"pip install {dep} --ignore-installed", f"Installing {dep}")

    # Step 5: Install ARIMA dependencies
    print_step(5, "Installing ARIMA dependencies")
    run_command("pip install statsmodels==0.14.0 pmdarima==2.0.3 --ignore-installed", "Installing ARIMA packages")

    # Step 6: Install TensorFlow
    print_step(6, "Installing TensorFlow")
    run_command("pip install tensorflow==2.12.0 --ignore-installed", "Installing TensorFlow")

    # Step 7: Install PyTorch
    print_step(7, "Installing PyTorch")
    if platform.system() == "Windows":
        run_command("pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118 --ignore-installed",
                   "Installing PyTorch with CUDA support")
    else:
        run_command("pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --ignore-installed",
                   "Installing PyTorch")

    # Step 8: Install remaining dependencies
    print_step(8, "Installing remaining dependencies")
    run_command("pip install -r requirements.txt --ignore-installed", "Installing remaining packages")

    # Step 9: Verify installation
    print_step(9, "Verifying installation")
    verification_script = """
import numpy
import pandas
import pmdarima
import tensorflow
import torch
import statsmodels

print(f"NumPy version: {numpy.__version__}")
print(f"pandas version: {pandas.__version__}")
print(f"pmdarima version: {pmdarima.__version__}")
print(f"TensorFlow version: {tensorflow.__version__}")
print(f"PyTorch version: {torch.__version__}")
print(f"statsmodels version: {statsmodels.__version__}")

print("\\nAll packages imported successfully!")
"""

    # Write verification script to a temporary file
    with open("verify_install.py", "w") as f:
        f.write(verification_script)

    # Run verification script
    success = run_command("python verify_install.py", "Verifying package installation")

    # Clean up
    if os.path.exists("verify_install.py"):
        os.remove("verify_install.py")

    if success:
        print("\n" + "=" * 80)
        print("Environment setup completed successfully!".center(80))
        print("=" * 80)
        print("\nYou can now run the trading bot with the correct package versions.")
    else:
        print("\n" + "=" * 80)
        print("Environment setup encountered issues.".center(80))
        print("=" * 80)
        print("\nPlease check the error messages above and try to resolve them manually.")

if __name__ == "__main__":
    main()
