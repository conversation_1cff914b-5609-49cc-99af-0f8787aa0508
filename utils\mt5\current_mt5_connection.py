"""
Current MT5 Connection

This script works with whatever terminal MT5 is currently initialized with,
without trying to change it, to avoid disabling Algo Trading.

Key features:
1. Works with the currently initialized MT5 terminal
2. Preserves Algo Trading status
3. Provides terminal, account, and symbol information
4. Can place orders on the connected terminal
5. Never calls mt5.shutdown() to maintain Algo Trading status

Usage:
    python current_mt5_connection.py [--symbol SYMBOL] [--order] [--type TYPE] [--volume VOLUME]

Example:
    python current_mt5_connection.py --symbol BTCUSD.a --order --type BUY --volume 0.01
"""

import os
import logging
import argparse
import MetaTrader5 as mt5

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import MT5 credentials from config
from config.credentials import MT5_<PERSON><PERSON><PERSON><PERSON>LS

def get_terminal_id_from_path(path: str) -> str:
    """
    Get terminal ID from path.

    Args:
        path: Terminal path

    Returns:
        str: Terminal ID or None if not found
    """
    path = os.path.normpath(path).lower()

    for terminal_id, config in MT5_TERMINALS.items():
        config_path = os.path.normpath(os.path.dirname(config["path"])).lower()
        if path == config_path:
            # Ensure consistent string return type
            return str(terminal_id)

    return None

def display_terminal_info():
    """Display terminal information."""
    terminal_info = mt5.terminal_info()
    if not terminal_info:
        logger.error("Could not get terminal info")
        return

    # Get terminal ID from path
    terminal_id = get_terminal_id_from_path(terminal_info.path)
    terminal_id_str = f"{terminal_id}" if terminal_id else "Unknown"

    logger.info("\n=== Terminal Information ===")
    logger.info(f"Terminal ID: {terminal_id_str}")
    logger.info(f"Name: {terminal_info.name}")
    logger.info(f"Path: {terminal_info.path}")
    logger.info(f"Connected: {terminal_info.connected}")
    logger.info(f"Algo Trading: {'ENABLED' if terminal_info.trade_allowed else 'DISABLED'}")

    return terminal_info.trade_allowed

def display_account_info():
    """Display account information."""
    account_info = mt5.account_info()
    if not account_info:
        logger.error("Could not get account info")
        return

    logger.info("\n=== Account Information ===")
    logger.info(f"Login: {account_info.login}")
    logger.info(f"Server: {account_info.server}")
    logger.info(f"Currency: {account_info.currency}")
    logger.info(f"Balance: {account_info.balance}")
    logger.info(f"Equity: {account_info.equity}")
    logger.info(f"Margin: {account_info.margin}")
    logger.info(f"Margin Free: {account_info.margin_free}")
    logger.info(f"Margin Level: {account_info.margin_level}%")
    logger.info(f"Leverage: 1:{account_info.leverage}")

def display_symbol_info(symbol: str):
    """
    Display symbol information.

    Args:
        symbol: Symbol to get information for (e.g., "EURUSD", "BTCUSD.a")
    """
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        logger.warning(f"Symbol {symbol} not found")
        return

    logger.info(f"\n=== Symbol Information for {symbol} ===")
    logger.info(f"Bid: {symbol_info.bid}")
    logger.info(f"Ask: {symbol_info.ask}")
    logger.info(f"Spread: {symbol_info.spread} points")
    logger.info(f"Volume Min: {symbol_info.volume_min}")
    logger.info(f"Volume Max: {symbol_info.volume_max}")
    logger.info(f"Volume Step: {symbol_info.volume_step}")

    return symbol_info

def place_order(symbol: str, order_type: str, volume: float, price: float = 0.0,
               sl: float = 0.0, tp: float = 0.0, comment: str = ""):
    """
    Place an order.

    Args:
        symbol: Symbol to trade (e.g., "EURUSD", "BTCUSD.a")
        order_type: Order type ("BUY" or "SELL")
        volume: Order volume
        price: Order price (0 for market orders)
        sl: Stop loss price
        tp: Take profit price
        comment: Order comment

    Returns:
        bool: True if order placed successfully, False otherwise
    """
    # Check if Algo Trading is enabled
    terminal_info = mt5.terminal_info()
    if not terminal_info or not terminal_info.trade_allowed:
        logger.error("Cannot place order: Algo Trading is disabled")
        return False

    # Get symbol info
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        logger.warning(f"Symbol {symbol} not found")
        return False

    # Prepare order request
    if order_type.upper() == "BUY":
        order_type = mt5.ORDER_TYPE_BUY
        price = mt5.symbol_info_tick(symbol).ask
    elif order_type.upper() == "SELL":
        order_type = mt5.ORDER_TYPE_SELL
        price = mt5.symbol_info_tick(symbol).bid
    else:
        logger.error(f"Invalid order type: {order_type}")
        return False

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": order_type,
        "price": price,
        "sl": sl,
        "tp": tp,
        "comment": comment,
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC
    }

    # Send order
    logger.info(f"Placing order: {order_type} {volume} {symbol} @ {price}")
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"Order failed: {result.retcode}, {result.comment}")

        # Provide more detailed error information
        if result.retcode == mt5.TRADE_RETCODE_INVALID_VOLUME:
            logger.error(f"Invalid volume: {volume}. Min: {symbol_info.volume_min}, Max: {symbol_info.volume_max}, Step: {symbol_info.volume_step}")
        elif result.retcode == mt5.TRADE_RETCODE_INVALID_PRICE:
            logger.error(f"Invalid price: {price}. Current bid: {symbol_info.bid}, ask: {symbol_info.ask}")
        elif result.retcode == mt5.TRADE_RETCODE_INVALID_STOPS:
            logger.error(f"Invalid stop levels. Check SL/TP values and minimum stop level")
        elif result.retcode == mt5.TRADE_RETCODE_TRADE_DISABLED:
            logger.error("Trading is disabled. Check if Algo Trading is enabled in MT5")
        elif result.retcode == mt5.TRADE_RETCODE_MARKET_CLOSED:
            logger.error(f"Market is closed for {symbol}")
        elif result.retcode == mt5.TRADE_RETCODE_NO_MONEY:
            logger.error("Not enough money to place the order")

        return False

    logger.info(f"Order placed successfully: {result.order}")
    return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Current MT5 Connection")
    parser.add_argument("--symbol", type=str, default="BTCUSD.a", help="Symbol to check")
    parser.add_argument("--order", action="store_true", help="Place a test order")
    parser.add_argument("--type", type=str, default="BUY", help="Order type (BUY or SELL)")
    parser.add_argument("--volume", type=float, default=0.01, help="Order volume")
    args = parser.parse_args()

    # Check if MT5 is already initialized
    if not mt5.initialize():
        logger.error("MT5 is not initialized")
        logger.info("Initializing MT5 with minimal parameters to preserve Algo Trading")

        # Try to initialize with minimal parameters to preserve Algo Trading
        if not mt5.initialize(portable=True):
            error = mt5.last_error()
            logger.error(f"Failed to initialize MT5: {error}")
            logger.error("Please ensure that at least one MT5 terminal is running")
            logger.error("You may need to manually start an MT5 terminal before running this script")
            return

    logger.info("MT5 is initialized")

    # Display terminal info and check if Algo Trading is enabled
    algo_trading_enabled = display_terminal_info()

    # Display account info
    display_account_info()

    # Display symbol info
    symbol_info = display_symbol_info(args.symbol)

    # Place a test order if requested
    if args.order and algo_trading_enabled and symbol_info:
        logger.info("\n=== Placing Order ===")
        volume = args.volume if args.volume else symbol_info.volume_min
        success = place_order(args.symbol, args.type, volume, comment="Test order")
        if success:
            logger.info("Order placed successfully")
        else:
            logger.error("Failed to place order")

    # Keep the connection open
    logger.info("\nKeeping MT5 connection open to preserve Algo Trading")
    logger.info("You can now use this connection for other operations")

if __name__ == "__main__":
    main()
