#!/bin/bash
# Script to train ARIMA models for all timeframes

# Create necessary directories
mkdir -p models
mkdir -p logs
mkdir -p metrics
mkdir -p plots

# Set timeframes
TIMEFRAMES=("M5" "M15" "M30" "H1" "H4")

# Train ARIMA models for all timeframes
for timeframe in "${TIMEFRAMES[@]}"
do
    echo "Training ARIMA model for $timeframe..."
    python train_arima_single.py --timeframe $timeframe --target-column close --auto-arima
    
    # Check if training was successful
    if [ $? -eq 0 ]; then
        echo "Successfully trained ARIMA model for $timeframe"
    else
        echo "Failed to train ARIMA model for $timeframe"
    fi
done

echo "All ARIMA models trained successfully!"
