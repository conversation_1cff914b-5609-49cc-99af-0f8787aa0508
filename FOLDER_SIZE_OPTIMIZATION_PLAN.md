# Folder Size Optimization Plan

## Critical Issues Identified

### **🚨 MASSIVE REDUNDANCY PROBLEM (551GB Models Directory)**

#### **Primary Issue: Duplicate ARIMA Models**
- **Problem**: ARIMA model.pkl files are 21GB each and duplicated across 5+ locations
- **Current Size**: 515GB in terminal directories (1-5) alone
- **Impact**: 99% of space is redundant duplicates

#### **Redundancy Breakdown**
```
models/1/M5/arima_BTCUSD.a_M5/model.pkl = 21GB
models/2/M5/arima_BTCUSD.a_M5/model.pkl = 21GB (DUPLICATE)
models/3/M5/arima_BTCUSD.a_M5/model.pkl = 21GB (DUPLICATE)
models/4/M5/arima_BTCUSD.a_M5/model.pkl = 21GB (DUPLICATE)
models/5/M5/arima_BTCUSD.a_M5/model.pkl = 21GB (DUPLICATE)
+ Additional duplicates in other timeframes
Total Redundancy: ~500GB
```

#### **Additional Issues**
- **Duplicate Model Structures**: Multiple naming conventions for same models
- **Obsolete Terminal Directories**: Terminal 1-5 directories are legacy
- **Inconsistent Organization**: Mixed naming patterns and structures
- **Large Log Files**: 148MB lightning_logs, 44MB test_results

## Optimization Strategy

### **Phase 1: Emergency Cleanup (Immediate - 99% Space Reduction)**

#### **1.1 Remove Duplicate Terminal Directories**
```bash
# Remove terminal directories 1-5 (515GB reduction)
rm -rf models/1 models/2 models/3 models/4 models/5
```

#### **1.2 Consolidate ARIMA Models**
Keep only the canonical ARIMA models:
- `models/arima_BTCUSD.a_M5/` (21GB)
- `models/arima_BTCUSD.a_M15/` (6.9GB)
- `models/arima_BTCUSD.a_M30/` (3.5GB)
- `models/arima_BTCUSD.a_H1/` (1.7GB)
- `models/arima_BTCUSD.a_H4/` (446MB)

Remove duplicates:
```bash
# Remove duplicate ARIMA files (3.2GB + 426MB + 139MB + 42MB + 11MB)
rm -f models/arima_M5_BTCUSD.a_M5.pkl.pkl
rm -f models/arima_M15_BTCUSD.a_M15.pkl.pkl
rm -f models/arima_M30_BTCUSD.a_M30.pkl.pkl
rm -f models/arima_H1_BTCUSD.a_H1.pkl.pkl
rm -f models/arima_H4_BTCUSD.a_H4.pkl.pkl
```

#### **1.3 Clean Obsolete Directories**
```bash
# Remove obsolete terminal directories
rm -rf models/terminal_* models/test models/saved
```

### **Phase 2: Structure Optimization (Medium Priority)**

#### **2.1 Standardize Model Organization**
```
models/
├── lstm_BTCUSD.a_M5/     # Keep canonical LSTM models
├── lstm_BTCUSD.a_M15/
├── lstm_BTCUSD.a_M30/
├── lstm_BTCUSD.a_H1/
├── lstm_BTCUSD.a_H4/
├── arima_BTCUSD.a_M5/    # Keep canonical ARIMA models
├── arima_BTCUSD.a_M15/
├── arima_BTCUSD.a_M30/
├── arima_BTCUSD.a_H1/
├── arima_BTCUSD.a_H4/
├── tft_BTCUSD.a_M5/      # Keep canonical TFT models
├── tft_BTCUSD.a_M15/
├── tft_BTCUSD.a_M30/
├── tft_BTCUSD.a_H1/
├── tft_BTCUSD.a_H4/
└── tft_arima_BTCUSD.a_*/ # Keep TFT+ARIMA models
```

#### **2.2 Remove Duplicate Model Formats**
```bash
# Remove duplicate LSTM models with wrong naming
rm -rf models/lstm_*_BTCUSD.a_*  # Remove timeframe_symbol format
rm -rf models/lstm/              # Remove nested structure
```

#### **2.3 Clean Log Directories**
```bash
# Clean large log directories
rm -rf lightning_logs/           # 148MB
rm -rf test_results/            # 44MB
find logs/ -name "*.log" -size +10M -delete  # Large log files
```

### **Phase 3: Data Optimization (Low Priority)**

#### **3.1 Data Directory Cleanup**
```bash
# Remove duplicate data files
rm -rf data/cache/              # Duplicate of data/historical/
find data/ -name "*combined*" -delete  # Remove combined files if not needed
```

#### **3.2 Visualization Cleanup**
```bash
# Clean old visualizations
find visualizations/ -name "*.png" -mtime +30 -delete
find plots/ -name "*.png" -mtime +30 -delete
```

## Implementation Script

### **Safe Cleanup Script**
```python
#!/usr/bin/env python3
"""
Safe folder size optimization script.
"""

import os
import shutil
from pathlib import Path
import logging

def safe_cleanup():
    """Perform safe cleanup operations."""
    
    # Phase 1: Remove duplicate terminal directories
    terminal_dirs = ['models/1', 'models/2', 'models/3', 'models/4', 'models/5']
    for dir_path in terminal_dirs:
        if os.path.exists(dir_path):
            print(f"Removing {dir_path}...")
            shutil.rmtree(dir_path)
    
    # Phase 2: Remove duplicate ARIMA files
    duplicate_arima = [
        'models/arima_M5_BTCUSD.a_M5.pkl.pkl',
        'models/arima_M15_BTCUSD.a_M15.pkl.pkl',
        'models/arima_M30_BTCUSD.a_M30.pkl.pkl',
        'models/arima_H1_BTCUSD.a_H1.pkl.pkl',
        'models/arima_H4_BTCUSD.a_H4.pkl.pkl'
    ]
    for file_path in duplicate_arima:
        if os.path.exists(file_path):
            print(f"Removing {file_path}...")
            os.remove(file_path)
    
    # Phase 3: Remove obsolete directories
    obsolete_dirs = ['models/terminal_1', 'models/terminal_2', 'models/terminal_3', 
                     'models/terminal_4', 'models/terminal_5', 'models/terminal_integration_test',
                     'models/test', 'models/saved']
    for dir_path in obsolete_dirs:
        if os.path.exists(dir_path):
            print(f"Removing {dir_path}...")
            shutil.rmtree(dir_path)

if __name__ == "__main__":
    safe_cleanup()
```

## Expected Results

### **Space Reduction**
- **Before**: 551GB models directory
- **After**: ~35GB models directory
- **Reduction**: 516GB (93.6% reduction)

### **Maintained Functionality**
- ✅ All working models preserved
- ✅ Current training capabilities maintained
- ✅ Ensemble functionality intact
- ✅ Model loading/saving works

### **Improved Organization**
- ✅ Consistent naming convention
- ✅ No duplicate models
- ✅ Clear directory structure
- ✅ Easier maintenance

## Risk Mitigation

### **Backup Strategy**
1. **Test Environment**: Run cleanup on copy first
2. **Incremental Cleanup**: Remove directories one by one
3. **Verification**: Test model loading after each phase
4. **Rollback Plan**: Keep list of removed items

### **Validation Steps**
1. **Model Loading Test**: Verify all models still load
2. **Training Test**: Ensure training scripts work
3. **Ensemble Test**: Verify LSTM+ARIMA ensemble works
4. **Performance Test**: Check model performance unchanged

## Long-term Maintenance

### **Prevention Strategies**
1. **Model Versioning**: Use proper versioning instead of duplication
2. **Symbolic Links**: Link to canonical models instead of copying
3. **Cleanup Automation**: Regular cleanup scripts
4. **Size Monitoring**: Alert when directories exceed thresholds

### **Best Practices**
1. **Single Source of Truth**: One canonical model per type/timeframe
2. **Consistent Naming**: Follow established patterns
3. **Regular Cleanup**: Monthly cleanup of old files
4. **Documentation**: Clear model organization documentation
