#!/usr/bin/env python
"""
TFT Model with ARIMA Integration Training Script for a Single Timeframe

This script trains a Temporal Fusion Transformer (TFT) model with ARIMA integration
on BTCUSD.a data for a single timeframe using PyTorch with GPU acceleration.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime
import argparse
import matplotlib.pyplot as plt
import pickle
from pmdarima import auto_arima

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train TFT model with ARIMA integration for a single timeframe')

    # Required arguments
    parser.add_argument('--timeframe', type=str, required=True,
                        help='Timeframe to train (e.g., M5, H1)')

    # Optional arguments
    parser.add_argument('--feature-columns', type=str, default='open,high,low,close,real_volume',
                        help='Comma-separated list of feature columns')
    parser.add_argument('--target-column', type=str, default='close',
                        help='Target column to predict')
    parser.add_argument('--sequence-length', type=int, default=60,
                        help='Length of input sequences')
    parser.add_argument('--hidden-size', type=int, default=64,
                        help='Hidden size for TFT model')
    parser.add_argument('--attention-head-size', type=int, default=4,
                        help='Number of attention heads')
    parser.add_argument('--dropout-rate', type=float, default=0.1,
                        help='Dropout rate')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--epochs', type=int, default=5,
                        help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Proportion of data to use for testing')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--use-gpu', action='store_true', default=True,
                        help='Use GPU for training if available')
    parser.add_argument('--data-dir', type=str, default='data/historical/btcusd.a',
                        help='Directory containing data files')
    parser.add_argument('--arima-window', type=int, default=10000,
                        help='Number of data points to use for ARIMA training')

    return parser.parse_args()

def load_data(timeframe, data_dir):
    """Load data for a specific timeframe."""
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def train_arima_model(df, target_column, arima_window, test_size):
    """Train ARIMA model and generate predictions."""
    try:
        # Use only the last arima_window rows for ARIMA to make it faster
        if len(df) > arima_window:
            logger.info(f"Using last {arima_window} rows for ARIMA training")
            arima_df = df.iloc[-arima_window:].copy()
        else:
            arima_df = df.copy()

        # Extract target column
        y = arima_df[target_column].values

        # Split into train and test sets
        train_size = int(len(y) * (1 - test_size))
        train_data = y[:train_size]

        # Train ARIMA model
        logger.info("Training ARIMA model")
        model = auto_arima(
            train_data,
            start_p=0, start_q=0,
            max_p=5, max_q=5,
            d=None,
            seasonal=False,
            trace=True,
            error_action='ignore',
            suppress_warnings=True,
            stepwise=True
        )

        # Get the best parameters
        best_p, best_d, best_q = model.order
        logger.info(f"Best ARIMA parameters: ({best_p},{best_d},{best_q})")

        # Generate simplified ARIMA predictions for the entire dataset
        logger.info("Generating simplified ARIMA predictions for the entire dataset")
        arima_preds = np.zeros(len(df))

        # Use a simple approach: lag-1 prediction with ARIMA trend
        # This is much more efficient than the complex rolling window approach
        target_values = df[target_column].values

        # For the first few points, use actual values
        arima_preds[:10] = target_values[:10]

        # For the rest, use a simple AR(1) model with ARIMA-derived parameters
        # This approximates ARIMA behavior but is much faster
        for i in range(10, len(df)):
            # Simple lag-1 prediction with trend adjustment
            if i >= best_p + best_d:
                # Use ARIMA-like prediction: weighted average of recent values
                recent_values = target_values[i-best_p-best_d:i]
                if len(recent_values) > 0:
                    # Simple moving average with trend
                    trend = np.mean(np.diff(recent_values[-5:])) if len(recent_values) >= 5 else 0
                    arima_preds[i] = np.mean(recent_values[-3:]) + trend
                else:
                    arima_preds[i] = target_values[i-1]
            else:
                arima_preds[i] = target_values[i-1]  # Simple lag-1 for early points

        # Save the ARIMA model
        model_dir = Path("models") / f"arima_BTCUSD.a_{df['time'].iloc[0].strftime('%Y%m%d')}_{df['time'].iloc[-1].strftime('%Y%m%d')}"
        model_dir.mkdir(parents=True, exist_ok=True)

        with open(model_dir / "model.pkl", "wb") as f:
            pickle.dump(model, f)

        logger.info(f"ARIMA model saved to {model_dir}")

        # Add ARIMA predictions to the dataframe
        df['arima_pred'] = arima_preds

        return df, model

    except Exception as e:
        logger.error(f"Error training ARIMA model: {str(e)}", exc_info=True)
        return df, None

def train_tft_arima_model(args):
    """Train TFT model with ARIMA integration for a specific timeframe."""
    try:
        # Import required packages
        import torch
        import torch.nn as nn
        import torch.optim as optim
        from torch.utils.data import DataLoader, TensorDataset

        logger.info("Using simplified TFT+ARIMA implementation due to PyTorch Lightning compatibility issues")

        # Load data
        df = load_data(args.timeframe, args.data_dir)
        if df is None:
            return None

        # Parse feature columns
        feature_columns = args.feature_columns.split(',')

        # Train ARIMA model and add predictions to dataframe
        df, arima_model = train_arima_model(df, args.target_column, args.arima_window, args.test_size)

        # Add ARIMA predictions as a feature
        feature_columns.append('arima_pred')

        # Use the same preprocessing approach as the fixed TFT model
        from train_tft_pytorch import TemporalFusionTransformer, preprocess_data

        # Preprocess data using the same method as TFT
        X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data(
            df, feature_columns, args.target_column, args.sequence_length, args.test_size
        )

        # Convert to PyTorch tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train)
        X_test_tensor = torch.FloatTensor(X_test)
        y_test_tensor = torch.FloatTensor(y_test)

        # Create datasets and dataloaders
        from torch.utils.data import DataLoader, TensorDataset
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=args.batch_size)

        # Set device
        device = torch.device('cuda' if torch.cuda.is_available() and args.use_gpu else 'cpu')
        logger.info(f"Using device: {device}")

        # Create TFT model (same architecture as the fixed TFT model)
        model = TemporalFusionTransformer(
            input_dim=X_train.shape[2],
            hidden_dim=args.hidden_size,
            num_heads=args.attention_head_size,
            num_layers=2,  # Fixed to 2 layers
            dropout=args.dropout_rate
        ).to(device)

        # Define loss function and optimizer with improvements from TFT fixes
        import torch.nn as nn
        import torch.optim as optim
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=1e-5)

        # Add learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=2)

        # Training loop with early stopping (same as fixed TFT)
        logger.info(f"Training TFT+ARIMA model for BTCUSD.a {args.timeframe}")

        history = {
            'train_loss': [],
            'val_loss': []
        }

        # Early stopping parameters
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 3  # Stop if validation loss doesn't improve for 3 epochs
        best_model_state = None

        for epoch in range(args.epochs):
            model.train()
            train_loss = 0.0

            for inputs, targets in train_loader:
                inputs, targets = inputs.to(device), targets.to(device)

                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, targets)

                # Backward pass and optimize
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                train_loss += loss.item() * inputs.size(0)

            train_loss /= len(train_loader.dataset)
            history['train_loss'].append(train_loss)

            # Validation
            model.eval()
            val_loss = 0.0

            with torch.no_grad():
                for inputs, targets in test_loader:
                    inputs, targets = inputs.to(device), targets.to(device)
                    outputs = model(inputs)
                    loss = criterion(outputs, targets)
                    val_loss += loss.item() * inputs.size(0)

            val_loss /= len(test_loader.dataset)
            history['val_loss'].append(val_loss)

            # Learning rate scheduling
            scheduler.step(val_loss)

            # Early stopping logic
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = model.state_dict().copy()
                logger.info(f"Epoch {epoch+1}/{args.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f} (Best)")
            else:
                patience_counter += 1
                logger.info(f"Epoch {epoch+1}/{args.epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f} (Patience: {patience_counter}/{patience})")

                if patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break

        # Restore best model state
        if best_model_state is not None:
            model.load_state_dict(best_model_state)
            logger.info("Restored best model state")

        # Evaluate model
        model.eval()
        y_pred = []
        y_true = []

        with torch.no_grad():
            for inputs, targets in test_loader:
                inputs = inputs.to(device)
                outputs = model(inputs)
                y_pred.extend(outputs.cpu().numpy())
                y_true.extend(targets.numpy())

        y_pred = np.array(y_pred)
        y_true = np.array(y_true)

        # Inverse transform predictions and actual values
        y_pred_inv = y_scaler.inverse_transform(y_pred)
        y_true_inv = y_scaler.inverse_transform(y_true)

        # Calculate metrics using inverse transformed values
        mse = np.mean((y_pred_inv - y_true_inv) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_pred_inv - y_true_inv))

        # Calculate R² (coefficient of determination)
        y_mean = np.mean(y_true_inv)
        ss_total = np.sum((y_true_inv - y_mean) ** 2)
        ss_residual = np.sum((y_true_inv - y_pred_inv) ** 2)
        r2 = 1 - (ss_residual / ss_total)

        logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

        # Save model
        model_dir = Path("models") / f"tft_arima_BTCUSD.a_{args.timeframe}"
        model_dir.mkdir(parents=True, exist_ok=True)

        # Save model using the save method
        model.save(str(model_dir))

        # Save scalers
        torch.save({
            'X_scaler': X_scaler,
            'y_scaler': y_scaler
        }, model_dir / "scalers.pt")

        # Save metrics
        metrics = {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'model_type': 'tft_arima',
            'timeframe': args.timeframe,
            'feature_columns': feature_columns,
            'target_column': args.target_column,
            'sequence_length': args.sequence_length,
            'hidden_size': args.hidden_size,
            'attention_head_size': args.attention_head_size,
            'dropout_rate': args.dropout_rate,
            'learning_rate': args.learning_rate,
            'epochs': args.epochs,
            'batch_size': args.batch_size,
            'training_samples': len(X_train),
            'validation_samples': len(X_test),
            'arima_order': arima_model.order if arima_model else None,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Save metrics to file
        os.makedirs('metrics', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = f"metrics/tft_arima_BTCUSD.a_{args.timeframe}_{timestamp}.json"

        with open(metrics_file, 'w') as f:
            json.dump(metrics, f, indent=4)

        logger.info(f"Metrics saved to {metrics_file}")

        # Plot predictions vs actual
        plt.figure(figsize=(12, 6))
        plt.plot(y_true_inv[:100], label='Actual')
        plt.plot(y_pred_inv[:100], label='TFT+ARIMA Predicted')

        # Get ARIMA predictions for comparison (from test set)
        test_start_idx = len(df) - len(X_test) - args.sequence_length
        if test_start_idx >= 0 and test_start_idx < len(df):
            arima_preds = df.iloc[test_start_idx:test_start_idx+100]['arima_pred'].values
            plt.plot(arima_preds[:min(100, len(arima_preds))], label='ARIMA Predicted')

        plt.legend()
        plt.title(f'TFT+ARIMA Model Predictions vs Actual for {args.timeframe}')

        # Save plot
        os.makedirs('plots', exist_ok=True)
        plt.savefig(f'plots/tft_arima_predictions_{args.timeframe}.png')
        plt.close()

        return metrics

    except Exception as e:
        logger.error(f"Error training TFT+ARIMA model: {str(e)}", exc_info=True)
        return None

def main():
    """Main function."""
    args = parse_args()
    train_tft_arima_model(args)

if __name__ == "__main__":
    main()
