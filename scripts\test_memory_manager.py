"""
Test script for the enhanced memory manager.
"""
import os
import sys
import logging
import time
import numpy as np
import pandas as pd
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_memory_manager():
    """Test the enhanced memory manager."""
    try:
        from utils.enhanced_memory_manager import (
            enhanced_memory_manager,
            get_memory_stats,
            check_memory,
            cleanup_memory,
            optimize_dataframe,
            memory_profile,
            start_monitoring,
            stop_monitoring,
            CleanupLevel,
            MemoryStatus
        )
        
        logger.info("Testing enhanced memory manager")
        
        # Test basic memory stats
        stats = get_memory_stats()
        logger.info(f"Memory stats: {stats['process']['percent']:.2f}% used")
        
        # Test memory status
        status = check_memory()
        logger.info(f"Memory status: {status}")
        
        # Test dataframe optimization
        def create_test_dataframe(rows=1000000, cols=10):
            """Create a test dataframe with high memory usage."""
            logger.info(f"Creating test dataframe with {rows} rows and {cols} columns")
            data = {}
            for i in range(cols):
                if i % 3 == 0:
                    # Integer column
                    data[f'int_col_{i}'] = np.random.randint(0, 100, size=rows)
                elif i % 3 == 1:
                    # Float column
                    data[f'float_col_{i}'] = np.random.random(size=rows)
                else:
                    # String column with few unique values
                    categories = [f'category_{j}' for j in range(10)]
                    data[f'str_col_{i}'] = np.random.choice(categories, size=rows)
            
            return pd.DataFrame(data)
        
        # Create a test dataframe
        df = create_test_dataframe(rows=500000, cols=10)
        
        # Check memory usage before optimization
        before_size = df.memory_usage(deep=True).sum() / (1024 * 1024)  # MB
        logger.info(f"Dataframe size before optimization: {before_size:.2f} MB")
        
        # Optimize dataframe
        optimized_df = optimize_dataframe(df)
        
        # Check memory usage after optimization
        after_size = optimized_df.memory_usage(deep=True).sum() / (1024 * 1024)  # MB
        logger.info(f"Dataframe size after optimization: {after_size:.2f} MB")
        logger.info(f"Memory saved: {before_size - after_size:.2f} MB ({(before_size - after_size) / before_size * 100:.2f}%)")
        
        # Test component registration and tracking
        enhanced_memory_manager.register_component(
            "test_component",
            initial_bytes=df.memory_usage(deep=True).sum()
        )
        
        # Update component usage
        enhanced_memory_manager.update_component_usage(
            "test_component",
            optimized_df.memory_usage(deep=True).sum()
        )
        
        # Test memory cleanup
        logger.info("Testing memory cleanup")
        freed = cleanup_memory(CleanupLevel.LIGHT)
        logger.info(f"Light cleanup freed: {freed.get('total', 0) / (1024 * 1024):.2f} MB")
        
        freed = cleanup_memory(CleanupLevel.MODERATE)
        logger.info(f"Moderate cleanup freed: {freed.get('total', 0) / (1024 * 1024):.2f} MB")
        
        freed = cleanup_memory(CleanupLevel.AGGRESSIVE)
        logger.info(f"Aggressive cleanup freed: {freed.get('total', 0) / (1024 * 1024):.2f} MB")
        
        # Test memory profiling
        def memory_intensive_function():
            """A function that uses a lot of memory."""
            # Create a large array
            large_array = np.random.random((5000, 5000))
            # Do some operations
            result = np.dot(large_array, large_array.T)
            return result.diagonal().sum()
        
        logger.info("Testing memory profiling")
        result, profile = memory_profile(memory_intensive_function)
        logger.info(f"Function result: {result}")
        logger.info(f"Memory profile: {profile}")
        
        # Test monitoring
        logger.info("Testing memory monitoring")
        start_monitoring(interval=5)
        
        # Create some memory pressure
        logger.info("Creating memory pressure")
        large_arrays = []
        for i in range(5):
            large_arrays.append(np.random.random((2000, 2000)))
            time.sleep(1)
            logger.info(f"Created array {i+1}, checking memory status")
            status = check_memory()
            logger.info(f"Memory status: {status}")
        
        # Wait for monitoring to react
        logger.info("Waiting for monitoring to react")
        time.sleep(10)
        
        # Clean up
        large_arrays = None
        
        # Stop monitoring
        stop_monitoring()
        
        # Final memory stats
        stats = get_memory_stats()
        logger.info(f"Final memory stats: {stats['process']['percent']:.2f}% used")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing enhanced memory manager: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_memory_manager():
        logger.info("Enhanced memory manager test passed")
        sys.exit(0)
    else:
        logger.error("Enhanced memory manager test failed")
        sys.exit(1)
