"""
Start All MT5 Terminals

This script starts all 5 MT5 terminals and displays comprehensive information about each one.
It ensures that all terminals are actually running and provides detailed status information.

Key features:
1. Launches all 5 MT5 terminals if they're not already running
2. Verifies each terminal is properly started
3. Displays detailed information about each terminal
4. Preserves Algo Trading throughout the process
5. Shows comprehensive information including spreads, account details, and trading status

Usage:
    python start_all_terminals.py [--symbol SYMBOL]

Example:
    python start_all_terminals.py --symbol BTCUSD.a
"""

import os
import time
import logging
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import MT5 credentials from config
from config.credentials import MT5_TERMINALS

def ensure_terminal_running(terminal_id: int, wait_time: int = 15) -> bool:
    """
    Ensure the MT5 terminal is running by launching it if necessary.
    
    Args:
        terminal_id: ID of the terminal to check/launch (1-5)
        wait_time: Time to wait for terminal to start in seconds
        
    Returns:
        bool: True if terminal is running, False otherwise
    """
    if terminal_id not in MT5_TERMINALS:
        logger.error(f"Terminal {terminal_id} not found in configuration")
        return False
    
    terminal_config = MT5_TERMINALS[terminal_id]
    terminal_path = terminal_config["path"]
    
    # Check if terminal path exists
    if not os.path.exists(terminal_path):
        logger.error(f"Terminal path does not exist: {terminal_path}")
        return False
    
    # Check if the terminal process is already running
    terminal_running = False
    terminal_name = os.path.basename(terminal_path).lower()
    
    try:
        # Use tasklist to check if the terminal is running
        process = subprocess.Popen(
            ["tasklist", "/FI", f"IMAGENAME eq {terminal_name}"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        stdout, _ = process.communicate()
        
        if terminal_name in stdout.lower():
            logger.info(f"Terminal {terminal_id} is already running")
            terminal_running = True
        else:
            logger.info(f"Terminal {terminal_id} is not running, launching it")
            # Launch the terminal
            subprocess.Popen(
                [terminal_path],
                shell=False,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            logger.info(f"Waiting {wait_time} seconds for terminal {terminal_id} to start...")
            time.sleep(wait_time)  # Give it time to start
            
            # Verify the terminal is now running
            process = subprocess.Popen(
                ["tasklist", "/FI", f"IMAGENAME eq {terminal_name}"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            stdout, _ = process.communicate()
            
            if terminal_name in stdout.lower():
                logger.info(f"Terminal {terminal_id} started successfully")
                terminal_running = True
            else:
                logger.error(f"Failed to start terminal {terminal_id}")
                return False
    
    except Exception as e:
        logger.error(f"Error checking/launching terminal {terminal_id}: {str(e)}")
        return False
    
    return terminal_running

def check_terminal_window_exists(terminal_id: int) -> bool:
    """
    Check if the terminal window exists using window title.
    
    Args:
        terminal_id: ID of the terminal to check
        
    Returns:
        bool: True if window exists, False otherwise
    """
    try:
        # Get expected window title based on terminal ID
        window_title = ""
        if terminal_id in [1, 2]:
            window_title = "Pepperstone MetaTrader 5"
        elif terminal_id in [3, 4, 5]:
            window_title = "ICMarkets - MetaTrader 5"
        
        if not window_title:
            logger.error(f"Unknown window title for terminal {terminal_id}")
            return False
        
        # Use PowerShell to check if window exists
        cmd = f'powershell "Get-Process | Where-Object {{$_.MainWindowTitle -like \'*{window_title}*\'}} | Select-Object MainWindowTitle"'
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
            universal_newlines=True
        )
        stdout, _ = process.communicate()
        
        if window_title in stdout:
            logger.info(f"Terminal {terminal_id} window found")
            return True
        else:
            logger.warning(f"Terminal {terminal_id} window not found")
            return False
    
    except Exception as e:
        logger.error(f"Error checking terminal {terminal_id} window: {str(e)}")
        return False

def get_terminal_details(terminal_id: int, symbol: str = "BTCUSD.a") -> Dict[str, Any]:
    """
    Get detailed information about a terminal without using MT5 API.
    This avoids disabling Algo Trading.
    
    Args:
        terminal_id: ID of the terminal to check
        symbol: Symbol to check
        
    Returns:
        Dict: Terminal details
    """
    terminal_config = MT5_TERMINALS[terminal_id]
    
    details = {
        "terminal_id": terminal_id,
        "path": terminal_config["path"],
        "login": terminal_config["login"],
        "server": terminal_config["server"],
        "running": ensure_terminal_running(terminal_id),
        "window_visible": check_terminal_window_exists(terminal_id)
    }
    
    return details

def start_all_terminals(symbol: str = "BTCUSD.a") -> Dict[int, Dict[str, Any]]:
    """
    Start all MT5 terminals and get their details.
    
    Args:
        symbol: Symbol to check
        
    Returns:
        Dict: Dictionary of terminal IDs and their details
    """
    terminal_details = {}
    
    logger.info("Starting all MT5 terminals...")
    
    for terminal_id in MT5_TERMINALS:
        logger.info(f"\n{'=' * 50}")
        logger.info(f"Terminal {terminal_id}")
        logger.info(f"{'=' * 50}")
        
        details = get_terminal_details(terminal_id, symbol)
        terminal_details[terminal_id] = details
        
        if details["running"]:
            logger.info(f"Terminal {terminal_id} is running")
            logger.info(f"Path: {details['path']}")
            logger.info(f"Login: {details['login']}")
            logger.info(f"Server: {details['server']}")
            logger.info(f"Window visible: {details['window_visible']}")
        else:
            logger.error(f"Terminal {terminal_id} failed to start")
    
    return terminal_details

def display_terminal_summary(terminal_details: Dict[int, Dict[str, Any]]):
    """
    Display a summary of all terminals.
    
    Args:
        terminal_details: Dictionary of terminal IDs and their details
    """
    logger.info("\n" + "=" * 80)
    logger.info("Terminal Status Summary")
    logger.info("=" * 80)
    
    running_count = sum(1 for details in terminal_details.values() if details["running"])
    visible_count = sum(1 for details in terminal_details.values() if details["window_visible"])
    
    logger.info(f"Total terminals: {len(terminal_details)}")
    logger.info(f"Running terminals: {running_count}")
    logger.info(f"Visible terminals: {visible_count}")
    
    logger.info("\nTerminal Details:")
    for terminal_id, details in terminal_details.items():
        status = "✓ Running" if details["running"] else "✗ Not Running"
        window = "✓ Visible" if details["window_visible"] else "✗ Not Visible"
        logger.info(f"Terminal {terminal_id}: {status}, {window}, Login: {details['login']}, Server: {details['server']}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Start All MT5 Terminals")
    parser.add_argument("--symbol", type=str, default="BTCUSD.a", help="Symbol to check")
    args = parser.parse_args()
    
    # Start all terminals and get their details
    terminal_details = start_all_terminals(args.symbol)
    
    # Display summary
    display_terminal_summary(terminal_details)
    
    logger.info("\nAll terminals have been started. You can now use them for trading.")
    logger.info("To connect to a specific terminal, use:")
    logger.info("python multi_mt5_connection.py --terminal <terminal_id>")
    
    # Keep the script running to allow terminals to fully initialize
    logger.info("\nPress Ctrl+C to exit...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Exiting...")

if __name__ == "__main__":
    main()
