"""
Configuration Service for the trading bot.
Legacy compatibility layer for the configuration system.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from .unified_config import config_manager, get_config, TradingConfig

logger = logging.getLogger(__name__)

class ConfigurationService:
    """
    Legacy configuration service for backwards compatibility.
    Wraps the UnifiedConfigManager to provide the old interface.
    """
    
    def __init__(self):
        """Initialize the configuration service."""
        self._config_manager = config_manager
        self._ensure_directories()
        logger.info("System directories created successfully")
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        directories = [
            'logs',
            'data',
            'models',
            'reports',
            'monitoring',
            'monitoring_output',
            'monitoring_output/model_performance'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_config(self) -> TradingConfig:
        """
        Get the trading configuration.
        
        Returns:
            TradingConfig: The trading configuration object
        """
        return self._config_manager.get_config()
    
    def get_mt5_config(self):
        """Get MT5 configuration."""
        return self._config_manager.get_mt5_config()
    
    def get_strategy_config(self):
        """Get strategy configuration."""
        return self._config_manager.get_strategy_config()
    
    def get_model_config(self, model_name: str):
        """Get model configuration."""
        return self._config_manager.get_model_config(model_name)
    
    def get_all_model_configs(self):
        """Get all model configurations."""
        return self._config_manager.get_all_model_configs()
    
    def get_system_config(self):
        """Get system configuration."""
        return self._config_manager.get_system_config()
    
    def get_monitoring_config(self):
        """Get monitoring configuration."""
        return self._config_manager.get_monitoring_config()
    
    def validate(self) -> bool:
        """
        Validate the configuration.
        
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            config = self.get_config()
            return config is not None
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

# Create global instance for backwards compatibility
configuration_service = ConfigurationService()

# Export for backwards compatibility
__all__ = ['ConfigurationService', 'configuration_service']
