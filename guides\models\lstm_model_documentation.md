# LSTM Model Documentation

## Table of Contents
1. [Model Architecture](#1-model-architecture)
2. [Configuration Requirements](#2-configuration-requirements)
3. [Model Setup and Training](#3-model-setup-and-training)
4. [Testing and Evaluation](#4-testing-and-evaluation)
5. [Environment Configuration](#5-environment-configuration)
6. [Best Practices](#6-best-practices)
7. [Example Usage](#7-example-usage)

## 1. Model Architecture

### 1.1 Core Components
- **Base Model**: Inherits from `BaseModel` class
- **LSTM Model**: Main model class implementing LSTM using PyTorch
- **Sequence Preparation**: Time series sequence handling
- **Data Preprocessing**: Feature scaling and normalization

### 1.2 Key Features
- Long Short-Term Memory networks
- Bidirectional processing
- Multi-layer architecture
- Dropout regularization
- Sequence-to-sequence prediction
- GPU acceleration support
- Mixed precision training

## 2. Configuration Requirements

### 2.1 Required Parameters
```python
LSTM_CONFIG = {
    'batch_size': 32,           # Batch size for training
    'learning_rate': 1e-4,      # Learning rate
    'hidden_size': 64,          # Hidden state size
    'num_layers': 2,            # Number of LSTM layers
    'dropout': 0.2,             # Dropout rate
    'bidirectional': True,      # Use bidirectional LSTM
    'sequence_length': 60,      # Input sequence length
    'weight_decay': 1e-5,       # Weight decay
    'gradient_clip_val': 0.1    # Gradient clipping
}
```

### 2.2 Data Requirements
- Time series data with timestamp index
- Required columns:
  - `time_idx`: Integer time index
  - `close`: Target variable
  - Feature columns for prediction
- Data should be preprocessed and normalized

## 3. Model Setup and Training

### 3.1 Initialization
```python
model = LSTMModel(config)
model.build(training_data)
```

### 3.2 Training Process
```python
history = model.train(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    epochs=100,
    batch_size=32
)
```

### 3.3 Key Training Features
- Automatic mixed precision training
- Gradient scaling
- Learning rate scheduling
- Early stopping
- Model checkpointing
- TensorBoard logging
- GPU acceleration

## 4. Testing and Evaluation

### 4.1 Test Configuration
```python
LSTM_TEST_CONFIG = {
    'batch_size': 16,           # Reduced for testing
    'learning_rate': 1e-4,
    'hidden_size': 32,          # Reduced for testing
    'num_layers': 1,            # Reduced for testing
    'max_epochs': 2,            # Minimal epochs
    'sequence_length': 60
}
```

### 4.2 Test Cases
1. Model Initialization
2. Data Loading
3. Sequence Preparation
4. Model Training
5. Prediction
6. Device Management

### 4.3 Evaluation Metrics
- Mean Absolute Error (MAE)
- Mean Squared Error (MSE)
- Root Mean Squared Error (RMSE)
- R-squared Score (R2)
- Mean Absolute Percentage Error (MAPE)
- Sequence Prediction Accuracy

## 5. Environment Configuration

### 5.1 Required Dependencies
```python
# Core dependencies
torch>=1.10.0
numpy>=1.19.0
pandas>=1.3.0
scikit-learn>=0.24.0

# Optional dependencies for GPU support
cuda-toolkit>=11.0  # For CUDA support
cudnn>=8.0         # For deep learning acceleration

# Testing dependencies
pytest>=6.0.0
pytest-cov>=2.12.0
```

### 5.2 Environment Setup
```bash
# Create and activate virtual environment
python -m venv lstm_env
source lstm_env/bin/activate  # Linux/Mac
lstm_env\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Install CUDA toolkit if using GPU
# Windows: Download and install from NVIDIA website
# Linux: sudo apt-get install nvidia-cuda-toolkit
```

## 6. Best Practices

### 6.1 Data Preparation
- Normalize time series data
- Handle missing values
- Create proper sequences
- Implement proper train/val split
- Use appropriate sequence length

### 6.2 Model Configuration
- Use appropriate hyperparameters
- Monitor training progress
- Save model checkpoints
- Log training metrics
- Enable mixed precision training
- Use GPU when available

### 6.3 Performance Optimization
- Use appropriate batch size
- Enable gradient scaling
- Monitor GPU memory usage
- Use mixed precision training
- Optimize sequence length
- Use persistent workers

## 7. Example Usage

```python
# Initialize model
model = LSTMModel(LSTM_CONFIG)

# Prepare data
X_train, y_train, X_val, y_val = prepare_sequences(data, sequence_length)

# Build and train model
model.build(X_train)
history = model.train(
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    epochs=100,
    batch_size=32
)

# Make predictions
predictions = model.predict(X_test)

# Get sequence predictions
sequence_predictions = model.predict_sequence(X_test)
```