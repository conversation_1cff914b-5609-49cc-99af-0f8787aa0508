"""
Utility for checking and monitoring Algo Trading status in MT5 terminals.
"""

import logging
import time
import MetaTrader5 as mt5
from typing import Dict, Optional, Union, List
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_algo_trading_status(terminal_id: Optional[Union[str, int]] = None) -> Dict[str, bool]:
    """
    Check if Algo Trading is enabled in the specified terminal or all terminals.

    Args:
        terminal_id: Optional terminal ID to check. If None, checks current connection.

    Returns:
        Dict[str, bool]: Dictionary mapping terminal IDs to Algo Trading status
    """
    results = {}

    try:
        # Check if MT5 is initialized
        if not mt5.terminal_info():
            logger.error("MT5 is not initialized")
            return results

        # Get terminal info
        terminal_info = mt5.terminal_info()

        # Check if Algo Trading is enabled using terminal_info.trade_allowed
        # This is the correct property to check, NOT account_info.trade_expert
        algo_trading_enabled = terminal_info.trade_allowed

        # Get terminal path
        terminal_path = terminal_info.path

        # Use terminal path as ID if no specific ID provided
        terminal_id_str = str(terminal_id) if terminal_id else Path(terminal_path).parent.name

        # Store result
        results[terminal_id_str] = algo_trading_enabled

        # Log detailed status
        if algo_trading_enabled:
            logger.info(f"[ENABLED] Algo Trading is ENABLED for terminal {terminal_id_str}")
            logger.info(f"Terminal path: {terminal_path}")
        else:
            logger.error(f"[DISABLED] Algo Trading is DISABLED for terminal {terminal_id_str}")
            logger.error(f"Terminal path: {terminal_path}")
            logger.error("To enable Algo Trading:")
            logger.error(f"1. Open the MT5 terminal at {terminal_path}")
            logger.error("2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
            logger.error("3. Verify that 'Algo Trading enabled' appears in the status bar")

    except Exception as e:
        logger.error(f"Error checking Algo Trading status: {str(e)}")

    return results

def monitor_algo_trading(interval: int = 60, duration: Optional[int] = None) -> None:
    """
    Monitor Algo Trading status at regular intervals.

    Args:
        interval: Check interval in seconds
        duration: Optional monitoring duration in seconds. If None, monitors indefinitely.
    """
    logger.info(f"Starting Algo Trading monitor with {interval}s interval")

    start_time = time.time()
    iteration = 1

    try:
        while True:
            logger.info(f"Check #{iteration} - Checking Algo Trading status...")

            # Check Algo Trading status
            results = check_algo_trading_status()

            # Count enabled/disabled terminals
            enabled_count = sum(1 for status in results.values() if status)
            disabled_count = len(results) - enabled_count

            # Log summary
            logger.info(f"Summary: {enabled_count} terminals with Algo Trading ENABLED, "
                       f"{disabled_count} terminals with Algo Trading DISABLED")

            # Check if monitoring duration has elapsed
            if duration and (time.time() - start_time) >= duration:
                logger.info(f"Monitoring duration of {duration}s elapsed. Stopping.")
                break

            # Wait for next check
            logger.info(f"Next check in {interval} seconds...")
            time.sleep(interval)
            iteration += 1

    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    except Exception as e:
        logger.error(f"Error during monitoring: {str(e)}")

def enable_algo_trading_instructions() -> None:
    """
    Print detailed instructions for enabling Algo Trading.
    """
    logger.info("=" * 80)
    logger.info("INSTRUCTIONS FOR ENABLING ALGO TRADING IN MT5")
    logger.info("=" * 80)
    logger.info("")
    logger.info("1. Open your MT5 terminal")
    logger.info("2. Look for the 'Algo Trading' button in the toolbar:")
    logger.info("   - It's usually located in the top toolbar")
    logger.info("   - It may be labeled as 'AutoTrading' or have a robot icon")
    logger.info("3. Click the 'Algo Trading' button to enable it:")
    logger.info("   - When enabled, the button should be highlighted (usually in green)")
    logger.info("   - When disabled, the button is not highlighted (usually gray)")
    logger.info("4. Verify that Algo Trading is enabled:")
    logger.info("   - 'AutoTrading enabled' should appear in the status bar at the bottom")
    logger.info("   - Or run this script again to check the status")
    logger.info("")
    logger.info("IMPORTANT NOTES:")
    logger.info("- You must manually enable Algo Trading in each MT5 terminal")
    logger.info("- Initializing MT5 with one terminal can disable Algo Trading in others")
    logger.info("- Always use portable=True when initializing MT5 to preserve settings")
    logger.info("- Avoid using mt5.shutdown() when possible to maintain Algo Trading")
    logger.info("=" * 80)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Check and monitor Algo Trading status in MT5 terminals')
    parser.add_argument('--monitor', action='store_true', help='Monitor Algo Trading status continuously')
    parser.add_argument('--interval', type=int, default=60, help='Monitoring interval in seconds')
    parser.add_argument('--duration', type=int, help='Monitoring duration in seconds')
    parser.add_argument('--instructions', action='store_true', help='Show instructions for enabling Algo Trading')

    args = parser.parse_args()

    if args.instructions:
        enable_algo_trading_instructions()
    elif args.monitor:
        monitor_algo_trading(interval=args.interval, duration=args.duration)
    else:
        check_algo_trading_status()
