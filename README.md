# Advanced Trading Bot System

A sophisticated trading bot system that combines multiple machine learning models with robust error handling and resource management.

## Features

### Core Functionality
- Multi-terminal MT5 integration with connection pooling
- Advanced data preprocessing with custom technical indicators
- Ensemble model system with multiple ML models
- Real-time trading signal generation and execution
- Comprehensive error handling and recovery system
- Context-aware model loading and management (`ModelManager`)

### Technical Features
- Memory-optimized data processing
- Circuit breaker pattern for system protection
- Multi-threaded execution with resource management
- Real-time performance monitoring
- Automated recovery mechanisms
- Centralized configuration management (`ConfigurationManager`)
- Advanced training visualization system

### Machine Learning Models
- LSTM (Long Short-Term Memory) - PyTorch implementation with GPU acceleration
- TFT (Temporal Fusion Transformer) - PyTorch implementation with GPU acceleration
- ARIMA (AutoRegressive Integrated Moving Average) - statsmodels implementation

## System Requirements

- Python 3.10+
- MetaTrader 5
- CUDA-capable GPU (optional)
- Minimum 8GB RAM
- Windows 10/11

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/trading-bot.git
cd trading-bot
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure MT5:
- Install MetaTrader 5
- Configure terminal settings (Paths, Login details)
- Update `config/config.json` with your MT5 terminal credentials and paths.

## Configuration

The main configuration is managed by `config/config.py` (`ConfigurationManager`) which reads `config/config.json`.

Key sections in `config/config.json`:

```json
{
  "mt5": {
    "max_connections": 5,
    "timeout": 60000,
    "retry_interval": 5,
    "terminals": {
      "1": {
        "login": "YOUR_LOGIN_1",
        "password": "YOUR_PASSWORD_1",
        "server": "YOUR_SERVER_1",
        "path": "C:/Path/To/MT5_Terminal_1/terminal64.exe"
      },
      "2": { ... }
    }
  },
  "strategy": {
    "symbol": "BTCUSD.a",
    "timeframes": ["M5", "M15"],
    "sequence_length": 288,
    "lot_size": 0.01,
    // ... other strategy parameters ...
  },
  "models": {
    "lstm": {
      "model_path": "lstm_model.h5", // <<< Filename only
      "input_dim": 50,
      "output_dim": 1,
      "weight": 0.2,
      "FEATURE_COLUMNS": [...],
      // ... other model params ...
    },
    "tft": {
      "model_path": "tft_model.pytorch", // <<< Filename only
       // ... other model params ...
    }
    // ... other models ...
  },
  "data_base_path": "data/", // <<< Base path for all data
  "models_base_path": "models/", // <<< Base path for all models
  "confidence_threshold": 0.65,
  "update_interval": 60,
  "max_memory_usage": 85.0,
  "log_level": "INFO",
  "debug_mode": false,
  "terminal_model_pairings": {
    "1": {
      "terminal_id": 1,
      "primary_model": "lstm",
      "allocation": 33
    },
    "2": {
      "terminal_id": 2,
      "primary_model": "arima",
      "allocation": 33
    },
    "3": {
      "terminal_id": 3,
      "primary_model": "tft",
      "allocation": 34
    }
  }
}
```

**Important Notes:**
- `data_base_path` and `models_base_path` define the root directories for data and model files.
- `model_path` within each model's configuration should **only** be the filename. The full path (e.g., `models/terminal_1/M5/lstm_model.h5`) is constructed automatically by the system based on the `models_base_path`, the current `terminal_id`, and `timeframe`.

## Usage

1. **Collect Data:** Use the unified data collection script
   ```bash
   # Example: Collect data for EURUSD M5 timeframe
   python collect_data.py --symbol EURUSD --timeframe M5 --start-date 2023-01-01 --end-date 2023-12-31

   # Example: Collect data for EURUSD across multiple timeframes
   python collect_data.py --symbol EURUSD --timeframes M5,H1,H4 --start-date 2023-01-01 --end-date 2023-12-31

   # Example: Collect data for multiple symbols on M5 timeframe
   python collect_data.py --symbols EURUSD,GBPUSD,USDJPY --timeframe M5 --start-date 2023-01-01 --end-date 2023-12-31
   ```

2. **Train Models:** Use the unified model training script
   ```bash
   # Example: Train LSTM model for EURUSD M5
   python train_model.py --model-type lstm --symbol EURUSD --timeframe M5

   # Example: Train LSTM model for EURUSD M5 with custom parameters
   python train_model.py --model-type lstm --symbol EURUSD --timeframe M5 --lstm-units 100 --dropout-rate 0.3

   # Example: Train ARIMA model for EURUSD M5 with custom parameters
   python train_model.py --model-type arima --symbol EURUSD --timeframe M5 --p 2 --d 1 --q 2 --use-exog
   ```

   **Alternative Training Methods:**
   ```bash
   # Train LSTM model for BTCUSD.a M5
   python train_lstm_single.py --timeframe M5 --target-column close

   # Train TFT model for BTCUSD.a M5
   python train_tft_single.py --timeframe M5 --target-column close

   # Train ARIMA model for BTCUSD.a M5
   python train_arima_single.py --timeframe M5 --target-column close --auto-arima

   # Train all ARIMA models for all timeframes
   # On Linux/Mac:
   ./train_all_arima_models.sh

   # On Windows:
   train_all_arima_models.bat
   ```

   **Flexible Model Selection**

   The system is designed to be flexible and modular, allowing you to choose which models to include in your ensemble. For detailed information, see the [Model Selection Guide](docs/model_selection_guide.md).

   Key features of the model selection system:

   1. **Training Individual Models**: Train each model type independently without affecting others
   2. **Selective Model Loading**: Load only the specific models you want to use
   3. **Adjustable Model Weights**: Control each model's influence in the ensemble
   4. **Configuration-Based Selection**: Use the config.json file to manage model selection
   5. **Dynamic Weight Adjustment**: Automatically adjust model weights based on market conditions

   The trading bot supports running with any combination of LSTM, TFT, and ARIMA models, allowing you to experiment and find the optimal setup for your trading strategy.

   For comprehensive step-by-step guides, see:
   - [Comprehensive Guide](guides/COMPREHENSIVE_GUIDE.md) - Complete overview of the entire system
   - [Data Collection Guide](guides/DATA_COLLECTION_GUIDE.md) - Detailed instructions for collecting and verifying data
   - [Model Training Guide](guides/MODEL_TRAINING_GUIDE.md) - In-depth guide for training and evaluating models
   - [Trading Execution Guide](guides/TRADING_EXECUTION_GUIDE.md) - Complete guide for configuring and running the trading bot
   - [Bot Control Guide](guides/BOT_CONTROL_GUIDE.md) - Detailed instructions for starting, stopping, and managing the bots

   All documentation is now organized in the [guides](guides/) directory.

3. **Run Trading Bot:**

   **Option 1: Standard Trading Bot**
   ```bash
   python main.py
   ```

   **Option 2: Independent Trading System**
   ```bash
   python independent_trading_bot.py
   ```

   **Option 3: Test Independent Trading System**
   ```bash
   # Test all terminals
   python test_independent_trading.py --iterations 3 --interval 300

   # Test specific terminal
   python test_independent_trading.py --terminal 1 --iterations 3 --interval 300
   ```

4. **Stop Trading Bot:**

   **Option 1: Using Keyboard Interrupt**
   ```bash
   # Press Ctrl+C in the terminal where the bot is running
   ```

   **Option 2: Using the Stop Script**
   ```bash
   python stop_bots.py
   ```

   **Option 3: Using the Bot Control Interface**
   ```bash
   # Stop all bots
   python bot_control.py stop

   # Stop a specific bot (by terminal ID)
   python bot_control.py stop --terminal 1
   ```

5. **Control Trading Bot:**
   ```bash
   # Start all bots
   python bot_control.py start

   # Stop all bots
   python bot_control.py stop

   # Stop a specific bot
   python bot_control.py stop --terminal 1

   # Show help
   python bot_control.py --help
   ```

6. **Monitor Performance:**
   ```bash
   # (Assuming monitor script exists)
   # python monitoring/monitor.py
   ```

7. **View Logs:** Logs are typically stored in the `logs/` directory.
   ```bash
   tail -f logs/main.log
   ```

## Project Structure

```
trading-bot/
├── config/             # Configuration files (config.py, config.json, schemas/)
├── data/               # Base directory for data (defined by data_base_path in config)
│   ├── terminal_1/
│   │   ├── M5_data.csv
│   │   └── ...
│   ├── training/       # Example subdirectory
│   └── cache/          # Example subdirectory
├── guides/            # Comprehensive documentation
│   ├── models/        # Model-specific documentation
│   │   ├── lstm_model_documentation.md
│   │   ├── tft_model_documentation.md
│   │   └── arima_model_documentation.md
│   ├── COMPREHENSIVE_GUIDE.md
│   ├── DATA_COLLECTION_GUIDE.md
│   ├── MODEL_TRAINING_GUIDE.md
│   ├── TRADING_EXECUTION_GUIDE.md
│   ├── BOT_CONTROL_GUIDE.md
│   └── README.md
├── logs/              # Log files
├── models/            # Base directory for models (defined by models_base_path in config)
│   ├── terminal_1/
│   │   ├── M5/
│   │   │   ├── lstm_model.h5
│   │   │   └── ...
│   │   └── ...
│   └── ...
├── monitoring/        # Performance monitoring tools
├── reports/           # Trading reports
├── tests/             # Test suite
├── trading/           # Trading logic (bot.py, strategy.py, executor.py, signal_generator.py)
├── utils/             # Utility functions & managers (model_manager.py, data_preprocessor.py etc.)
├── visualizations/    # Generated visualization files (HTML, PNG)
│   ├── model_comparison.html
│   ├── model_comparison.png
│   ├── training_progress.html
│   ├── training_time_comparison.html
│   └── training_time_comparison.png
├── main.py            # Main entry point to run the bot system
├── bot_control.py     # Script for controlling the trading bots
├── stop_bots.py       # Script for stopping all trading bots
├── requirements.txt   # Dependencies
├── collect_data.py    # Data collection script
├── train_models.py    # Model training script
└── visualize_training.py # Training visualization system
```

## Error Handling

The system includes comprehensive error handling:
- Automatic recovery from MT5 connection issues (managed by `MT5ConnectionManager`)
- Memory management and cleanup (managed by `MemoryManager`)
- Model error recovery (marking unhealthy models in `ModelManager`)
- Data processing error handling
- Circuit breaker pattern implementation

## Performance Monitoring

Real-time monitoring of:
- Trading performance (Profit, Win Rate, Drawdown)
- System resources (CPU, Memory, Disk)
- Error rates
- Model health status

## Best Practices

1. **Configuration:** Maintain all paths and core parameters within `config/config.json`. Use the `ConfigurationManager` singleton to access config values consistently.
2. **Error Handling:** Leverage the `ErrorHandler` and `CircuitBreaker` patterns. Implement specific recovery logic within relevant managers.
3. **Memory Management:** Monitor usage via `MemoryManager`. Optimize data handling in `DataPreprocessor` and models.
4. **Modularity:** Keep components decoupled. Use managers (`MT5ConnectionManager`, `ModelManager`, `ThreadManager`) to handle shared resources and context.

## Recent Improvements

1. **Enhanced Model Architecture:**
   - Streamlined the model architecture by focusing on LSTM, TFT, and ARIMA models
   - Migrated LSTM models from TensorFlow to PyTorch for better performance and consistency
   - Added ARIMA model for statistical time series forecasting to complement deep learning models
   - Implemented a flexible model selection system allowing any combination of models to be used
   - Removed XGBoost, GRU, and LightGBM models to reduce complexity and maintenance overhead
   - Updated all related components to work with the streamlined model set
   - Improved model loading and prediction efficiency
   - Added dynamic model weighting based on market regime detection
   - Standardized code style and documentation across all model implementations
   - Added comprehensive type hints for better code quality and IDE support
   - Improved error handling and logging in all model training scripts

2. **Unified Utilities and Modules:**
   - Consolidated redundant GPU testing scripts into a single `utils/gpu_utils.py` module optimized for PyTorch
   - Created a unified data collection system with `utils/data_collector.py` and `collect_data.py`
   - Implemented a modular model training framework with `utils/model_trainer.py`, `utils/pytorch_lstm_trainer.py`, and `utils/arima_trainer.py`
   - Added a cleanup utility to remove redundant scripts and files
   - Standardized command-line interfaces for all utilities
   - Removed TensorFlow-specific utilities and dependencies where possible
   - Implemented consistent logging configuration across all modules
   - Added comprehensive type hints to all utility functions
   - Improved error handling and recovery in all utility modules
   - Created a unified model comparison system with standardized metrics and visualizations

3. **Independent Trading with Proportional Ensemble Allocation:**
   - Each terminal operates independently with its own dedicated primary model
   - Each terminal uses all models but with different weights (primary model gets highest weight)
   - Capital/risk is allocated proportionally across terminals
   - Allocations are adjusted dynamically based on performance
   - See `INDEPENDENT_TRADING.md` for detailed documentation

4. **Enhanced MT5 Terminal Management:**
   - Improved MT5 connection system to reliably start all 5 terminals
   - Preserved Algo Trading functionality across all terminals
   - Added robust terminal process detection and management
   - Enhanced error handling and recovery for terminal connections

5. **Enhanced Error Recovery Strategies:**
   - Improved MT5 connection error recovery with multiple fallback mechanisms
   - Enhanced timeout error handling with connectivity checks
   - Added exponential backoff with jitter for network-related errors
   - Integrated with circuit breaker and graceful degradation systems

6. **Adaptive Resource Management:**
   - Dynamic resource allocation based on system load
   - Workload-aware throttling and prioritization
   - Automatic scaling of concurrent operations
   - Integration with memory manager and circuit breaker
   - Resource usage monitoring and adaptation

7. **Comprehensive Logging and Monitoring:**
   - Structured logging with context and correlation IDs
   - Performance tracking and metrics collection
   - Log aggregation and filtering
   - Automatic log rotation and cleanup
   - Integration with error handling and resource management

8. **System Stability & Consistency:**
   - Centralized configuration via `ConfigurationManager` and `config.json`.
   - Standardized path handling using `data_base_path` and `models_base_path`.
   - Context-aware `ModelManager` for specific terminal/timeframe model loading.
   - Refactored `TradingBot` to accept managers and context.
   - Updated utility scripts and test files for consistency.
   - Standardized code style and documentation across all modules.
   - Implemented consistent error handling and logging across all components.
   - Added comprehensive type hints for better code quality and IDE support.
   - Improved file organization and module structure.
   - Removed redundant and obsolete code.

9. **Performance:**
   - Optimized data processing
   - Efficient memory usage with adaptive management
   - Context-specific model loading
   - Resource-aware operation scheduling

10. **Advanced Visualization System:**
   - Comprehensive training visualization with interactive and static outputs
   - Realistic synthetic data generation for training curves
   - Model comparison with meaningful validation loss metrics
   - Training time analysis and comparison
   - Real-time monitoring of ongoing training

## Future Enhancements

1. **Planned Features:**
   - Advanced risk management (dynamic position sizing)
   - More sophisticated market regime detection and use in ensembling.
   - Hyperparameter optimization framework integration (Optuna results).
   - Robustness testing framework integration.

2. **System Improvements:**
   - GPU acceleration verification and optimization.
   - Caching mechanisms for preprocessed data and predictions.
   - Automated model retraining and deployment pipeline.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue in the GitHub repository or contact the maintainers.

## Training Visualization System

The trading bot includes a comprehensive visualization system for model training analysis, implemented in `visualize_training.py`.

### Key Features

- **Training Progress Visualization**: Shows training and validation loss curves for each model
- **Model Comparison**: Compares final validation loss across all models
- **Training Time Comparison**: Compares training duration across models
- **Interactive & Static Outputs**: Generates both HTML (interactive) and PNG (static) visualizations
- **Real-time Monitoring**: Supports monitoring of ongoing training with periodic updates
- **Realistic Synthetic Data**: Generates realistic training curves when detailed metrics are unavailable

### Usage

```bash
# Generate all visualizations
python visualize_training.py --log_file model_training.log

# Monitor training in real-time
python visualize_training.py --log_file model_training.log --monitor --interval 10

# Visualize specific models
python visualize_training.py --log_file model_training.log --models lstm tft arima
```

### Documentation

Detailed documentation is available in the `docs/` directory:

- `visualization_guide.md`: Comprehensive user guide
- `visualization_technical_reference.md`: Technical implementation details
- `visualization_quick_reference.md`: Quick reference for common tasks

## MT5 Connection Management

The trading bot implements a robust MT5 connection management system (`MT5ConnectionManager`) using connection pooling, configured via `config/config.json`.

### Configuration (`config.json`)
```json
{
  "mt5": {
    "max_connections": 5, // Max simultaneous connections pooled
    "timeout": 60000, // Connection attempt timeout
    "retry_interval": 5, // Seconds between reconnect attempts
    "terminals": {
      "1": { // Key is the Terminal ID (string)
        "login": "YOUR_LOGIN",
        "password": "YOUR_PASSWORD",
        "server": "YOUR_SERVER",
        "path": "C:/Path/To/MT5/terminal64.exe"
      },
      // ... other terminals
    }
  }
}
```

### Best Practices
1. Initialize `MT5ConnectionManager` once (e.g., in `main.py`) and pass the instance.
2. Use `mt5_manager.get_connection(terminal_id)` to get a connection.
3. The manager handles initialization, connection state, and automatic retries.
4. Do not call `mt5.shutdown()` directly; use `mt5_manager.shutdown_all()` during graceful system exit.