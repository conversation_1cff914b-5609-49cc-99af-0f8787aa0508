#!/usr/bin/env python
"""
TFT Model Training Script for a Single Timeframe

This script trains a Temporal Fusion Transformer (TFT) model on BTCUSD.a data
for a single timeframe using PyTorch with GPU acceleration.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime
import argparse
import matplotlib.pyplot as plt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train TFT model for a single timeframe')

    # Required arguments
    parser.add_argument('--timeframe', type=str, required=True,
                        help='Timeframe to train (e.g., M5, H1)')

    # Optional arguments
    parser.add_argument('--feature-columns', type=str, default='open,high,low,close,real_volume',
                        help='Comma-separated list of feature columns')
    parser.add_argument('--target-column', type=str, default='close',
                        help='Target column to predict')
    parser.add_argument('--sequence-length', type=int, default=60,
                        help='Length of input sequences')
    parser.add_argument('--hidden-size', type=int, default=64,
                        help='Hidden size for TFT model')
    parser.add_argument('--attention-head-size', type=int, default=4,
                        help='Number of attention heads')
    parser.add_argument('--dropout-rate', type=float, default=0.1,
                        help='Dropout rate')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--epochs', type=int, default=5,
                        help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Proportion of data to use for testing')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--use-gpu', action='store_true', default=True,
                        help='Use GPU for training if available')
    parser.add_argument('--data-dir', type=str, default='data/historical/btcusd.a',
                        help='Directory containing data files')

    return parser.parse_args()

def load_data(timeframe, data_dir):
    """Load data for a specific timeframe."""
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def train_tft_model(args):
    """Train TFT model for a specific timeframe."""
    try:
        # Try to import PyTorch and PyTorch Forecasting
        try:
            import torch
            import pytorch_lightning as pl
            from pytorch_forecasting import TemporalFusionTransformer, TimeSeriesDataSet
            from pytorch_forecasting.data import GroupNormalizer
            from pytorch_forecasting.metrics import QuantileLoss
            from pytorch_lightning.callbacks import EarlyStopping, LearningRateMonitor
            from pytorch_lightning.loggers import TensorBoardLogger
        except ImportError as e:
            logger.error(f"Required packages not installed: {str(e)}")
            logger.error("Please install PyTorch and PyTorch Forecasting: pip install torch pytorch_forecasting pytorch_lightning")
            return None

        # Load data
        df = load_data(args.timeframe, args.data_dir)
        if df is None:
            return None

        # Ensure time column exists and is datetime
        if 'time' not in df.columns:
            logger.error("Time column not found in data")
            return None

        df['time'] = pd.to_datetime(df['time'])

        # Parse feature columns
        feature_columns = args.feature_columns.split(',')

        # Add necessary columns for TimeSeriesDataSet
        df['time_idx'] = np.arange(len(df))
        df['group'] = 'BTCUSD.a'  # Single group for now

        # Determine training/validation cutoff
        training_cutoff = int(len(df) * (1 - args.test_size))

        # Set max encoder and prediction length
        max_encoder_length = args.sequence_length
        max_prediction_length = 1  # Predict one step ahead

        # Create training dataset
        training = TimeSeriesDataSet(
            data=df[:training_cutoff],
            time_idx="time_idx",
            target=args.target_column,
            group_ids=["group"],
            min_encoder_length=max_encoder_length // 2,
            max_encoder_length=max_encoder_length,
            min_prediction_length=1,
            max_prediction_length=max_prediction_length,
            static_categoricals=[],
            static_reals=[],
            time_varying_known_categoricals=[],
            time_varying_known_reals=[],
            time_varying_unknown_categoricals=[],
            time_varying_unknown_reals=feature_columns,
            target_normalizer=GroupNormalizer(
                groups=["group"], transformation="softplus"
            ),
            add_relative_time_idx=True,
            add_target_scales=True,
            add_encoder_length=True,
            randomize_length=True,
        )

        # Create validation dataset
        validation = TimeSeriesDataSet.from_dataset(
            training, df[training_cutoff:], predict=False, stop_randomization=True
        )

        # Create data loaders
        train_dataloader = training.to_dataloader(
            train=True, batch_size=args.batch_size, num_workers=0
        )
        val_dataloader = validation.to_dataloader(
            train=False, batch_size=args.batch_size, num_workers=0
        )

        # Create TFT model with proper Lightning wrapper
        class TFTLightningWrapper(pl.LightningModule):
            def __init__(self, tft_model):
                super().__init__()
                self.model = tft_model

            def forward(self, x):
                return self.model(x)

            def training_step(self, batch, batch_idx):
                # Set trainer reference for the wrapped model
                try:
                    if hasattr(self, 'trainer') and self.trainer is not None:
                        self.model.trainer = self.trainer
                except:
                    pass
                return self.model.training_step(batch, batch_idx)

            def validation_step(self, batch, batch_idx):
                # Set trainer reference for the wrapped model
                try:
                    if hasattr(self, 'trainer') and self.trainer is not None:
                        self.model.trainer = self.trainer
                except:
                    pass
                return self.model.validation_step(batch, batch_idx)

            def configure_optimizers(self):
                return self.model.configure_optimizers()

            def predict_step(self, batch, batch_idx, dataloader_idx=0):
                # Set trainer reference for the wrapped model
                try:
                    if hasattr(self, 'trainer') and self.trainer is not None:
                        self.model.trainer = self.trainer
                except:
                    pass
                return self.model.predict_step(batch, batch_idx, dataloader_idx)

            def setup(self, stage=None):
                """Setup hook to initialize trainer reference"""
                try:
                    if hasattr(self, 'trainer') and self.trainer is not None:
                        self.model.trainer = self.trainer
                        # Override the model's log method to use our log method
                        original_log = self.model.log
                        def wrapped_log(*args, **kwargs):
                            try:
                                return self.log(*args, **kwargs)
                            except:
                                return original_log(*args, **kwargs)
                        self.model.log = wrapped_log
                except:
                    pass

        # Create base TFT model
        base_tft = TemporalFusionTransformer.from_dataset(
            training,
            learning_rate=args.learning_rate,
            hidden_size=args.hidden_size,
            attention_head_size=args.attention_head_size,
            dropout=args.dropout_rate,
            hidden_continuous_size=args.hidden_size // 2,
            loss=QuantileLoss(),
            log_interval=-1,  # Disable all logging to avoid plotting issues
            log_val_interval=-1,  # Disable validation logging
            reduce_on_plateau_patience=3,
        )

        # Wrap in Lightning module
        tft = TFTLightningWrapper(base_tft)

        # Configure training settings - Force CPU for stability
        gpu_settings = {}
        # Force CPU training to avoid device mismatch issues
        gpu_settings["accelerator"] = "cpu"
        logger.info("Using CPU for training (forced for stability)")

        # Create PyTorch Lightning trainer
        logger_dir = Path("lightning_logs")
        logger_dir.mkdir(parents=True, exist_ok=True)

        logger_name = f"tft_BTCUSD.a_{args.timeframe}"

        # Create trainer
        trainer = pl.Trainer(
            max_epochs=args.epochs,
            gradient_clip_val=0.1,
            callbacks=[
                EarlyStopping(
                    monitor="val_loss", patience=10, mode="min"
                ),
                LearningRateMonitor(logging_interval="epoch"),
            ],
            logger=TensorBoardLogger(logger_dir, name=logger_name),
            **gpu_settings
        )

        # Train model
        logger.info(f"Training TFT model for BTCUSD.a {args.timeframe}")
        # Handle different PyTorch Lightning versions
        try:
            # Newer versions
            trainer.fit(
                tft,
                train_dataloaders=train_dataloader,
                val_dataloaders=val_dataloader,
            )
        except TypeError:
            # Older versions
            trainer.fit(
                tft,
                train_dataloader,
                val_dataloader,
            )

        # Save model
        model_dir = Path("models") / f"tft_BTCUSD.a_{args.timeframe}"
        model_dir.mkdir(parents=True, exist_ok=True)

        # Save best model
        best_model_path = trainer.checkpoint_callback.best_model_path
        if best_model_path:
            # Load the wrapper from checkpoint and extract the base model
            best_wrapper = TFTLightningWrapper.load_from_checkpoint(best_model_path)
            best_tft = best_wrapper.model
            best_tft.save(model_dir / "best_model.pt")
            logger.info(f"Best model saved to {model_dir / 'best_model.pt'}")
        else:
            # Use the current model if no checkpoint available
            best_tft = base_tft

        # Evaluate model
        predictions = best_tft.predict(val_dataloader, return_y=True)

        # Extract predictions and actual values
        y_pred = predictions.output.detach().cpu().numpy()
        y_true = predictions.y.detach().cpu().numpy()

        # Calculate metrics
        mse = np.mean((y_pred - y_true) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_pred - y_true))

        # Calculate R² (coefficient of determination)
        y_mean = np.mean(y_true)
        ss_total = np.sum((y_true - y_mean) ** 2)
        ss_residual = np.sum((y_true - y_pred) ** 2)
        r2 = 1 - (ss_residual / ss_total)

        logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

        # Save metrics
        metrics = {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'model_type': 'tft',
            'timeframe': args.timeframe,
            'feature_columns': feature_columns,
            'target_column': args.target_column,
            'sequence_length': args.sequence_length,
            'hidden_size': args.hidden_size,
            'attention_head_size': args.attention_head_size,
            'dropout_rate': args.dropout_rate,
            'learning_rate': args.learning_rate,
            'epochs': args.epochs,
            'batch_size': args.batch_size,
            'training_samples': training_cutoff,
            'validation_samples': len(df) - training_cutoff,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Save metrics to file
        os.makedirs('metrics', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = f"metrics/tft_BTCUSD.a_{args.timeframe}_{timestamp}.json"

        with open(metrics_file, 'w') as f:
            json.dump(metrics, f, indent=4)

        logger.info(f"Metrics saved to {metrics_file}")

        # Plot predictions vs actual
        plt.figure(figsize=(12, 6))
        plt.plot(y_true[:100], label='Actual')
        plt.plot(y_pred[:100], label='Predicted')
        plt.legend()
        plt.title(f'TFT Model Predictions vs Actual for {args.timeframe}')

        # Save plot
        os.makedirs('plots', exist_ok=True)
        plt.savefig(f'plots/tft_predictions_{args.timeframe}.png')
        plt.close()

        return metrics

    except Exception as e:
        logger.error(f"Error training TFT model: {str(e)}", exc_info=True)
        return None

def main():
    """Main function."""
    args = parse_args()
    train_tft_model(args)

if __name__ == "__main__":
    main()
