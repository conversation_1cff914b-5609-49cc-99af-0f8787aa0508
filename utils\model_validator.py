"""
Model validation utility to verify model files, compatibility, and weights.
"""

import os
import torch
import pickle
import logging
from typing import Dict, Any
from config.unified_config import UnifiedConfigManager as ConfigManager
import json

logger = logging.getLogger(__name__)

class ModelValidator:
    def __init__(self):
        self.config = ConfigManager()
        self.model_paths = {
            'lstm': self.config.model_config.lstm.model_path,
            'tft': self.config.model_config.tft.model_path,
            'arima': self.config.model_config.arima.model_path
        }

    def validate_model_files(self) -> Dict[str, bool]:
        """Verify that all model files exist in specified paths."""
        results = {}
        for model_name, path in self.model_paths.items():
            exists = os.path.exists(path)
            results[model_name] = exists
            if not exists:
                logger.error(f"Model file not found: {model_name} at {path}")
        return results

    def validate_model_compatibility(self) -> Dict[str, Dict[str, Any]]:
        """Check model compatibility with current system."""
        results = {}

        # Check PyTorch models
        for model_name in ['lstm']:
            path = self.model_paths[model_name]
            try:
                model = torch.load(path, map_location='cpu')
                results[model_name] = {
                    'compatible': True,
                    'input_dim': model.input_dim,
                    'output_dim': model.output_dim,
                    'device_compatible': True
                }
            except Exception as e:
                logger.error(f"Error loading {model_name} model: {str(e)}")
                results[model_name] = {
                    'compatible': False,
                    'error': str(e)
                }

        # Check TFT model
        model_name = 'tft'
        path = self.model_paths[model_name]
        try:
            # TFT models are typically saved in a directory
            if os.path.isdir(path):
                results[model_name] = {
                    'compatible': True,
                    'device_compatible': True
                }
            else:
                raise FileNotFoundError(f"TFT model directory not found at {path}")
        except Exception as e:
            logger.error(f"Error validating {model_name} model: {str(e)}")
            results[model_name] = {
                'compatible': False,
                'error': str(e)
            }

        # Check ARIMA model
        model_name = 'arima'
        path = self.model_paths[model_name]
        try:
            with open(path, 'rb') as f:
                model = pickle.load(f)
            results[model_name] = {
                'compatible': True,
                'version_compatible': True
            }
        except Exception as e:
            logger.error(f"Error loading {model_name} model: {str(e)}")
            results[model_name] = {
                'compatible': False,
                'error': str(e)
            }

        return results

    def validate_model_weights(self) -> Dict[str, Dict[str, Any]]:
        """Validate model weights and performance."""
        results = {}

        # Check PyTorch model weights
        for model_name in ['lstm']:
            path = self.model_paths[model_name]
            try:
                model = torch.load(path, map_location='cpu')
                weights = {name: param.data for name, param in model.named_parameters()}

                # Check for NaN or Inf values
                has_nan = any(torch.isnan(w).any() for w in weights.values())
                has_inf = any(torch.isinf(w).any() for w in weights.values())

                results[model_name] = {
                    'weights_valid': not (has_nan or has_inf),
                    'has_nan': has_nan,
                    'has_inf': has_inf,
                    'weight_stats': {
                        name: {
                            'mean': float(w.mean()),
                            'std': float(w.std()),
                            'min': float(w.min()),
                            'max': float(w.max())
                        } for name, w in weights.items()
                    }
                }
            except Exception as e:
                logger.error(f"Error validating {model_name} weights: {str(e)}")
                results[model_name] = {
                    'weights_valid': False,
                    'error': str(e)
                }

        # For TFT and ARIMA models, we just check if they exist
        for model_name in ['tft', 'arima']:
            path = self.model_paths[model_name]
            try:
                if os.path.exists(path):
                    results[model_name] = {
                        'weights_valid': True,
                        'file_exists': True
                    }
                else:
                    results[model_name] = {
                        'weights_valid': False,
                        'file_exists': False
                    }
            except Exception as e:
                logger.error(f"Error validating {model_name} model: {str(e)}")
                results[model_name] = {
                    'weights_valid': False,
                    'error': str(e)
                }

        return results

    def generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report."""
        return {
            'files_exist': self.validate_model_files(),
            'compatibility': self.validate_model_compatibility(),
            'weights': self.validate_model_weights()
        }

def main():
    """Run model validation and generate report."""
    validator = ModelValidator()
    report = validator.generate_validation_report()

    # Log validation results
    logger.info("Model Validation Report:")
    logger.info(json.dumps(report, indent=2))

    return report

if __name__ == "__main__":
    main()