"""
<PERSON><PERSON>t to run all model tests in parallel.
"""
import os
import sys
import logging
import concurrent.futures
from pathlib import Path
from datetime import datetime
from typing import List, Dict

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tests.models.test_lstm_model import LSTMTest
from tests.models.test_tft_model import TestTFTModel as TFTTest
from tests.models.test_arima_model import ARIMATest

def run_test(test_class):
    """Run a single model test.

    Args:
        test_class: Test class to instantiate and run

    Returns:
        Tuple of (model name, success status, error message if any)
    """
    try:
        test = test_class()
        test.test_model()
        return test.model_name, True, None
    except Exception as e:
        return test.model_name, False, str(e)

def main():
    """Run all model tests in parallel."""
    try:
        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path("test_results") / "all_models" / timestamp
        output_dir.mkdir(parents=True, exist_ok=True)

        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(output_dir / "all_tests.log"),
                logging.StreamHandler()
            ]
        )
        logger = logging.getLogger("all_tests")

        logger.info("Starting parallel model tests")

        # Define test classes
        test_classes = [
            LSTMTest,
            TFTTest,
            ARIMATest
        ]

        # Run tests in parallel
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(test_classes)) as executor:
            future_to_test = {executor.submit(run_test, test_class): test_class for test_class in test_classes}
            for future in concurrent.futures.as_completed(future_to_test):
                test_class = future_to_test[future]
                try:
                    model_name, success, error = future.result()
                    results.append({
                        'model': model_name,
                        'success': success,
                        'error': error
                    })
                    if success:
                        logger.info(f"{model_name} test completed successfully")
                    else:
                        logger.error(f"{model_name} test failed: {error}")
                except Exception as e:
                    logger.error(f"Error running test: {str(e)}")

        # Generate summary report
        summary = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'total_tests': len(results),
            'successful_tests': sum(1 for r in results if r['success']),
            'failed_tests': sum(1 for r in results if not r['success']),
            'results': results
        }

        # Save summary
        summary_path = output_dir / "test_summary.json"
        with open(summary_path, 'w') as f:
            import json
            json.dump(summary, f, indent=4)

        logger.info(f"Test summary saved to {summary_path}")

        # Print final status
        logger.info("\nTest Summary:")
        logger.info(f"Total tests: {len(results)}")
        logger.info(f"Successful: {summary['successful_tests']}")
        logger.info(f"Failed: {summary['failed_tests']}")

        if summary['failed_tests'] > 0:
            logger.error("\nFailed tests:")
            for result in results:
                if not result['success']:
                    logger.error(f"{result['model']}: {result['error']}")

        logger.info(f"\nAll test results saved to {output_dir}")

    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        raise

if __name__ == "__main__":
    main()