#!/usr/bin/env python
"""
Trading Bot Runner Script
Initializes and runs the trading bot with proper configuration and error handling.
"""
import os
import sys
import argparse
import logging
import signal
import time
from pathlib import Path
import traceback
from datetime import datetime

# Setup logging early
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/bot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)

logger = logging.getLogger(__name__)

# Add project root to path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(SCRIPT_DIR)

# Import after path setup
from utils.config_manager import ConfigurationManager
from utils.memory_manager import MemoryManager
from utils.error_handler import ErrorHandler
from utils.mt5.mt5_connection_manager import MT5ConnectionManager
from trading.bot import TradingBot

class TradingBotRunner:
    """
    Runner class for the Trading Bot that handles initialization,
    monitoring, and proper shutdown.
    """

    def __init__(self, config_file: str, debug: bool = False):
        """
        Initialize the trading bot runner.

        Args:
            config_file: Path to the configuration file
            debug: Whether to run in debug mode
        """
        self.config_file = config_file
        self.debug = debug
        self.running = False
        self.bot = None
        self.config_manager = None
        self.error_handler = None

        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)

        # Set log level based on debug flag
        if debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logger.info("Debug mode enabled")

        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info(f"TradingBotRunner initialized with config: {config_file}")

    def _signal_handler(self, sig, frame):
        """Handle termination signals for graceful shutdown."""
        logger.info(f"Received signal {sig}, shutting down...")
        self.stop()

    def initialize(self) -> bool:
        """
        Initialize all components required for the trading bot.

        Returns:
            bool: Whether initialization was successful
        """
        try:
            logger.info("Initializing components...")

            # Initialize error handler first for early error trapping
            self.error_handler = ErrorHandler()

            # Initialize configuration manager
            self.config_manager = ConfigurationManager(
                config_file=self.config_file,
                schema_dir="config/schemas",
                auto_load=True
            )

            # Check if configuration loaded successfully
            if not self.config_manager.config_loaded:
                logger.error("Failed to load configuration")
                return False

            # Get configuration values for logging
            config = self.config_manager.get_config()
            log_level = config.get("log_level", "INFO").upper()
            logging.getLogger().setLevel(getattr(logging, log_level))

            # Set up error handling
            self.config_manager.error_handler = self.error_handler

            # Register MT5 error handlers for recovery
            self.config_manager.register_mt5_error_handlers()

            # Start memory monitoring
            self.config_manager.start_memory_monitoring()

            # Initialize trading bot
            strategy_config = config.get("strategy", {})
            models_config = config.get("models", {})

            self.bot = TradingBot(
                config_manager=self.config_manager,
                error_handler=self.error_handler,
                strategy_params=strategy_config,
                model_params=models_config
            )

            logger.info("Initialization completed successfully")
            return True

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "initialize"},
                    source="TradingBotRunner"
                )
            else:
                logger.error(f"Initialization failed: {str(e)}")
                logger.debug(traceback.format_exc())
            return False

    def run(self) -> bool:
        """
        Run the trading bot.

        Returns:
            bool: Whether the bot ran successfully or encountered a fatal error
        """
        if not self.initialize():
            logger.error("Failed to initialize components, aborting")
            return False

        try:
            logger.info("Starting trading bot...")
            self.running = True

            # Use the error handler for safe execution
            result = self.error_handler.safe_execute(
                self.bot.run,
                retry_count=3,
                retry_delay=5,
                context={"method": "run"}
            )

            if result is not True:
                logger.error("Trading bot run method returned an error")
                return False

            # Monitor bot until stopped
            while self.running:
                time.sleep(1)

                # Check if bot is still alive
                if not self.bot.is_running():
                    logger.warning("Trading bot stopped unexpectedly")
                    self.running = False
                    break

            return True

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "run"},
                    source="TradingBotRunner"
                )
            else:
                logger.error(f"Error running trading bot: {str(e)}")
                logger.debug(traceback.format_exc())
            return False
        finally:
            # Ensure cleanup happens even on error
            self.stop()

    def stop(self) -> None:
        """Stop the trading bot and clean up resources."""
        if not self.running:
            return

        logger.info("Stopping trading bot...")
        self.running = False

        try:
            # Stop the bot if it's running
            if self.bot and self.bot.is_running():
                self.bot.stop()

            # Clean up resources
            if self.config_manager:
                self.config_manager.clean_up()

            logger.info("Trading bot stopped successfully")

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "stop"},
                    source="TradingBotRunner"
                )
            else:
                logger.error(f"Error stopping trading bot: {str(e)}")
                logger.debug(traceback.format_exc())

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run the trading bot')
    parser.add_argument('--config', '-c', type=str, default='config/config.json',
                        help='Path to the configuration file')
    parser.add_argument('--debug', '-d', action='store_true',
                        help='Enable debug mode')
    return parser.parse_args()

def main():
    """Main entry point for the script."""
    args = parse_arguments()

    # Ensure config file exists
    if not os.path.exists(args.config):
        logger.error(f"Configuration file not found: {args.config}")
        return 1

    # Create and run the bot
    runner = TradingBotRunner(args.config, args.debug)
    success = runner.run()

    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())