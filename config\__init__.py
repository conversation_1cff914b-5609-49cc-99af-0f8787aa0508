"""
Configuration package for the trading bot.
Provides centralized configuration management through the UnifiedConfigManager.
"""
from .unified_config import (
    config_manager,
    get_config,
    get_mt5_config,
    get_strategy_config,
    get_model_config,
    get_all_model_configs,
    get_system_config,
    get_monitoring_config,
    get_data_base_path,
    get_models_base_path,
    TradingConfig,
    MT5Config,
    StrategyConfig,
    ModelConfig,
    SystemConfig,
    MonitoringConfig,
    MT5TerminalConfig
)

# For backwards compatibility
from .config import ConfigurationManager
from .service import ConfigurationService, configuration_service

__all__ = [
    'config_manager',
    'get_config',
    'get_mt5_config',
    'get_strategy_config',
    'get_model_config',
    'get_all_model_configs',
    'get_system_config',
    'get_monitoring_config',
    'get_data_base_path',
    'get_models_base_path',
    'TradingConfig',
    'MT5Config',
    'StrategyConfig',
    'ModelConfig',
    'SystemConfig',
    'MonitoringConfig',
    'MT5TerminalConfig',
    'ConfigurationManager',
    'ConfigurationService',
    'configuration_service'
]