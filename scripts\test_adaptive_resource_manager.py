"""
Test script for the adaptive resource manager implementation.
"""
import os
import sys
import logging
import time
import threading
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_adaptive_resource_manager():
    """Test the adaptive resource manager implementation."""
    try:
        from utils.adaptive_resource_manager import (
            AdaptiveResourceManager,
            ResourceType,
            ResourcePriority,
            ResourceUsage,
            ResourceLimits,
            acquire_operation_permit,
            release_operation_permit,
            with_operation_permit,
            get_resource_usage,
            get_resource_limits,
            get_active_operations
        )

        logger.info("Testing adaptive resource manager implementation")

        # Get the singleton instance
        resource_manager = AdaptiveResourceManager.get_instance()

        # Test 1: Resource usage monitoring
        logger.info("Test 1: Resource usage monitoring")

        # Get current resource usage
        usage = get_resource_usage()
        logger.info(f"CPU usage: {usage.cpu_percent:.1f}%")
        logger.info(f"Memory usage: {usage.memory_percent:.1f}%")
        logger.info(f"Disk usage: {usage.disk_percent:.1f}%")
        logger.info(f"Thread count: {usage.thread_count}")

        # Get resource limits
        limits = get_resource_limits()
        logger.info(f"CPU limit: {limits.max_cpu_percent:.1f}%")
        logger.info(f"Memory limit: {limits.max_memory_percent:.1f}%")
        logger.info(f"Thread limit: {limits.max_threads}")

        # Test 2: Operation permits
        logger.info("\nTest 2: Operation permits")

        # Set operation limit
        operation_type = "test_operation"
        operation_limit = 3
        resource_manager.set_operation_limit(operation_type, operation_limit)

        # Get active operations before test
        active_before = get_active_operations()
        logger.info(f"Active operations before test: {active_before}")

        # Test acquiring and releasing permits
        permits_acquired = 0
        for i in range(operation_limit + 2):  # Try to acquire more than the limit
            if acquire_operation_permit(operation_type, timeout=0.1):
                permits_acquired += 1
                logger.info(f"Acquired permit {permits_acquired}/{operation_limit}")
            else:
                logger.info(f"Failed to acquire permit {i+1} (expected after {operation_limit})")

        # Get active operations during test
        active_during = get_active_operations()
        logger.info(f"Active operations during test: {active_during}")
        assert active_during.get(operation_type, 0) == permits_acquired, "Active operations count mismatch"

        # Release all permits
        for i in range(permits_acquired):
            release_operation_permit(operation_type)
            logger.info(f"Released permit {i+1}")

        # Get active operations after test
        active_after = get_active_operations()
        logger.info(f"Active operations after test: {active_after}")
        assert active_after.get(operation_type, 0) == 0, "All permits should be released"

        # Test 3: Decorator usage
        logger.info("\nTest 3: Decorator usage")

        # Define decorated function
        @with_operation_permit("decorated_operation", timeout=0.1)  # Shorter timeout to ensure failures
        def decorated_function(sleep_time=0.5):  # Longer sleep to ensure concurrent execution
            logger.info(f"Decorated function running, sleeping for {sleep_time}s")
            time.sleep(sleep_time)
            return "Decorated function result"

        # Set a low limit for this operation
        resource_manager.set_operation_limit("decorated_operation", 2)

        # Run decorated function in multiple threads
        results = []
        threads = []

        def run_decorated():
            try:
                result = decorated_function()
                results.append(result)
            except Exception as e:
                logger.info(f"Decorated function failed as expected: {str(e)}")

        # Start multiple threads
        for i in range(5):  # More than our limit of 2
            thread = threading.Thread(target=run_decorated)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        logger.info(f"Decorated function results: {len(results)}")
        assert len(results) <= 2, "Should have at most 2 successful results due to permit limit"

        # Test 4: Resource adaptation
        logger.info("\nTest 4: Resource adaptation")

        # Register a callback for CPU adaptation
        cpu_adaptation_called = [False]

        def cpu_adaptation_callback(resource_type, usage):
            logger.info(f"CPU adaptation callback called: {usage.cpu_percent:.1f}%")
            cpu_adaptation_called[0] = True

        resource_manager.register_resource_callback(ResourceType.CPU, cpu_adaptation_callback)

        # Simulate high CPU usage by creating a CPU-intensive task
        def cpu_intensive_task():
            logger.info("Starting CPU-intensive task")
            start_time = time.time()
            while time.time() - start_time < 2.0:  # Run for 2 seconds
                # Perform CPU-intensive calculation
                for i in range(1000000):
                    _ = i ** 2
            logger.info("CPU-intensive task completed")

        # Run CPU-intensive task in a separate thread
        cpu_thread = threading.Thread(target=cpu_intensive_task)
        cpu_thread.start()
        cpu_thread.join()

        # Wait for monitoring to detect high CPU
        logger.info("Waiting for resource monitoring to run...")
        time.sleep(10)

        # Check if adaptation callback was called
        logger.info(f"CPU adaptation callback called: {cpu_adaptation_called[0]}")

        # Test 5: Concurrent operations
        logger.info("\nTest 5: Concurrent operations")

        # Define a function that simulates an operation
        def simulated_operation(operation_type, duration=0.2):
            if acquire_operation_permit(operation_type, timeout=1.0):
                try:
                    logger.info(f"Running {operation_type} operation for {duration:.1f}s")
                    time.sleep(duration)
                    return True
                finally:
                    release_operation_permit(operation_type)
            else:
                logger.info(f"Failed to acquire permit for {operation_type}")
                return False

        # Set limits for different operation types
        resource_manager.set_operation_limit("high_priority", 5)
        resource_manager.set_operation_limit("medium_priority", 3)
        resource_manager.set_operation_limit("low_priority", 1)

        # Run operations concurrently
        operation_threads = []
        operation_results = {
            "high_priority": 0,
            "medium_priority": 0,
            "low_priority": 0
        }

        def run_operation(operation_type):
            result = simulated_operation(operation_type)
            if result:
                operation_results[operation_type] += 1

        # Start multiple threads for each operation type
        for _ in range(10):
            for op_type in operation_results.keys():
                thread = threading.Thread(target=run_operation, args=(op_type,))
                operation_threads.append(thread)
                thread.start()

        # Wait for all threads to complete
        for thread in operation_threads:
            thread.join()

        # Check results
        logger.info(f"Operation results: {operation_results}")
        assert operation_results["high_priority"] <= 10, "High priority operations exceeded"
        assert operation_results["medium_priority"] <= 10, "Medium priority operations exceeded"
        assert operation_results["low_priority"] <= 10, "Low priority operations exceeded"

        logger.info("Adaptive resource manager test passed")
        return True

    except Exception as e:
        logger.error(f"Error testing adaptive resource manager: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_adaptive_resource_manager():
        logger.info("Adaptive resource manager test passed")
        sys.exit(0)
    else:
        logger.error("Adaptive resource manager test failed")
        sys.exit(1)
