"""
Setup script for the trading bot configuration.
This script helps users set up their configuration files.
"""
import os
import sys
import json
import shutil
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

def setup_config():
    """Set up configuration files."""
    print("Setting up configuration files...")
    
    # Check if config.json exists
    config_path = project_root / "config" / "config.json"
    config_example_path = project_root / "config" / "config.json.example"
    
    if not config_path.exists() and config_example_path.exists():
        print(f"Creating config.json from example...")
        shutil.copy(config_example_path, config_path)
        print(f"Created {config_path}")
    elif not config_path.exists():
        print(f"Error: {config_example_path} not found. Cannot create config.json.")
        return False
    else:
        print(f"Config file {config_path} already exists.")
    
    # Check if local_config.json exists
    local_config_path = project_root / "config" / "local_config.json"
    local_config_template_path = project_root / "config" / "local_config_template.json"
    
    if not local_config_path.exists() and local_config_template_path.exists():
        print(f"Creating local_config.json from template...")
        shutil.copy(local_config_template_path, local_config_path)
        print(f"Created {local_config_path}")
        print("Please edit local_config.json with your MT5 credentials.")
    elif not local_config_path.exists():
        print(f"Error: {local_config_template_path} not found. Cannot create local_config.json.")
        return False
    else:
        print(f"Local config file {local_config_path} already exists.")
    
    # Create directories
    directories = [
        project_root / "data",
        project_root / "models",
        project_root / "logs",
        project_root / "data" / "cache",
        project_root / "monitoring",
        project_root / "monitoring" / "plots",
        project_root / "monitoring" / "reports"
    ]
    
    for directory in directories:
        if not directory.exists():
            print(f"Creating directory {directory}...")
            directory.mkdir(parents=True, exist_ok=True)
    
    print("Configuration setup complete.")
    return True

def update_gitignore():
    """Update .gitignore file to exclude sensitive files."""
    gitignore_path = project_root / ".gitignore"
    
    # Entries to add to .gitignore
    entries = [
        "# Sensitive configuration files",
        "config/local_config.json",
        "config/credentials.py",
        "",
        "# Data and model files",
        "data/",
        "models/",
        "logs/",
        "monitoring/",
        "*.log",
        "*.h5",
        "*.pkl",
        "*.json.bak",
        ""
    ]
    
    # Read existing .gitignore
    existing_entries = []
    if gitignore_path.exists():
        with open(gitignore_path, "r") as f:
            existing_entries = f.read().splitlines()
    
    # Add new entries
    with open(gitignore_path, "a+") as f:
        for entry in entries:
            if entry not in existing_entries:
                f.write(f"{entry}\n")
    
    print("Updated .gitignore file.")
    return True

def main():
    """Main function."""
    print("Trading Bot Configuration Setup")
    print("==============================")
    
    if not setup_config():
        print("Error setting up configuration files.")
        return 1
    
    if not update_gitignore():
        print("Error updating .gitignore file.")
        return 1
    
    print("\nSetup complete!")
    print("Please edit config/local_config.json with your MT5 credentials.")
    print("You can now run the trading bot with: python main.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
