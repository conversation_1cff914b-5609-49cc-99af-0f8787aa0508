# ARIMA Model Documentation

## Overview

The ARIMA (AutoRegressive Integrated Moving Average) model is a statistical time series forecasting method that has been integrated into the trading bot. ARIMA models are particularly effective for capturing linear temporal dependencies in financial time series data. The implementation includes both standard ARIMA models and an advanced ensemble approach that combines multiple ARIMA models for improved performance.

## Model Architecture

### Standard ARIMA

ARIMA models are defined by three parameters:
- **p**: The order of the autoregressive (AR) component
- **d**: The degree of differencing required to make the time series stationary
- **q**: The order of the moving average (MA) component

The model can be represented as ARIMA(p,d,q) where:
- AR(p) component: Uses past values to predict future values
- I(d) component: Applies differencing to make the series stationary
- MA(q) component: Uses past forecast errors in a moving average model

### Ensemble ARIMA

The ensemble ARIMA approach combines multiple ARIMA models with different configurations:

1. **Base Models**: The ensemble includes several ARIMA models with different parameters:
   - ARIMA(5,d,5): The best-performing configuration based on empirical testing
   - ARIMA(2,d,2): A balanced model with moderate complexity
   - ARIMA(5,d,0): A model focused on autoregressive components
   - ARIMA(0,d,5): A model focused on moving average components
   - ARIMA(4,d,2): Another effective configuration for capturing trends
   - ARIMA(3,d,3): A balanced model with moderate complexity
   - Auto ARIMA: An automatically optimized model with improved settings

2. **Meta-Model Selection**: The system automatically selects the best meta-model from multiple candidates:
   - Gradient Boosting Regressor
   - Random Forest Regressor
   - Extra Trees Regressor
   - ElasticNet
   - Ridge Regression

3. **Model Weighting**: Models are weighted based on their individual performance on the training data

4. **Data Preprocessing**: Includes standardization, feature engineering, and dimensionality reduction

## Implementation Details

### Class Structure

The ARIMA models are implemented in two main classes:

1. **Standard ARIMA**: Implemented in the `ARIMAModel` class in `utils/arima_trainer.py`. This class provides methods for training, prediction, and model management.

2. **Ensemble ARIMA**: Implemented in the `EnsembleARIMAModel` class in `models/ensemble_arima_model.py`. This class manages multiple ARIMA models and combines their predictions.

### Key Methods for Standard ARIMA

1. **__init__(config)**: Initializes the ARIMA model with configuration parameters
2. **build()**: Prepares the model structure
3. **train(X, y)**: Trains the ARIMA model on the provided data
4. **predict(X)**: Generates predictions using the trained model
5. **save_model(path)**: Saves the trained model to disk
6. **load_model(path)**: Loads a trained model from disk

### Key Methods for Ensemble ARIMA

1. **__init__(config)**: Initializes the ensemble with configuration parameters
2. **build()**: Prepares the ensemble structure
3. **train(train_data, exog)**: Trains multiple ARIMA models and the meta-model
4. **predict(n_periods, exog, X)**: Generates predictions by combining outputs from all models
5. **_train_meta_model(train_data, exog)**: Trains the meta-model that combines base model predictions and calculates model weights

### Configuration Parameters

#### Standard ARIMA Configuration

```json
"arima": {
  "model_path": "arima_model.pkl",
  "input_dim": 5,
  "output_dim": 1,
  "weight": 0.2,
  "sequence_length": 60,
  "p": 1,
  "d": 1,
  "q": 1,
  "seasonal_p": 0,
  "seasonal_d": 0,
  "seasonal_q": 0,
  "seasonal_m": 0,
  "use_seasonal": false,
  "auto_arima": true,
  "use_exog": false,
  "forecast_steps": 1,
  "FEATURE_COLUMNS": [
    "open", "high", "low", "close", "volume"
  ]
}
```

#### Ensemble ARIMA Configuration

```json
"ensemble_arima": {
  "model_path": "ensemble_arima_model.pkl",
  "input_dim": 5,
  "output_dim": 1,
  "weight": 0.3,
  "sequence_length": 60,
  "n_models": 5,
  "d": 1,
  "seasonal": false,
  "seasonal_m": 0,
  "use_exog": true,
  "forecast_steps": 1,
  "FEATURE_COLUMNS": [
    "open", "high", "low", "close", "volume"
  ]
}
```

### Parameter Descriptions

#### Standard ARIMA Parameters
- **p, d, q**: The main ARIMA parameters
- **seasonal_p, seasonal_d, seasonal_q, seasonal_m**: Parameters for seasonal ARIMA (SARIMA)
- **use_seasonal**: Whether to use seasonal components
- **auto_arima**: Whether to automatically determine optimal p, d, q values
- **use_exog**: Whether to use exogenous variables in the model
- **forecast_steps**: Number of steps to forecast ahead
- **FEATURE_COLUMNS**: Data columns to use for training

#### Ensemble ARIMA Parameters
- **n_models**: Number of base models in the ensemble
- **d**: Differencing order (shared across all models)
- **seasonal**: Whether to use seasonal components
- **seasonal_m**: Seasonal period (if seasonal is true)
- **use_exog**: Whether to use exogenous variables
- **max_rows**: Maximum number of rows to use for training (0 for all data)
- **data_selection**: Method for selecting data ('recent', 'all', or 'sample')

## Training Process

### Data Preparation

1. **Data Loading**: Historical data is loaded from parquet files
2. **Feature Engineering**:
   - Technical indicators (RSI, MACD, Bollinger Bands, etc.)
   - Rolling statistics (mean, std, min, max, quantiles)
   - Lag features and differences
   - Time-based features (hour, day, month, etc.)
3. **Stationarity Testing**: ADF test is used to check for stationarity
4. **Differencing**: Data is differenced if necessary to achieve stationarity
5. **Seasonality Detection**: ACF analysis to detect seasonal patterns
6. **Train-Test Split**: Data is split into training and testing sets

### Standard ARIMA Model Fitting

The ARIMA model is fitted using the statsmodels library:

```python
from statsmodels.tsa.arima.model import ARIMA

# For manual parameter specification
model = ARIMA(y_train, order=(p, d, q))
model_fit = model.fit()

# For auto ARIMA
from pmdarima import auto_arima
model = auto_arima(y_train, seasonal=use_seasonal,
                  m=seasonal_m if use_seasonal else 1,
                  exogenous=X_train if use_exog else None)
```

### Ensemble ARIMA Model Fitting

The ensemble ARIMA approach trains multiple models with an advanced meta-model selection process:

```python
# Initialize ensemble model
ensemble = EnsembleARIMAModel(config={
    'n_models': 7,  # Increased number of base models
    'd': suggested_d,
    'seasonal': use_seasonal,
    'seasonal_m': seasonal_m
})

# Train with exogenous variables if available
if use_exog:
    ensemble.train(train_data, exog=train_exog)
else:
    ensemble.train(train_data)

# The training process includes:
# 1. Training multiple base ARIMA models with different configurations
# 2. Calculating performance-based weights for each model
# 3. Evaluating multiple meta-models using time series cross-validation
# 4. Selecting the best meta-model based on performance metrics
# 5. Training the selected meta-model on the full dataset
```

### Prediction Generation

#### Standard ARIMA Predictions

```python
# For one-step forecasts
forecast = model_fit.forecast(steps=forecast_steps, exog=X_test if use_exog else None)

# For multi-step forecasts
forecasts = []
for i in range(forecast_steps):
    forecast = model_fit.forecast(steps=1, exog=X_test[i:i+1] if use_exog else None)
    forecasts.append(forecast)
```

#### Ensemble ARIMA Predictions

```python
# Generate predictions with the ensemble
predictions = ensemble.predict(n_periods=len(test_data), exog=test_exog)

# The prediction process includes:
# 1. Getting predictions from all base models
# 2. Applying dimensionality reduction to exogenous variables if needed
# 3. Using the trained meta-model to combine base model predictions
# 4. Falling back to weighted averaging if meta-model fails
# 5. Using model weights calculated during training for optimal combination
```

## Performance Characteristics

### Standard ARIMA Strengths

1. **Statistical Foundation**: Based on well-established statistical principles
2. **Interpretability**: Parameters have clear statistical interpretations
3. **Efficiency**: Computationally efficient compared to deep learning models
4. **Performance on Stationary Data**: Works well when data exhibits stationarity
5. **Confidence Intervals**: Provides statistical confidence intervals for forecasts

### Ensemble ARIMA Strengths

1. **Improved Accuracy**: Combines multiple models for better predictions
2. **Robustness**: Less sensitive to outliers and noise
3. **Adaptability**: Can capture different patterns in the data
4. **Feature Integration**: Effectively uses engineered features
5. **Generalization**: Better performance on unseen data
6. **Automatic Model Selection**: Selects the best meta-model for the specific dataset
7. **Performance-Based Weighting**: Weights models based on their individual performance
8. **Fallback Mechanisms**: Multiple fallback strategies ensure reliable predictions

### Standard ARIMA Limitations

1. **Linearity**: Assumes linear relationships in the data
2. **Stationarity Requirement**: Requires stationary data (handled by differencing)
3. **Limited Pattern Recognition**: May not capture complex non-linear patterns
4. **Manual Tuning**: May require manual parameter tuning if auto_arima is not used
5. **Single Variable Focus**: Primarily designed for univariate time series

### Ensemble ARIMA Limitations

1. **Computational Cost**: More computationally intensive than standard ARIMA
2. **Complexity**: More complex to understand and interpret
3. **Training Data Requirements**: Requires more data for effective training
4. **Hyperparameter Sensitivity**: Performance depends on ensemble configuration
5. **Memory Usage**: Higher memory requirements for multiple models

## Integration with Trading Bot

### Signal Generation

Both ARIMA models generate trading signals by:
1. Forecasting future price movements
2. Comparing forecasts to current prices
3. Generating buy/sell signals based on the direction and magnitude of predicted changes

### Ensemble Integration

#### Standard ARIMA Integration
- Default weight of 20% in the ensemble
- Complementary role to LSTM and TFT models
- Particularly valuable during periods of linear, trending markets

#### Ensemble ARIMA Integration
- Default weight of 30% in the ensemble (higher than standard ARIMA)
- Can replace standard ARIMA for improved performance
- Particularly effective in volatile or complex market conditions
- Can be used alongside LSTM and TFT models for a more robust ensemble

## Optimization Strategies

### Standard ARIMA Parameter Tuning

1. **Auto ARIMA**: Use auto_arima for automatic parameter selection
2. **Grid Search**: Manually test different p, d, q combinations
3. **Information Criteria**: Use AIC or BIC to select optimal models
4. **Cross-Validation**: Use time series cross-validation for parameter selection

### Ensemble ARIMA Optimization

1. **Base Model Selection**: Choose appropriate models for the ensemble
2. **Meta-Model Tuning**: Optimize the meta-model parameters
3. **Ensemble Size**: Adjust the number of models in the ensemble
4. **Weighting Strategy**: Experiment with different model weighting approaches

### Data Selection Strategies

1. **All Data**: Use all available historical data for training
2. **Recent Data**: Use only the most recent data for faster training
3. **Sampled Data**: Sample data at regular intervals for a representative subset
4. **Configurable Limits**: Adjust the maximum number of rows based on available resources

### Performance Improvement

1. **Feature Engineering**: Add relevant exogenous variables
   - Technical indicators
   - Rolling statistics
   - Time-based features
   - Volatility measures
2. **Seasonal Components**: Enable seasonal components if appropriate
3. **Data Transformation**: Apply appropriate transformations (log, Box-Cox)
4. **Outlier Handling**: Identify and handle outliers in the training data
5. **Feature Selection**: Use SelectKBest to choose the most important features

## Example Usage

### Standard ARIMA Example

```python
from utils.arima_trainer import ARIMAModel

# Create configuration
config = {
    'model_name': 'arima_btcusd_m5',
    'p': 2,
    'd': 1,
    'q': 2,
    'auto_arima': True,
    'use_exog': False,
    'forecast_steps': 1
}

# Initialize and build model
model = ARIMAModel(config)
model.build()

# Train model
history = model.train(X_train, y_train)

# Make predictions
predictions = model.predict(X_test)

# Save model
model.save_model('models/arima_btcusd_m5')
```

### Ensemble ARIMA Example

```python
from models.ensemble_arima_model import EnsembleARIMAModel

# Create configuration
config = {
    'n_models': 7,  # Increased number of base models
    'd': 1,
    'seasonal': False,
    'seasonal_m': 0,
    'use_exog': True
}

# Initialize and build ensemble model
ensemble = EnsembleARIMAModel(config)
ensemble.build()

# Train ensemble with exogenous variables
ensemble.train(train_data, exog=train_exog)

# The training process automatically:
# - Trains multiple base ARIMA models
# - Evaluates and selects the best meta-model
# - Calculates performance-based weights for each model

# Make predictions
predictions = ensemble.predict(n_periods=len(test_data), exog=test_exog)

# Save model
with open('models/ensemble_arima_btcusd_m5.pkl', 'wb') as f:
    pickle.dump(ensemble, f)
```

### Command-Line Usage

```bash
# Train standard ARIMA model
python train_arima_single.py --timeframe M5 --target-column close --auto-arima

# Train ensemble ARIMA model
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --use-ensemble --ensemble-models 5

# Train with data selection options
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --max-rows 50000 --data-selection all
```

## Model Loading and Path Resolution

The ARIMA model implementation includes a robust model loading mechanism that searches for model files in multiple locations:

```python
# Try multiple possible model paths
possible_paths = [
    # Path 1: Standard path with terminal_id and timeframe
    Path('models') / terminal_id / timeframe / f"{model_name}_{symbol}_{timeframe}",

    # Path 2: Direct path used by train_arima_single.py
    Path('models') / f"{model_name}_{symbol}_{timeframe}",

    # Path 3: Alternative path format
    Path('models') / f"{model_name}_{timeframe}_{symbol}_{timeframe}",

    # Path 4: Simple path with just the model name and timeframe
    Path('models') / f"{model_name}_{timeframe}",

    # Path 5: Terminal-specific model directory
    Path('models') / terminal_id / timeframe / f"{model_name}",

    # Path 6: Terminal-specific model directory with symbol
    Path('models') / terminal_id / timeframe / f"{model_name}_{symbol}",

    # Path 7: Terminal-specific model directory with symbol and timeframe
    Path('models') / terminal_id / f"{model_name}_{symbol}_{timeframe}"
]

# Also try direct .pkl files with various naming conventions
if not model_file.exists():
    model_file = Path('models') / f"{model_name}_{timeframe}_{symbol}_{timeframe}.pkl"

if not model_file.exists():
    model_file = Path('models') / f"{model_name}_{timeframe}_{symbol}_{timeframe}.pkl.pkl"

if not model_file.exists():
    model_file = Path('models') / f"{model_name}_{symbol}_{timeframe}.pkl"

if not model_file.exists():
    model_file = Path('models') / f"{model_name}_{symbol}_{timeframe}.pkl.pkl"

if not model_file.exists():
    model_file = Path('models') / terminal_id / timeframe / f"{model_name}_{symbol}_{timeframe}.pkl"
```

This flexible path resolution ensures that models can be found regardless of how they were trained or where they were saved. The system will try multiple locations and file naming conventions until it finds a valid model file.

## Conclusion

The ARIMA models provide a statistically sound approach to time series forecasting that complements the deep learning models in the trading bot. The standard ARIMA model offers efficiency and interpretability, while the ensemble ARIMA approach provides improved accuracy and robustness. Together, they form a powerful component of the trading system, particularly effective in capturing different types of market conditions and patterns.
