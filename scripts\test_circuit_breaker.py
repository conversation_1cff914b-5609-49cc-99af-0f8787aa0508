"""
Test script for the circuit breaker implementation.
"""
import os
import sys
import logging
import time
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_circuit_breaker():
    """Test the circuit breaker implementation."""
    try:
        from utils.circuit_breaker import CircuitBreaker, CircuitOpenError, CircuitState
        
        logger.info("Testing circuit breaker implementation")
        
        # Create a circuit breaker with a low failure threshold for testing
        circuit = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=5.0,
            half_open_max_calls=1,
            name="test_circuit"
        )
        
        # Test function that sometimes fails
        def unstable_operation(should_fail=False):
            if should_fail:
                raise ValueError("Simulated failure")
            return "Success"
        
        # Test successful execution
        logger.info("Testing successful execution")
        for i in range(5):
            try:
                result = circuit.execute(unstable_operation)
                logger.info(f"Execution {i+1} successful: {result}")
            except Exception as e:
                logger.error(f"Execution {i+1} failed: {str(e)}")
        
        # Test failure threshold
        logger.info("Testing failure threshold")
        failure_count = 0
        for i in range(5):
            try:
                result = circuit.execute(unstable_operation, should_fail=True)
                logger.info(f"Execution {i+1} successful: {result}")
            except CircuitOpenError as e:
                logger.info(f"Circuit open as expected: {str(e)}")
                failure_count += 1
            except Exception as e:
                logger.info(f"Execution {i+1} failed as expected: {str(e)}")
                failure_count += 1
        
        # Verify circuit is open
        assert circuit.state == CircuitState.OPEN, "Circuit should be open after failures"
        logger.info(f"Circuit state after failures: {circuit.state}")
        
        # Get circuit stats
        stats = circuit.get_stats()
        logger.info(f"Circuit stats: {stats}")
        
        # Wait for recovery timeout
        logger.info(f"Waiting for recovery timeout ({circuit.recovery_timeout} seconds)...")
        time.sleep(circuit.recovery_timeout + 1)
        
        # Verify circuit is half-open
        assert circuit.state == CircuitState.HALF_OPEN, "Circuit should be half-open after timeout"
        logger.info(f"Circuit state after timeout: {circuit.state}")
        
        # Test successful recovery
        logger.info("Testing successful recovery")
        try:
            result = circuit.execute(unstable_operation)
            logger.info(f"Recovery successful: {result}")
            assert circuit.state == CircuitState.CLOSED, "Circuit should be closed after successful recovery"
        except Exception as e:
            logger.error(f"Recovery failed: {str(e)}")
        
        logger.info(f"Final circuit state: {circuit.state}")
        
        # Reset the circuit
        circuit.reset()
        logger.info(f"Circuit reset, state: {circuit.state}")
        
        # Test registry functions
        retrieved_circuit = CircuitBreaker.get("test_circuit")
        assert retrieved_circuit is circuit, "Retrieved circuit should be the same instance"
        
        all_circuits = CircuitBreaker.get_all()
        assert "test_circuit" in all_circuits, "Circuit should be in registry"
        
        logger.info("Circuit breaker test passed")
        return True
        
    except Exception as e:
        logger.error(f"Error testing circuit breaker: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_circuit_breaker():
        logger.info("Circuit breaker test passed")
        sys.exit(0)
    else:
        logger.error("Circuit breaker test failed")
        sys.exit(1)
