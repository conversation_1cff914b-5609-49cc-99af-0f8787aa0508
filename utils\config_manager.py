"""
Configuration Manager for the trading bot.
Handles loading, validation, and management of all configuration settings.
Integrates with MT5ConnectionManager, MemoryManager, and ErrorHandler for robust operations.
"""

import os
import json
import logging
import time
import threading
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Tuple
import traceback
import copy

from utils.config_validator import ConfigVali<PERSON><PERSON>, ConfigError
from utils.memory_manager import MemoryManager
from utils.error_handler import <PERSON><PERSON>r<PERSON>and<PERSON>
from utils.mt5.mt5_connection_manager import MT5ConnectionManager

logger = logging.getLogger(__name__)

class ConfigurationManager:
    """
    Manages the configuration for the trading bot.
    Handles loading, validation, and application of settings.
    Provides thread-safe access to configuration values.
    Integrates with MT5ConnectionManager, MemoryManager, and ErrorHandler.
    """

    def __init__(self,
                config_file: str = 'config/config.json',
                schema_dir: str = 'config/schemas',
                auto_load: bool = True):
        """
        Initialize the configuration manager.

        Args:
            config_file: Path to the JSON configuration file
            schema_dir: Path to directory containing JSON schemas
            auto_load: Whether to load the configuration automatically on initialization
        """
        self.config_file = Path(config_file)
        self.schema_dir = Path(schema_dir)
        self.config_loaded = False
        self.config_lock = threading.RLock()

        # Configuration dictionary
        self._config = {}

        # Components
        self.validator = ConfigValidator(config_schemas_dir=schema_dir)
        self.mt5_manager = None
        self.memory_manager = None
        self.error_handler = None

        # Load configuration if auto_load is True
        if auto_load and self.config_file.exists():
            self.load_config()

    def load_config(self, config_file: Optional[str] = None) -> bool:
        """
        Load configuration from the specified JSON file.

        Args:
            config_file: Path to configuration file to load (overrides the default)

        Returns:
            bool: Whether the configuration was loaded successfully
        """
        if config_file:
            self.config_file = Path(config_file)

        try:
            # Check if config file exists
            if not self.config_file.exists():
                logger.error(f"Configuration file not found: {self.config_file}")
                return False

            # Load configuration
            with self.config_file.open('r') as f:
                config = json.load(f)

            # Validate configuration
            is_valid, errors = self.validator.validate_config(config)
            if not is_valid:
                logger.error(f"Invalid configuration in {self.config_file}:")
                for error in errors:
                    logger.error(f"  - {error}")
                return False

            # Apply the configuration
            with self.config_lock:
                self._config = config
                self.config_loaded = True

                # Initialize components with the new configuration
                self._initialize_components()

            logger.info(f"Successfully loaded configuration from {self.config_file}")
            return True

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing configuration file {self.config_file}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return False

    def _initialize_components(self) -> None:
        """Initialize or update components with the current configuration."""
        try:
            # Initialize MT5 connection manager if it doesn't exist
            if self.mt5_manager is None:
                self.mt5_manager = MT5ConnectionManager(self._config.get('mt5', {}))
            else:
                # Update existing MT5 manager with new config
                self.mt5_manager.update_config(self._config.get('mt5', {}))

            # Initialize Memory manager if it doesn't exist
            if self.memory_manager is None:
                max_memory_usage = self._config.get('max_memory_usage', 90.0)
                check_interval = self._config.get('memory_check_interval', 60)
                self.memory_manager = MemoryManager(
                    warning_threshold=max(50.0, max_memory_usage - 10),
                    critical_threshold=max_memory_usage,
                    check_interval=check_interval
                )
            else:
                # Update existing memory manager with new config
                max_memory_usage = self._config.get('max_memory_usage', 90.0)
                self.memory_manager.warning_threshold = max(50.0, max_memory_usage - 10)
                self.memory_manager.critical_threshold = max_memory_usage
                self.memory_manager.check_interval = self._config.get('memory_check_interval', 60)

            # Initialize Error handler if it doesn't exist
            if self.error_handler is None:
                self.error_handler = ErrorHandler(
                    max_history=self._config.get('error_history_limit', 1000)
                )

        except Exception as e:
            logger.error(f"Error initializing components: {str(e)}")

    def reload_config(self) -> bool:
        """
        Reload the configuration from the current config file.

        Returns:
            bool: Whether the configuration was reloaded successfully
        """
        return self.load_config(self.config_file)

    def save_config(self, config_file: Optional[str] = None) -> bool:
        """
        Save the current configuration to a JSON file.

        Args:
            config_file: Path to save the configuration to (defaults to the current config file)

        Returns:
            bool: Whether the configuration was saved successfully
        """
        if not self.config_loaded:
            logger.error("Cannot save configuration: No configuration loaded")
            return False

        if config_file:
            target_file = Path(config_file)
        else:
            target_file = self.config_file

        try:
            # Create directory if it doesn't exist
            target_file.parent.mkdir(parents=True, exist_ok=True)

            # Save configuration
            with target_file.open('w') as f:
                json.dump(self._config, f, indent=2)

            logger.info(f"Configuration saved to {target_file}")
            return True

        except Exception as e:
            logger.error(f"Error saving configuration to {target_file}: {str(e)}")
            return False

    def get_config(self, deep_copy: bool = True) -> Dict[str, Any]:
        """
        Get the current configuration.

        Args:
            deep_copy: Whether to return a deep copy of the configuration

        Returns:
            Dict: Current configuration
        """
        with self.config_lock:
            if not self.config_loaded:
                logger.warning("Configuration not loaded, returning empty config")
                return {}

            if deep_copy:
                return copy.deepcopy(self._config)
            else:
                return self._config

    def get_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get a configuration value by key path.

        Args:
            key_path: Dot-separated path to the configuration value (e.g., 'strategy.symbol')
            default: Default value to return if the key doesn't exist

        Returns:
            Any: Configuration value or default if not found
        """
        if not self.config_loaded:
            logger.warning(f"Configuration not loaded, returning default for {key_path}")
            return default

        with self.config_lock:
            # Start with the complete configuration
            current = self._config

            # Split the key path and traverse the configuration
            keys = key_path.split('.')
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default

            return current

    def set_value(self, key_path: str, value: Any, save: bool = False) -> bool:
        """
        Set a configuration value by key path.

        Args:
            key_path: Dot-separated path to the configuration value
            value: Value to set
            save: Whether to save the configuration to file after setting the value

        Returns:
            bool: Whether the value was set successfully
        """
        if not self.config_loaded:
            logger.error(f"Cannot set value for {key_path}: No configuration loaded")
            return False

        with self.config_lock:
            # Start with the complete configuration
            current = self._config

            # Split the key path and traverse the configuration
            keys = key_path.split('.')

            # Navigate to the second last key
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                elif not isinstance(current[key], dict):
                    logger.error(f"Cannot set value for {key_path}: {key} is not a dictionary")
                    return False

                current = current[key]

            # Set the value
            current[keys[-1]] = value

            # Validate the updated configuration
            is_valid, errors = self.validator.validate_config(self._config)
            if not is_valid:
                # Restore the previous configuration
                self.reload_config()
                logger.error(f"Cannot set value for {key_path}: Invalid configuration")
                for error in errors:
                    logger.error(f"  - {error}")
                return False

            # Save the configuration if requested
            if save:
                return self.save_config()

            return True

    def create_default_config(self, output_file: Optional[str] = None) -> bool:
        """
        Create a default configuration file.

        Args:
            output_file: Path to save the default configuration to

        Returns:
            bool: Whether the default configuration was created successfully
        """
        return self.validator.generate_config_template(
            output_file or str(self.config_file)
        )

    def validate_current_config(self) -> Tuple[bool, List[str]]:
        """
        Validate the current configuration.

        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_errors)
        """
        if not self.config_loaded:
            return False, ["No configuration loaded"]

        with self.config_lock:
            return self.validator.validate_config(self._config)

    def register_mt5_error_handlers(self) -> None:
        """
        Register recovery handlers for common MT5 errors.
        This helps with automated recovery from transient issues.
        """
        if not self.error_handler or not self.mt5_manager:
            logger.warning("Cannot register MT5 error handlers: error_handler or mt5_manager not initialized")
            return

        import MetaTrader5 as mt5
        import socket
        import time

        # Define recovery handlers for different error types
        def handle_connection_error(error, context):
            """Handler for connection errors with MT5"""
            terminal_id = context.get('terminal_id', None)
            logger.info(f"Attempting to recover from MT5 connection error for terminal {terminal_id}")

            if terminal_id is None:
                return False

            # Force shutdown and reinitialize the connection
            self.mt5_manager.shutdown(terminal_id)
            time.sleep(2)  # Give some time for resources to be released

            # Try to reconnect
            conn = self.mt5_manager.get_connection(terminal_id)
            return conn and conn.is_connected

        def handle_socket_error(error, context):
            """Handler for socket-related errors"""
            terminal_id = context.get('terminal_id', None)
            logger.info(f"Attempting to recover from socket error for terminal {terminal_id}")

            if terminal_id is None:
                return False

            # For socket errors, we should wait a bit longer before reconnecting
            self.mt5_manager.shutdown(terminal_id)
            time.sleep(5)  # Longer wait for network stability

            # Try to reconnect
            conn = self.mt5_manager.get_connection(terminal_id)
            return conn and conn.is_connected

        def handle_timeout_error(error, context):
            """Handler for timeout errors"""
            terminal_id = context.get('terminal_id', None)
            logger.info(f"Attempting to recover from timeout error for terminal {terminal_id}")

            if terminal_id is None:
                return False

            # Get current connection without reconnecting
            conn = self.mt5_manager.get_connection_status(terminal_id)

            # If still connected, just retry the operation
            if conn and conn.is_connected:
                return True

            # Otherwise reconnect
            self.mt5_manager.shutdown(terminal_id)
            time.sleep(3)
            conn = self.mt5_manager.get_connection(terminal_id)
            return conn and conn.is_connected

        # Register handlers for specific error types
        self.error_handler.register_recovery_handler(mt5.error.ConnectionError, handle_connection_error)
        self.error_handler.register_recovery_handler(mt5.error.TimeoutError, handle_timeout_error)
        self.error_handler.register_recovery_handler(socket.error, handle_socket_error)
        self.error_handler.register_recovery_handler(ConnectionRefusedError, handle_connection_error)
        self.error_handler.register_recovery_handler(ConnectionResetError, handle_connection_error)
        self.error_handler.register_recovery_handler(ConnectionAbortedError, handle_connection_error)
        self.error_handler.register_recovery_handler(TimeoutError, handle_timeout_error)

        logger.info("MT5 error recovery handlers registered")

    def start_memory_monitoring(self) -> None:
        """
        Start continuous memory monitoring in a background thread.
        This helps prevent memory leaks and OOM conditions.
        """
        if not self.memory_manager:
            logger.warning("Cannot start memory monitoring: memory_manager not initialized")
            return

        # Get memory monitoring configuration
        config = self.get_config()
        memory_config = config.get('system', {}).get('memory', {})

        # Set memory thresholds from config or use defaults
        warning_threshold = memory_config.get('warning_threshold', 75)
        critical_threshold = memory_config.get('critical_threshold', 90)
        check_interval = memory_config.get('check_interval', 60)

        # Configure memory manager
        self.memory_manager.warning_threshold = warning_threshold
        self.memory_manager.critical_threshold = critical_threshold
        self.memory_manager.check_interval = check_interval

        # Start monitoring in a background thread
        import threading
        self.memory_monitor_thread = threading.Thread(
            target=self.memory_manager.monitor_memory,
            daemon=True,
            name="MemoryMonitor"
        )
        self.memory_monitor_thread.start()

        logger.info(f"Memory monitoring started with warning={warning_threshold}%, "
                   f"critical={critical_threshold}%, interval={check_interval}s")

    def stop_memory_monitoring(self) -> None:
        """Stop the memory monitoring thread."""
        if self.memory_manager:
            self.memory_manager.stop_monitoring()

    def clean_up(self) -> None:
        """
        Clean up resources when shutting down.
        This ensures proper resource release.
        """
        logger.info("Cleaning up configuration manager resources")

        # Shutdown MT5 connections
        if self.mt5_manager:
            try:
                self.mt5_manager.shutdown_all()
                logger.info("All MT5 connections shut down")
            except Exception as e:
                logger.error(f"Error shutting down MT5 connections: {str(e)}")

        # Clean up memory manager resources
        if hasattr(self, 'memory_monitor_thread') and self.memory_monitor_thread.is_alive():
            # We can't directly stop the thread, but we can signal it to stop
            # by setting a flag in the memory manager
            if hasattr(self.memory_manager, 'stop_monitoring'):
                self.memory_manager.stop_monitoring = True
                logger.info("Signaled memory monitor to stop")

        logger.info("Configuration manager cleanup completed")

    def safe_get_mt5_connection(self, terminal_id: Optional[int] = None):
        """
        Safely get an MT5 connection using error handling.

        Args:
            terminal_id: Terminal ID to connect to (if None, uses the default)

        Returns:
            The MT5 connection object or None if connection failed
        """
        if not self.mt5_manager or not self.error_handler:
            logger.error("MT5ConnectionManager or ErrorHandler not initialized")
            return None

        # Use the error handler to safely execute the connection
        return self.error_handler.safe_execute(
            self.mt5_manager.get_connection,
            args=(terminal_id,),
            retry_count=3,
            retry_delay=5,
            context={'terminal_id': terminal_id}
        )

    def __enter__(self):
        """Context manager entry - allows using config manager with 'with' statement."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - ensures resources are properly cleaned up."""
        self.clean_up()
        return False  # Don't suppress exceptions