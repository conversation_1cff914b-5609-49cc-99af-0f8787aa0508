# Bot Control Guide

This guide provides detailed information about controlling the trading bot system, including starting, stopping, and managing the bots.

## Table of Contents

1. [Introduction](#introduction)
2. [Bot Control Scripts](#bot-control-scripts)
   - [stop_bots.py](#stop_botspy)
   - [bot_control.py](#bot_controlpy)
3. [Starting the Trading Bot](#starting-the-trading-bot)
4. [Stopping the Trading Bot](#stopping-the-trading-bot)
5. [Managing Individual Bots](#managing-individual-bots)
6. [Troubleshooting](#troubleshooting)

## Introduction

The trading bot system includes several scripts for controlling the bots. These scripts provide a convenient way to start, stop, and manage the trading bots programmatically.

## Bot Control Scripts

### stop_bots.py

The `stop_bots.py` script is a simple utility for stopping all running trading bots. It initializes the necessary managers, creates a TradingBotManager instance, and calls the `stop_all_bots()` method to stop all running bots.

```python
"""
Script to stop all running trading bots.
"""
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/stop_bots.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import unified configuration manager
from config.unified_config import config_manager

# Import necessary managers
from utils.enhanced_error_handler import EnhancedErrorHandler
error_handler = EnhancedErrorHandler()

from utils.enhanced_memory_manager import enhanced_memory_manager
memory_manager = enhanced_memory_manager

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32,
    thread_name_prefix="Main"
)

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
mt5_manager = MT5ConnectionManager(config_manager)

# Import TradingBotManager
from main import TradingBotManager

def stop_all_bots():
    """Stop all running trading bots."""
    logger.info("Initializing bot manager to stop all bots...")

    # Create a manager instance
    manager = TradingBotManager(
        config_manager=config_manager,
        error_handler=error_handler,
        mt5_manager=mt5_manager,
        thread_manager=thread_manager,
        memory_manager=memory_manager
    )

    # Stop all bots
    logger.info("Stopping all trading bots...")
    success = manager.stop_all_bots()

    if success:
        logger.info("All trading bots stopped successfully.")
    else:
        logger.error("Failed to stop all trading bots.")

    # Perform full shutdown
    logger.info("Performing full shutdown...")
    manager.shutdown()

    return success

if __name__ == "__main__":
    try:
        success = stop_all_bots()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Error stopping bots: {e}", exc_info=True)
        sys.exit(1)
```

### bot_control.py

The `bot_control.py` script provides a more versatile command-line interface for controlling the trading bots. It supports starting all bots, stopping all bots, and stopping specific bots by terminal ID.

```python
"""
Command-line interface for controlling trading bots.
"""
import sys
import logging
import argparse
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/bot_control.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import unified configuration manager
from config.unified_config import config_manager

# Import necessary managers
from utils.enhanced_error_handler import EnhancedErrorHandler
error_handler = EnhancedErrorHandler()

from utils.enhanced_memory_manager import enhanced_memory_manager
memory_manager = enhanced_memory_manager

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32,
    thread_name_prefix="Main"
)

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
mt5_manager = MT5ConnectionManager(config_manager)

# Import TradingBotManager
from main import TradingBotManager

def create_manager():
    """Create and return a TradingBotManager instance."""
    return TradingBotManager(
        config_manager=config_manager,
        error_handler=error_handler,
        mt5_manager=mt5_manager,
        thread_manager=thread_manager,
        memory_manager=memory_manager
    )

def start_all_bots():
    """Start all trading bots."""
    logger.info("Initializing bot manager to start all bots...")

    # Create a manager instance
    manager = create_manager()

    # Start all bots
    logger.info("Starting all trading bots...")
    success = manager.start_all_bots()

    if success:
        logger.info("All trading bots started successfully.")
    else:
        logger.error("Failed to start all trading bots.")
        manager.shutdown()

    return success

def stop_all_bots():
    """Stop all running trading bots."""
    logger.info("Initializing bot manager to stop all bots...")

    # Create a manager instance
    manager = create_manager()

    # Stop all bots
    logger.info("Stopping all trading bots...")
    success = manager.stop_all_bots()

    if success:
        logger.info("All trading bots stopped successfully.")
    else:
        logger.error("Failed to stop all trading bots.")

    # Perform full shutdown
    logger.info("Performing full shutdown...")
    manager.shutdown()

    return success

def stop_specific_bot(terminal_id):
    """Stop a specific trading bot."""
    logger.info(f"Initializing bot manager to stop bot for terminal {terminal_id}...")

    # Create a manager instance
    manager = create_manager()

    # Check if the bot exists
    if terminal_id not in manager.bots:
        logger.error(f"No bot found for terminal {terminal_id}")
        return False

    # Stop the specific bot
    logger.info(f"Stopping bot for terminal {terminal_id}...")
    try:
        manager.bots[terminal_id].stop()
        logger.info(f"Bot for terminal {terminal_id} stopped successfully.")
        return True
    except Exception as e:
        logger.error(f"Failed to stop bot for terminal {terminal_id}: {e}")
        return False
```

## Starting the Trading Bot

There are several ways to start the trading bot:

### Method 1: Using main.py

The simplest way to start the trading bot is to run the main.py script:

```bash
python main.py
```

This will start all trading bots configured in the system.

### Method 2: Using bot_control.py

You can also use the bot_control.py script to start all bots:

```bash
python bot_control.py start
```

This will initialize all necessary managers, create a TradingBotManager instance, and call the start_all_bots() method to start all bots.

## Stopping the Trading Bot

There are several ways to stop the trading bot:

### Method 1: Using Keyboard Interrupt (Ctrl+C)

The simplest way to stop the bot is to press `Ctrl+C` in the terminal where the bot is running. The application has signal handlers set up to catch this interrupt and perform a clean shutdown.

### Method 2: Using stop_bots.py

You can use the stop_bots.py script to stop all running bots:

```bash
python stop_bots.py
```

This will initialize all necessary managers, create a TradingBotManager instance, and call the stop_all_bots() method to stop all running bots.

### Method 3: Using bot_control.py

You can also use the bot_control.py script to stop all bots:

```bash
python bot_control.py stop
```

This will initialize all necessary managers, create a TradingBotManager instance, and call the stop_all_bots() method to stop all running bots.

## Managing Individual Bots

You can use the bot_control.py script to stop specific bots by terminal ID:

```bash
python bot_control.py stop --terminal 1
```

This will stop only the bot running on terminal 1, leaving other bots running.

## Troubleshooting

### Common Issues

1. **Bot Not Stopping**:
   - Check if the bot is still running using the process manager
   - Try stopping the bot again with the stop_bots.py script
   - If all else fails, manually kill the process

2. **Bot Not Starting**:
   - Check the logs for error messages
   - Ensure that the MT5 terminals are configured correctly
   - Verify that the models are trained and saved correctly

3. **Permission Issues**:
   - Ensure that you have the necessary permissions to run the scripts
   - Try running the scripts with administrator privileges if needed

### Getting Help

If you encounter issues not covered in this guide:

1. Check the logs in the `logs/` directory
2. Review the error messages in the console
3. Open an issue on the GitHub repository
4. Contact the maintainers for support

---

This guide covers the essential aspects of controlling the trading bot system. For more information on data collection, model training, and trading execution, refer to the other guides in this directory.
