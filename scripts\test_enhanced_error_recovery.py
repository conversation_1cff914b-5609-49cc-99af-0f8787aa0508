"""
Test script for the enhanced error recovery strategies.
"""
import os
import sys
import logging
import time
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_enhanced_error_recovery():
    """Test the enhanced error recovery strategies."""
    try:
        from utils.enhanced_error_handler import (
            <PERSON>hancedError<PERSON>and<PERSON>,
            Error<PERSON>ate<PERSON>y,
            <PERSON>rror<PERSON>ever<PERSON>,
            ErrorInfo
        )

        logger.info("Testing enhanced error recovery strategies")

        # Get the singleton instance
        error_handler = EnhancedErrorHandler()

        # Test 1: MT5 API error recovery
        logger.info("Test 1: MT5 API error recovery")

        # Create a mock MT5 error
        class MT5Error(Exception):
            pass

        mt5_error = MT5Error("Failed to connect to MT5 terminal")

        # Create context with terminal_id
        context = {
            'terminal_id': '1',
            'retry_count': 0,
            'max_retries': 2
        }

        # Handle the error
        error_info = error_handler.handle_error(
            mt5_error,
            context=context,
            category=ErrorCategory.MT5_API
        )

        logger.info(f"MT5 error handled: {error_info.error_type} - {error_info.error_message}")
        logger.info(f"Recovery attempted: {error_info.recovery_attempted}")

        # Test 2: Timeout error recovery
        logger.info("\nTest 2: Timeout error recovery")

        # Create a mock timeout error
        timeout_error = TimeoutError("Connection timed out after 30 seconds")

        # Create context
        context = {
            'terminal_id': '2',
            'retry_count': 0,
            'max_retries': 2
        }

        # Handle the error
        error_info = error_handler.handle_error(
            timeout_error,
            context=context,
            category=ErrorCategory.TIMEOUT
        )

        logger.info(f"Timeout error handled: {error_info.error_type} - {error_info.error_message}")
        logger.info(f"Recovery attempted: {error_info.recovery_attempted}")

        # Test 3: Network error recovery
        logger.info("\nTest 3: Network error recovery")

        # Create a mock network error
        network_error = ConnectionError("Failed to establish connection to server")

        # Create context
        context = {
            'retry_count': 0,
            'max_retries': 2
        }

        # Handle the error
        error_info = error_handler.handle_error(
            network_error,
            context=context,
            category=ErrorCategory.NETWORK
        )

        logger.info(f"Network error handled: {error_info.error_type} - {error_info.error_message}")
        logger.info(f"Recovery attempted: {error_info.recovery_attempted}")

        # Test 4: Execute with retry
        logger.info("\nTest 4: Execute with retry")

        # Function that fails on first attempts but succeeds later
        attempt_counter = [0]
        def eventually_succeeds():
            attempt_counter[0] += 1
            if attempt_counter[0] <= 2:
                logger.info(f"Attempt {attempt_counter[0]} failing")
                raise TimeoutError(f"Timeout on attempt {attempt_counter[0]}")
            logger.info(f"Attempt {attempt_counter[0]} succeeding")
            return f"Success on attempt {attempt_counter[0]}"

        # Execute with retry manually
        max_retries = 3
        retry_count = 0
        result = None

        while retry_count <= max_retries:
            try:
                result = eventually_succeeds()
                logger.info(f"Execute with retry result: {result}")
                break
            except Exception as e:
                retry_count += 1
                error_info = error_handler.handle_error(
                    e,
                    context={'retry_count': retry_count, 'max_retries': max_retries},
                    category=ErrorCategory.TIMEOUT
                )
                if retry_count > max_retries:
                    logger.error(f"Execute with retry failed after {max_retries} attempts")
                    break

        # Test 5: Error statistics
        logger.info("\nTest 5: Error statistics")

        # Generate some errors
        for i in range(5):
            try:
                if i % 2 == 0:
                    raise ValueError(f"Value error {i}")
                else:
                    raise KeyError(f"Key error {i}")
            except Exception as e:
                error_handler.handle_error(e, category=ErrorCategory.DATA)

        # Get error statistics
        # The actual API might be different, so we'll just log that we generated errors
        logger.info(f"Generated 5 test errors for statistics")

        # Try to get recent errors if the method exists
        try:
            if hasattr(error_handler, 'get_recent_errors'):
                recent_errors = error_handler.get_recent_errors(5)
                logger.info(f"Recent errors: {len(recent_errors)}")
            elif hasattr(error_handler, 'get_errors'):
                recent_errors = error_handler.get_errors()
                logger.info(f"Recent errors: {len(recent_errors)}")
        except Exception as e:
            logger.warning(f"Could not get recent errors: {str(e)}")

        logger.info("Enhanced error recovery test passed")
        return True

    except Exception as e:
        logger.error(f"Error testing enhanced error recovery: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_enhanced_error_recovery():
        logger.info("Enhanced error recovery test passed")
        sys.exit(0)
    else:
        logger.error("Enhanced error recovery test failed")
        sys.exit(1)
