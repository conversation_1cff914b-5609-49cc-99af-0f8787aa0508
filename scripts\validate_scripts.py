"""
Script validation utility to protect critical scripts from accidental modifications.
"""

import hashlib
import os
import sys
import logging
from typing import Dict, Tuple
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('script_validation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScriptValidator:
    """Class for validating script integrity."""
    
    def __init__(self, hash_file: str = "docs/script_hashes.txt"):
        """Initialize the validator."""
        self.hash_file = hash_file
        self.protected_scripts = {
            "collect_training_data.py": {
                "path": "collect_training_data.py",
                "description": "Data collection script for MT5 terminals",
                "version": "1.0.0",
                "is_sensitive": False
            },
            "config/credentials.py": {
                "path": "config/credentials.py",
                "description": "MT5 credentials and terminal configurations",
                "version": "1.0.0",
                "is_sensitive": True,
                "template": "config/credentials_template.py"
            },
            "config/credentials_template.py": {
                "path": "config/credentials_template.py",
                "description": "Template for MT5 credentials configuration",
                "version": "1.0.0",
                "is_sensitive": False
            }
        }
        
    def calculate_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of a file."""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.sha256(content).hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {str(e)}")
            return ""
            
    def load_stored_hashes(self) -> Dict[str, str]:
        """Load stored hashes from file."""
        if not os.path.exists(self.hash_file):
            return {}
            
        try:
            with open(self.hash_file, 'r') as f:
                return dict(line.strip().split(':') for line in f)
        except Exception as e:
            logger.error(f"Error loading hashes: {str(e)}")
            return {}
            
    def save_hashes(self, hashes: Dict[str, str]) -> bool:
        """Save hashes to file."""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.hash_file), exist_ok=True)
            
            with open(self.hash_file, 'w') as f:
                for script, hash_value in hashes.items():
                    f.write(f"{script}:{hash_value}\n")
            return True
        except Exception as e:
            logger.error(f"Error saving hashes: {str(e)}")
            return False
            
    def validate_script(self, script_name: str) -> Tuple[bool, str]:
        """Validate a specific script."""
        if script_name not in self.protected_scripts:
            return False, f"Script {script_name} is not in protected scripts list"
            
        script_info = self.protected_scripts[script_name]
        script_path = script_info["path"]
        
        # Special handling for sensitive files
        if script_info.get("is_sensitive", False):
            # Check if file exists
            if not os.path.exists(script_path):
                # If template exists, create from template
                template_path = script_info.get("template")
                if template_path and os.path.exists(template_path):
                    try:
                        import shutil
                        shutil.copy(template_path, script_path)
                        logger.info(f"Created {script_path} from template {template_path}")
                    except Exception as e:
                        return False, f"Failed to create {script_path} from template: {str(e)}"
                else:
                    return False, f"Sensitive file {script_path} not found and no template available"
            
            # Add to .gitignore if not already present
            try:
                with open('.gitignore', 'a+') as f:
                    f.seek(0)
                    content = f.read()
                    if script_path not in content:
                        f.write(f'\n# Sensitive file\n{script_path}\n')
            except Exception as e:
                logger.warning(f"Could not update .gitignore for {script_path}: {str(e)}")
        
        if not os.path.exists(script_path):
            return False, f"Script file not found: {script_path}"
            
        current_hash = self.calculate_hash(script_path)
        if not current_hash:
            return False, f"Failed to calculate hash for {script_path}"
            
        stored_hashes = self.load_stored_hashes()
        
        # If no stored hash exists, store the current one
        if script_name not in stored_hashes:
            stored_hashes[script_name] = current_hash
            if not self.save_hashes(stored_hashes):
                return False, "Failed to save initial hash"
            logger.info(f"Initial hash created for {script_name}")
            return True, "Initial hash created"
            
        # Compare hashes
        if stored_hashes[script_name] != current_hash:
            if script_info.get("is_sensitive", False):
                logger.warning(f"Warning: Sensitive file {script_name} has been modified")
                return True, "Sensitive file modified (this is expected)"
            return False, f"Script {script_name} has been modified"
            
        return True, "Script validation successful"
        
    def validate_all(self) -> Dict[str, Tuple[bool, str]]:
        """Validate all protected scripts."""
        results = {}
        for script_name in self.protected_scripts:
            results[script_name] = self.validate_script(script_name)
        return results
        
    def update_hash(self, script_name: str) -> Tuple[bool, str]:
        """Update stored hash for a script after approved changes."""
        if script_name not in self.protected_scripts:
            return False, f"Script {script_name} is not in protected scripts list"
            
        script_info = self.protected_scripts[script_name]
        script_path = script_info["path"]
        
        if not os.path.exists(script_path):
            return False, f"Script file not found: {script_path}"
            
        current_hash = self.calculate_hash(script_path)
        if not current_hash:
            return False, f"Failed to calculate hash for {script_path}"
            
        stored_hashes = self.load_stored_hashes()
        stored_hashes[script_name] = current_hash
        
        if not self.save_hashes(stored_hashes):
            return False, "Failed to update hash"
            
        logger.info(f"Hash updated for {script_name}")
        return True, "Hash updated successfully"
        
    def create_template(self, script_name: str) -> Tuple[bool, str]:
        """Create or update template for a sensitive file."""
        if script_name not in self.protected_scripts:
            return False, f"Script {script_name} is not in protected scripts list"
            
        script_info = self.protected_scripts[script_name]
        if not script_info.get("is_sensitive", False):
            return False, f"Script {script_name} is not marked as sensitive"
            
        script_path = script_info["path"]
        template_path = script_info.get("template")
        
        if not template_path:
            return False, f"No template path specified for {script_name}"
            
        if not os.path.exists(script_path):
            return False, f"Source file {script_path} not found"
            
        try:
            # Read the source file
            with open(script_path, 'r') as f:
                content = f.read()
                
            # Create template by removing sensitive information
            template_content = content.replace(
                '"""MT5 credentials and terminal configurations.',
                '"""Template for MT5 credentials and terminal configurations.\nDO NOT ADD REAL CREDENTIALS TO THIS FILE.'
            )
            
            # Remove actual credentials
            import re
            template_content = re.sub(
                r'("login":\s*)"[^"]*"',
                r'\1""',
                template_content
            )
            template_content = re.sub(
                r'("password":\s*)"[^"]*"',
                r'\1""',
                template_content
            )
            template_content = re.sub(
                r'("bot_token":\s*)"[^"]*"',
                r'\1""',
                template_content
            )
            template_content = re.sub(
                r'("chat_id":\s*)"[^"]*"',
                r'\1""',
                template_content
            )
            
            # Save template
            with open(template_path, 'w') as f:
                f.write(template_content)
                
            logger.info(f"Created/updated template at {template_path}")
            return True, "Template created/updated successfully"
            
        except Exception as e:
            return False, f"Failed to create template: {str(e)}"

def main():
    """Main function to run script validation."""
    validator = ScriptValidator()
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--update":
            if len(sys.argv) != 3:
                print("Usage: python validate_scripts.py --update <script_name>")
                return
            script_name = sys.argv[2]
            success, message = validator.update_hash(script_name)
            print(f"Hash update for {script_name}: {message}")
            return
            
        elif sys.argv[1] == "--create-template":
            if len(sys.argv) != 3:
                print("Usage: python validate_scripts.py --create-template <script_name>")
                return
            script_name = sys.argv[2]
            success, message = validator.create_template(script_name)
            print(f"Template creation for {script_name}: {message}")
            return
            
    # Validate all scripts
    results = validator.validate_all()
    
    # Print results
    print("\nScript Validation Results:")
    print("-" * 50)
    for script_name, (success, message) in results.items():
        status = "✓" if success else "✗"
        print(f"{status} {script_name}: {message}")
    print("-" * 50)
    
    # Exit with appropriate status code
    sys.exit(0 if all(success for success, _ in results.values()) else 1)

if __name__ == "__main__":
    main() 