@echo off
echo Pushing Enhanced Trading Bot to GitHub
echo =====================================
echo.

echo Step 1: Configuring Git
git config --global user.name "pro7gt"
git config --global user.email "<EMAIL>"
echo.

echo Step 2: Creating GitHub repository (if not already created)
echo Please create a repository named "enhanced-trading-bot" on GitHub if you haven't already.
echo Visit: https://github.com/new
echo.
pause

echo Step 3: Adding remote repository
git remote add origin https://github.com/pro7gt/enhanced-trading-bot.git
echo.

echo Step 4: Pushing to GitHub
echo You may be prompted to authenticate with GitHub.
git push -u origin master
echo.

echo Step 5: Verifying push
echo Please check your repository at: https://github.com/pro7gt/enhanced-trading-bot
echo.

pause
