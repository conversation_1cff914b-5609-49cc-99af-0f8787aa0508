"""
Test script to verify terminal ID consistency across the codebase.
"""
import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_terminal_id_consistency():
    """Test terminal ID consistency across the codebase."""
    try:
        # Import necessary modules
        from config.unified_config import UnifiedConfigManager
        from utils.mt5.mt5_connection_manager import MT5ConnectionManager

        # Initialize configuration manager
        config_manager = UnifiedConfigManager()

        # Get MT5 configuration
        mt5_config = config_manager.get_mt5_config()

        # Print terminal configurations
        logger.info(f"MT5 Terminals: {len(mt5_config.terminals)}")
        for terminal_id, terminal_config in mt5_config.terminals.items():
            logger.info(f"Terminal {terminal_id} (type: {type(terminal_id).__name__}): {terminal_config.server}")

            # Verify terminal_id is a string
            assert isinstance(terminal_id, str), f"Terminal ID {terminal_id} is not a string"

        # Initialize MT5 connection manager
        mt5_manager = MT5ConnectionManager(config_manager)

        # Test getting connection with different terminal ID types
        for terminal_id in list(mt5_config.terminals.keys())[:1]:  # Test with first terminal only
            # Test with string terminal ID
            logger.info(f"Testing connection with string terminal ID: {terminal_id}")
            conn_str = mt5_manager.get_connection(terminal_id)
            if conn_str:
                logger.info(f"Connection successful with string terminal ID: {conn_str.terminal_id} (type: {type(conn_str.terminal_id).__name__})")
                assert isinstance(conn_str.terminal_id, str), f"Connection terminal ID {conn_str.terminal_id} is not a string"
            else:
                logger.warning(f"Connection failed with string terminal ID: {terminal_id}")

            # Test with integer terminal ID (should be converted to string)
            if terminal_id.isdigit():
                int_terminal_id = int(terminal_id)
                logger.info(f"Testing connection with integer terminal ID: {int_terminal_id}")
                conn_int = mt5_manager.get_connection(int_terminal_id)
                if conn_int:
                    logger.info(f"Connection successful with integer terminal ID: {conn_int.terminal_id} (type: {type(conn_int.terminal_id).__name__})")
                    assert isinstance(conn_int.terminal_id, str), f"Connection terminal ID {conn_int.terminal_id} is not a string"
                else:
                    logger.warning(f"Connection failed with integer terminal ID: {int_terminal_id}")

        # Shutdown all connections
        logger.info("Shutting down all connections")
        mt5_manager.shutdown_all()

        logger.info("Terminal ID consistency test passed")
        return True

    except Exception as e:
        logger.error(f"Error testing terminal ID consistency: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_terminal_id_consistency():
        logger.info("Terminal ID consistency test passed")
        sys.exit(0)
    else:
        logger.error("Terminal ID consistency test failed")
        sys.exit(1)
