#!/usr/bin/env python
'''
Model Replication Script

This script replicates the successful model configurations.
Generated automatically from validate_successful_configs.py
'''

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command):
    '''Run a command and log the output.'''
    logger.info(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✓ Command completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
        else:
            logger.error(f"✗ Command failed with return code {result.returncode}")
            if result.stderr:
                logger.error(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        logger.error(f"Exception running command: {e}")
        return False

def main():
    '''Main replication function.'''
    logger.info("Starting model replication...")

    # Training commands from successful configurations
    # LSTM Model
    if not run_command("python train_lstm_btcusd.py"):
        logger.error("Failed to train lstm model")
        return False

    # ARIMA Model
    if not run_command("python train_arima_single.py --timeframe M5 --auto-arima --data-selection recent --max-rows 10000"):
        logger.error("Failed to train arima model")
        return False

    # TFT Model
    if not run_command("python train_tft_pytorch.py --timeframe M5 --hidden-dim 64 --num-heads 4 --num-layers 2 --dropout-rate 0.1 --learning-rate 0.001 --epochs 5 --batch-size 32"):
        logger.error("Failed to train tft model")
        return False


    logger.info("All models trained successfully!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
