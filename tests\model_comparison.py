"""
Enhanced model testing and comparison framework.
"""
import sys
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any
import numpy as np
import pandas as pd
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import optuna
from optuna.visualization import plot_optimization_history, plot_param_importances
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import GPUtil
import psutil
import json

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.unified_config import get_all_model_configs
model_config = get_all_model_configs()

class ModelComparison:
    """Framework for comparing multiple models and enhanced testing."""

    def __init__(self, models: List[str], data_path: Path):
        """
        Initialize model comparison framework.

        Args:
            models: List of model names to compare
            data_path: Path to the data file
        """
        self.models = models
        self.data_path = data_path
        self.results = {}
        self.best_params = {}

        # Set up logging
        self.logger = logging.getLogger('model_comparison')
        self.logger.setLevel(logging.INFO)

        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = Path("test_results") / "comparison" / timestamp
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Add file handler
        log_file = self.output_dir / "comparison.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(file_handler)

        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(console_handler)

    def optimize_hyperparameters(self, model_name: str, X_train: np.ndarray, y_train: np.ndarray,
                               X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """
        Enhanced hyperparameter optimization using Optuna.

        Args:
            model_name: Name of the model
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets

        Returns:
            Dictionary of optimized hyperparameters
        """
        def objective(trial):
            # Define hyperparameter space based on model type
            if model_name == 'lstm':
                params = {
                    'hidden_size': trial.suggest_int('hidden_size', 32, 256),
                    'num_layers': trial.suggest_int('num_layers', 1, 3),
                    'dropout': trial.suggest_float('dropout', 0.1, 0.5),
                    'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2),
                    'batch_size': trial.suggest_int('batch_size', 32, 256),
                }
            elif model_name == 'arima':
                params = {
                    'p': trial.suggest_int('p', 1, 5),
                    'd': trial.suggest_int('d', 0, 2),
                    'q': trial.suggest_int('q', 1, 5),
                    'seasonal_p': trial.suggest_int('seasonal_p', 0, 2),
                    'seasonal_d': trial.suggest_int('seasonal_d', 0, 1),
                    'seasonal_q': trial.suggest_int('seasonal_q', 0, 2),
                    'seasonal_m': trial.suggest_int('seasonal_m', 0, 12),
                }
            else:
                raise ValueError(f"Unsupported model type: {model_name}")

            # Initialize model with trial parameters
            model = self._get_model_instance(model_name, params)

            # Train model
            train_metrics = model.train(X_train, y_train, X_val, y_val)

            # Return validation loss
            return train_metrics['val_loss']

        # Create study
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=50)

        # Plot optimization history
        fig = plot_optimization_history(study)
        fig.write_image(str(self.output_dir / f"{model_name}_optimization_history.png"))

        # Plot parameter importance
        fig = plot_param_importances(study)
        fig.write_image(str(self.output_dir / f"{model_name}_param_importance.png"))

        return study.best_params

    def _get_model_instance(self, model_name: str, params: Dict[str, Any]):
        """Get model instance with given parameters."""
        if model_name == 'lstm':
            from models.pytorch_lstm_model import LSTMModel
            model = LSTMModel(model_config)
        elif model_name == 'tft':
            from models.tft_model import TFTModel
            model = TFTModel(model_config)
        elif model_name == 'arima':
            from utils.arima_trainer import ARIMAModel
            model = ARIMAModel(model_config)
        else:
            raise ValueError(f"Unsupported model type: {model_name}")

        model.build()
        model.set_model_params(params)
        return model

    def run_comparison(self):
        """Run comprehensive model comparison."""
        try:
            # Load and preprocess data
            self.logger.info("Loading and preprocessing data...")
            data = pd.read_csv(self.data_path)
            X, y = self._preprocess_data(data)

            # Initialize cross-validation
            tscv = TimeSeriesSplit(n_splits=5)

            # Store results for each model
            for model_name in self.models:
                self.logger.info(f"\nProcessing model: {model_name}")
                model_results = []

                for fold, (train_idx, test_idx) in enumerate(tscv.split(X)):
                    self.logger.info(f"Processing fold {fold + 1}/5")

                    # Split data
                    X_train, X_test = X[train_idx], X[test_idx]
                    y_train, y_test = y[train_idx], y[test_idx]

                    # Further split for validation
                    val_size = int(len(X_train) * 0.2)
                    X_train, X_val = X_train[:-val_size], X_train[-val_size:]
                    y_train, y_val = y_train[:-val_size], y_train[-val_size:]

                    # Optimize hyperparameters
                    self.logger.info("Optimizing hyperparameters...")
                    best_params = self.optimize_hyperparameters(model_name, X_train, y_train, X_val, y_val)
                    self.best_params[model_name] = best_params

                    # Initialize and train model
                    model = self._get_model_instance(model_name, best_params)

                    # Monitor resources
                    self._monitor_resources()

                    # Train model
                    train_metrics = model.train(X_train, y_train, X_val, y_val)

                    # Make predictions
                    y_pred = model.predict(X_test)

                    # Calculate metrics
                    metrics = self._calculate_metrics(y_test, y_pred)
                    metrics.update(train_metrics)
                    model_results.append(metrics)

                    # Plot results
                    self._plot_results(y_test, y_pred, model_name, fold)

                # Store average metrics
                self.results[model_name] = pd.DataFrame(model_results).mean().to_dict()

            # Generate comparison report
            self._generate_comparison_report()

            self.logger.info("Model comparison completed successfully")

        except Exception as e:
            self.logger.error(f"Error in model comparison: {str(e)}")
            raise

    def _preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Preprocess data for model training."""
        # Basic preprocessing for demonstration purposes
        # In a real implementation, this should match your existing preprocessing pipeline

        # Ensure data is sorted by time
        if 'time' in data.columns:
            data = data.sort_values('time')

        # Use standard OHLCV features
        feature_cols = ['open', 'high', 'low', 'close', 'volume']
        features = data[feature_cols].values

        # Target is next period's return
        data['target'] = data['close'].pct_change(1).shift(-1)
        target = data['target'].dropna().values

        # Align features with target
        features = features[:len(target)]

        return features, target

    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate evaluation metrics."""
        metrics = {
            'mae': mean_absolute_error(y_true, y_pred),
            'mse': mean_squared_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
            'r2': r2_score(y_true, y_pred)
        }
        return metrics

    def _monitor_resources(self):
        """Monitor system resources during training."""
        try:
            # Monitor GPU usage
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                self.logger.info(f"GPU {gpu.id}: {gpu.load*100:.1f}% load, {gpu.memoryUtil*100:.1f}% memory used")

            # Monitor memory usage
            memory = psutil.virtual_memory()
            self.logger.info(f"Memory: {memory.percent}% used")

        except Exception as e:
            self.logger.warning(f"Error monitoring resources: {str(e)}")

    def _plot_results(self, y_true: np.ndarray, y_pred: np.ndarray, model_name: str, fold: int):
        """Plot model results."""
        try:
            # Create interactive plot
            fig = make_subplots(rows=2, cols=1)

            # Plot actual vs predicted
            fig.add_trace(
                go.Scatter(y=y_true, name='Actual', line=dict(color='blue')),
                row=1, col=1
            )
            fig.add_trace(
                go.Scatter(y=y_pred, name='Predicted', line=dict(color='red')),
                row=1, col=1
            )

            # Plot residuals
            residuals = y_true - y_pred
            fig.add_trace(
                go.Scatter(y=residuals, name='Residuals', line=dict(color='green')),
                row=2, col=1
            )

            fig.update_layout(
                title=f"{model_name} - Fold {fold + 1}",
                height=800
            )

            # Save plot
            fig.write_html(str(self.output_dir / f"{model_name}_fold_{fold}_results.html"))

        except Exception as e:
            self.logger.warning(f"Error plotting results: {str(e)}")

    def _generate_comparison_report(self):
        """Generate comprehensive comparison report."""
        try:
            # Create comparison table
            comparison_df = pd.DataFrame(self.results).T

            # Save to CSV
            comparison_df.to_csv(self.output_dir / "model_comparison.csv")

            # Create interactive comparison plot
            fig = go.Figure()

            for metric in comparison_df.columns:
                fig.add_trace(
                    go.Bar(
                        name=metric,
                        x=comparison_df.index,
                        y=comparison_df[metric],
                        text=comparison_df[metric].round(4),
                        textposition='auto',
                    )
                )

            fig.update_layout(
                title="Model Comparison",
                barmode='group',
                height=600
            )

            # Save plot
            fig.write_html(str(self.output_dir / "model_comparison.html"))

            # Save best parameters
            with open(self.output_dir / "best_parameters.json", 'w') as f:
                json.dump(self.best_params, f, indent=4)

        except Exception as e:
            self.logger.warning(f"Error generating comparison report: {str(e)}")