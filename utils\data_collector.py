"""
Unified Data Collector Module

This module provides a comprehensive solution for collecting historical and real-time
data from MT5 terminals with advanced features including:
- Memory-efficient data collection
- Technical indicator calculation
- Data validation and cleaning
- Caching and data management
- Error handling and recovery
"""

import os
import logging
import pandas as pd
import MetaTrader5 as mt5
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from pathlib import Path

# Import utility modules
from utils.mt5.mt5_connection_manager import MT5ConnectionManager
from utils.error_handler import ErrorHandler
from utils.memory_manager import MemoryManager

# Configure logging
logger = logging.getLogger(__name__)

class DataCollector:
    """
    Unified data collector for MT5 terminals with advanced features.

    This class combines the best features from multiple implementations:
    - Memory-efficient data collection with chunking
    - Technical indicator calculation
    - Data validation and cleaning
    - Caching and data management
    - Error handling and recovery
    """

    def __init__(
        self,
        mt5_manager: Optional[MT5ConnectionManager] = None,
        error_handler: Optional[ErrorHandler] = None,
        memory_manager: Optional[MemoryManager] = None,
        config: Optional[Dict[str, Any]] = None,
        cache_dir: str = "data/cache"
    ):
        """
        Initialize the DataCollector with advanced features.

        Args:
            mt5_manager: MT5 connection manager
            error_handler: Error handler for handling collection errors
            memory_manager: Memory manager for monitoring memory usage
            config: Configuration dictionary
            cache_dir: Directory for caching data
        """
        self.mt5_manager = mt5_manager
        self.error_handler = error_handler
        self.memory_manager = memory_manager
        self.config = config or {}
        self.cache_dir = cache_dir

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Initialize timeframe mapping
        self.timeframe_map = {
            'M1': mt5.TIMEFRAME_M1,
            'M5': mt5.TIMEFRAME_M5,
            'M15': mt5.TIMEFRAME_M15,
            'M30': mt5.TIMEFRAME_M30,
            'H1': mt5.TIMEFRAME_H1,
            'H4': mt5.TIMEFRAME_H4,
            'D1': mt5.TIMEFRAME_D1,
            'W1': mt5.TIMEFRAME_W1,
            'MN1': mt5.TIMEFRAME_MN1
        }

        # Cache for collected data
        self.data_cache = {}
        self.last_data_times = {}

        # Configuration values with defaults
        self.max_retries = config.get('max_retries', 3) if config else 3
        self.retry_delay = config.get('retry_delay', 5) if config else 5
        self.update_interval = config.get('update_interval', 60) if config else 60  # seconds

        logger.info("DataCollector initialized")

    def collect_data(
        self,
        symbol: str,
        timeframe: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        terminal_id: Optional[int] = None,
        save_to_file: bool = True,
        file_format: str = 'csv'
    ) -> Optional[pd.DataFrame]:
        """
        Collect historical data for a symbol and timeframe.

        Args:
            symbol: Trading symbol (e.g., 'EURUSD')
            timeframe: Timeframe (e.g., 'M5', 'H1')
            start_date: Start date for data collection
            end_date: End date for data collection
            terminal_id: MT5 terminal ID to use
            save_to_file: Whether to save the data to a file
            file_format: File format to save the data ('csv' or 'parquet')

        Returns:
            DataFrame with historical data or None if collection failed
        """
        try:
            # Set default dates if not provided
            if end_date is None:
                end_date = datetime.now()

            if start_date is None:
                # Default to 1 month of data
                start_date = end_date - timedelta(days=30)

            # Initialize MT5 if needed
            if not mt5.initialize():
                logger.warning("MT5 not initialized, attempting to initialize")
                if not mt5.initialize(portable=True):
                    logger.error(f"Failed to initialize MT5: {mt5.last_error()}")
                    return None

            # Use terminal_id if provided
            if terminal_id is not None:
                logger.info(f"Using terminal ID: {terminal_id}")

            # Get timeframe constant
            mt5_timeframe = self.timeframe_map.get(timeframe)
            if mt5_timeframe is None:
                logger.error(f"Invalid timeframe: {timeframe}")
                return None

            # Collect historical data
            logger.info(f"Collecting {symbol} {timeframe} data from {start_date} to {end_date}")
            rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)

            if rates is None or len(rates) == 0:
                logger.error(f"Failed to collect {symbol} {timeframe} data")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(rates)

            # Convert time column to datetime
            df['time'] = pd.to_datetime(df['time'], unit='s')

            # Save to file if requested
            if save_to_file:
                file_path = Path(self.cache_dir) / f"{symbol}_{timeframe}.{file_format}"
                try:
                    if file_format.lower() == 'csv':
                        df.to_csv(file_path, index=False)
                    elif file_format.lower() == 'parquet':
                        df.to_parquet(file_path, index=False)
                    else:
                        logger.error(f"Unsupported file format: {file_format}")
                    logger.info(f"Saved data to {file_path}")
                except Exception as e:
                    logger.error(f"Error saving data to {file_path}: {str(e)}")

            return df

        except Exception as e:
            logger.error(f"Error collecting data: {str(e)}")
            return None

    def collect_multi_timeframe_data(
        self,
        symbol: str,
        timeframes: List[str],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        terminal_id: Optional[int] = None,
        save_to_file: bool = True,
        file_format: str = 'csv'
    ) -> Dict[str, pd.DataFrame]:
        """
        Collect historical data for a symbol across multiple timeframes.

        Args:
            symbol: Trading symbol (e.g., 'EURUSD')
            timeframes: List of timeframes (e.g., ['M5', 'H1'])
            start_date: Start date for data collection
            end_date: End date for data collection
            terminal_id: MT5 terminal ID to use
            save_to_file: Whether to save the data to files
            file_format: File format to save the data ('csv' or 'parquet')

        Returns:
            Dictionary mapping timeframes to DataFrames with historical data
        """
        result = {}

        for tf in timeframes:
            df = self.collect_data(
                symbol=symbol,
                timeframe=tf,
                start_date=start_date,
                end_date=end_date,
                terminal_id=terminal_id,
                save_to_file=save_to_file,
                file_format=file_format
            )

            if df is not None:
                result[tf] = df

        return result

    def collect_multi_symbol_data(
        self,
        symbols: List[str],
        timeframe: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        terminal_id: Optional[int] = None,
        save_to_file: bool = True,
        file_format: str = 'csv'
    ) -> Dict[str, pd.DataFrame]:
        """
        Collect historical data for multiple symbols on a single timeframe.

        Args:
            symbols: List of trading symbols (e.g., ['EURUSD', 'GBPUSD'])
            timeframe: Timeframe (e.g., 'M5', 'H1')
            start_date: Start date for data collection
            end_date: End date for data collection
            terminal_id: MT5 terminal ID to use
            save_to_file: Whether to save the data to files
            file_format: File format to save the data ('csv' or 'parquet')

        Returns:
            Dictionary mapping symbols to DataFrames with historical data
        """
        result = {}

        for symbol in symbols:
            df = self.collect_data(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                terminal_id=terminal_id,
                save_to_file=save_to_file,
                file_format=file_format
            )

            if df is not None:
                result[symbol] = df

        return result

    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to the DataFrame.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            DataFrame with added technical indicators
        """
        try:
            # Make a copy to avoid modifying the original
            df_with_indicators = df.copy()

            # Simple Moving Averages
            df_with_indicators['sma_5'] = df_with_indicators['close'].rolling(window=5).mean()
            df_with_indicators['sma_10'] = df_with_indicators['close'].rolling(window=10).mean()
            df_with_indicators['sma_20'] = df_with_indicators['close'].rolling(window=20).mean()
            df_with_indicators['sma_50'] = df_with_indicators['close'].rolling(window=50).mean()
            df_with_indicators['sma_100'] = df_with_indicators['close'].rolling(window=100).mean()

            # Exponential Moving Averages
            df_with_indicators['ema_5'] = df_with_indicators['close'].ewm(span=5, adjust=False).mean()
            df_with_indicators['ema_10'] = df_with_indicators['close'].ewm(span=10, adjust=False).mean()
            df_with_indicators['ema_20'] = df_with_indicators['close'].ewm(span=20, adjust=False).mean()
            df_with_indicators['ema_50'] = df_with_indicators['close'].ewm(span=50, adjust=False).mean()
            df_with_indicators['ema_100'] = df_with_indicators['close'].ewm(span=100, adjust=False).mean()

            # Bollinger Bands (20, 2)
            df_with_indicators['bb_middle'] = df_with_indicators['close'].rolling(window=20).mean()
            df_with_indicators['bb_std'] = df_with_indicators['close'].rolling(window=20).std()
            df_with_indicators['bb_upper'] = df_with_indicators['bb_middle'] + 2 * df_with_indicators['bb_std']
            df_with_indicators['bb_lower'] = df_with_indicators['bb_middle'] - 2 * df_with_indicators['bb_std']

            # RSI (14)
            delta = df_with_indicators['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            df_with_indicators['rsi_14'] = 100 - (100 / (1 + rs))

            # MACD
            ema_12 = df_with_indicators['close'].ewm(span=12, adjust=False).mean()
            ema_26 = df_with_indicators['close'].ewm(span=26, adjust=False).mean()
            df_with_indicators['macd'] = ema_12 - ema_26
            df_with_indicators['macd_signal'] = df_with_indicators['macd'].ewm(span=9, adjust=False).mean()
            df_with_indicators['macd_hist'] = df_with_indicators['macd'] - df_with_indicators['macd_signal']

            # Fill NaN values
            df_with_indicators = df_with_indicators.fillna(method='bfill')

            return df_with_indicators

        except Exception as e:
            logger.error(f"Error adding technical indicators: {str(e)}")
            return df

    def normalize_data(self, df: pd.DataFrame, columns: List[str] = None) -> pd.DataFrame:
        """
        Normalize specified columns in the DataFrame.

        Args:
            df: DataFrame to normalize
            columns: List of columns to normalize (if None, normalize all numeric columns)

        Returns:
            DataFrame with normalized columns
        """
        try:
            # Make a copy to avoid modifying the original
            df_normalized = df.copy()

            # If columns not specified, use all numeric columns except 'time'
            if columns is None:
                columns = df.select_dtypes(include=['float64', 'int64']).columns.tolist()
                if 'time' in columns:
                    columns.remove('time')

            # Min-Max normalization
            for col in columns:
                if col in df.columns:
                    min_val = df[col].min()
                    max_val = df[col].max()
                    if max_val > min_val:  # Avoid division by zero
                        df_normalized[f'{col}_norm'] = (df[col] - min_val) / (max_val - min_val)

            return df_normalized

        except Exception as e:
            logger.error(f"Error normalizing data: {str(e)}")
            return df

    def get_cached_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        Get cached data for a symbol and timeframe.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe

        Returns:
            Cached DataFrame or None if not in cache
        """
        cache_key = f"{symbol}_{timeframe}"
        return self.data_cache.get(cache_key)

    def update_cache(self, symbol: str, timeframe: str, df: pd.DataFrame) -> None:
        """
        Update the cache with new data.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            df: DataFrame to cache
        """
        cache_key = f"{symbol}_{timeframe}"
        self.data_cache[cache_key] = df
        self.last_data_times[cache_key] = datetime.now()

    def collect_data_with_indicators(
        self,
        symbol: str,
        timeframe: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        terminal_id: Optional[int] = None,
        save_to_file: bool = True,
        file_format: str = 'csv',
        add_indicators: bool = True,
        normalize: bool = False
    ) -> Optional[pd.DataFrame]:
        """
        Collect historical data with technical indicators.

        Args:
            symbol: Trading symbol (e.g., 'EURUSD')
            timeframe: Timeframe (e.g., 'M5', 'H1')
            start_date: Start date for data collection
            end_date: End date for data collection
            terminal_id: MT5 terminal ID to use
            save_to_file: Whether to save the data to a file
            file_format: File format to save the data ('csv' or 'parquet')
            add_indicators: Whether to add technical indicators
            normalize: Whether to normalize the data

        Returns:
            DataFrame with historical data and indicators or None if collection failed
        """
        # Check cache first
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.data_cache:
            last_update = self.last_data_times.get(cache_key)
            if last_update and (datetime.now() - last_update).total_seconds() < self.update_interval:
                logger.info(f"Using cached data for {symbol} {timeframe}")
                return self.data_cache[cache_key]

        # Collect raw data
        df = self.collect_data(
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            terminal_id=terminal_id,
            save_to_file=False  # We'll save the processed data later
        )

        if df is None:
            return None

        # Add technical indicators if requested
        if add_indicators:
            df = self.add_technical_indicators(df)

        # Normalize data if requested
        if normalize:
            df = self.normalize_data(df)

        # Save to file if requested
        if save_to_file:
            file_path = Path(self.cache_dir) / f"{symbol}_{timeframe}_processed.{file_format}"
            try:
                if file_format.lower() == 'csv':
                    df.to_csv(file_path, index=False)
                elif file_format.lower() == 'parquet':
                    df.to_parquet(file_path, index=False)
                logger.info(f"Saved processed data to {file_path}")
            except Exception as e:
                logger.error(f"Error saving processed data to {file_path}: {str(e)}")

        # Update cache
        self.update_cache(symbol, timeframe, df)

        return df
