"""
MT5 credentials and terminal configurations.
This file should not be committed to version control.
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, Optional, Any

def create_local_config() -> None:
    """Create local configuration file from template if it doesn't exist."""
    template_path = Path("config/local_config_template.json")
    config_path = Path("config/local_config.json")

    if not config_path.exists() and template_path.exists():
        try:
            shutil.copy(template_path, config_path)
            print("\nCreated local configuration file from template.")
            print("Please edit config/local_config.json with your credentials.")
        except Exception as e:
            print(f"Warning: Could not create local config: {str(e)}")

def load_local_config() -> Optional[Dict]:
    """Load configuration from local config file if it exists."""
    config_path = Path("config/local_config.json")
    if config_path.exists():
        try:
            with open(config_path, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load local config: {str(e)}")
    return None

def get_config_value(env_var: str, local_config: Optional[Dict], key: str, default: str = "") -> str:
    """Get configuration value from environment variable or local config."""
    value = os.getenv(env_var, "")
    if not value and local_config:
        value = local_config.get(key, default)
    return value

# Create local config if it doesn't exist
create_local_config()

# Try to load local configuration
local_config = load_local_config()

# MT5 terminal configurations
MT5_TERMINALS: Dict[str, Dict[str, Any]] = {
    "1": {  # Terminal ID 1 - Pepperstone Demo 1
        "login": "",
        "password": "",
        "server": "mt5-demo01.pepperstone.com",
        "path": "C:/Users/<USER>/Desktop/MT5 Pepper 03/Terminal64.exe",
        "trade_mode": True,
        "auto_trading": True
    },
    "2": {  # Terminal ID 2 - Pepperstone Demo 2
        "login": "",
        "password": "",
        "server": "mt5-demo01.pepperstone.com",
        "path": "C:/Users/<USER>/Desktop/MT5 Pepper 02/Terminal64.exe",
        "trade_mode": True,
        "auto_trading": True
    },
    "3": {  # Terminal ID 3 - IC Markets Demo 1
        "login": "",
        "password": "",
        "server": "mt5-demo.icmarkets.com",
        "path": "C:/Users/<USER>/Desktop/MT5 IC 01/Terminal64.exe",
        "trade_mode": True,
        "auto_trading": True
    },
    "4": {  # Terminal ID 4 - IC Markets Demo 2
        "login": "",
        "password": "",
        "server": "mt5-demo.icmarkets.com",
        "path": "C:/Users/<USER>/Desktop/MT5 IC 02/Terminal64.exe",
        "trade_mode": True,
        "auto_trading": True
    },
    "5": {  # Terminal ID 5 - IC Markets Demo 3
        "login": "",
        "password": "",
        "server": "mt5-demo.icmarkets.com",
        "path": "C:/Users/<USER>/Desktop/MT5 IC 03/Terminal64.exe",
        "trade_mode": True,
        "auto_trading": True
    }
}

# Validate configurations
for terminal_id, config in MT5_TERMINALS.items():
    if not all(config.values()):
        print(f"Warning: Missing configuration values for {terminal_id}. Please update the credentials in config/local_config.json")
        print("Required values: path, login, password, server")
        print("Current configuration:")
        print(json.dumps(config, indent=2))

# Add a .gitignore entry to prevent this file from being tracked
try:
    with open('.gitignore', 'a+') as f:
        f.seek(0)
        content = f.read()
        if 'config/credentials.py' not in content:
            f.write('\n# Sensitive credentials\nconfig/credentials.py\n')
        if 'config/local_config.json' not in content:
            f.write('config/local_config.json\n')
except Exception as e:
    print(f"Warning: Could not update .gitignore: {str(e)}")

# Telegram Bot Configuration
TELEGRAM_CONFIG = {
    "bot_token": "",
    "chat_id": ""
}

# API Keys (if needed for additional data sources)
API_KEYS = {
    "alpha_vantage": "",
    "polygon": ""
}

# Database Configuration (if needed)
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "trading_bot",
    "user": "",
    "password": ""
}