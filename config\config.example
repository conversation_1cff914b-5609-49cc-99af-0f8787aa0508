{"mt5": {"max_connections": 5, "timeout": 60000, "retry_interval": 5, "auto_start_terminals": true, "terminals": {"1": {"path": "C:/Users/<USER>/Desktop/MT5 Pepper 03/MT5 portable for Terminal ID 1/terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo01.pepperstone.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "2": {"path": "C:/Users/<USER>/Desktop/MT5 Pepper 02/MT5 portable for Terminal ID 2/terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo01.pepperstone.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "3": {"path": "C:/Users/<USER>/Desktop/MT5 IC 01/MT5 portable for Terminal ID 3/terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo.icmarkets.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "4": {"path": "C:/Users/<USER>/Desktop/MT5 IC 02/MT5 portable for Terminal ID 4/terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo.icmarkets.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "5": {"path": "C:/Users/<USER>/Desktop/MT5 IC 03/MT5 portable for Terminal ID 5/terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo.icmarkets.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "terminal1": {"path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo01.pepperstone.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "terminal2": {"path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo01.pepperstone.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "terminal3": {"path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo.icmarkets.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "terminal4": {"path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo.icmarkets.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}, "terminal5": {"path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe", "login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "mt5-demo.icmarkets.com", "trade_mode": true, "auto_trading": true, "timeout": 60000, "retry_interval": 5}}}, "strategy": {"symbol": "BTCUSD.a", "timeframes": ["M5", "M15", "M30"], "sequence_length": 288, "lot_size": 0.01, "max_positions": 2, "stop_loss_pips": 200, "take_profit_pips": 400, "max_spread_pips": 50, "risk_per_trade": 0.01, "max_daily_loss": 50.0, "max_daily_trades": 5, "cooldown_period": 600, "volatility_threshold": 2.0, "trend_threshold": 0.7, "position_sizing_factor": 0.5}, "models": {"lstm": {"model_path": "lstm_model.h5", "input_dim": 50, "output_dim": 1, "weight": 0.2, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"], "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "num_leaves": 31}, "gru": {"model_path": "gru_model.h5", "input_dim": 50, "output_dim": 1, "weight": 0.2, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"], "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "num_leaves": 31}, "xgboost": {"model_path": "xgboost_model.json", "input_dim": 50, "output_dim": 1, "weight": 0.2, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"], "sequence_length": 288, "batch_size": 32, "epochs": 100, "patience": 10, "learning_rate": 0.01, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "num_leaves": 31}, "lightgbm": {"model_path": "lightgbm_model.txt", "input_dim": 50, "output_dim": 1, "weight": 0.2, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"], "sequence_length": 288, "batch_size": 32, "epochs": 100, "patience": 10, "learning_rate": 0.01, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "num_leaves": 31}, "transformer": {"model_path": "transformer_model.h5", "input_dim": 50, "output_dim": 1, "weight": 0.1, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"], "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.1, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "num_leaves": 31}, "tft": {"model_path": "tft_model.h5", "input_dim": 5, "output_dim": 1, "weight": 0.1, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume"], "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.1, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "num_leaves": 31}}, "data_base_path": "data/", "models_base_path": "models/", "confidence_threshold": 0.65, "update_interval": 60, "max_memory_usage": 85.0, "log_level": "INFO", "debug_mode": false, "system": {"log_level": "INFO", "log_file": "logs/system.log", "min_memory_gb": 4, "environment": "development", "data_dir": "data/", "models_dir": "models/", "reports_dir": "reports"}, "monitoring": {"report_interval": 10, "metrics_interval": 5, "plot_interval": 10, "save_interval": 10, "output_dir": "monitoring", "plots_dir": "plots", "reports_dir": "reports", "max_log_files": 10, "max_log_size_mb": 100}}