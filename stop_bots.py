"""
Script to stop all running trading bots.
"""
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/stop_bots.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Create logs directory
logs_path = Path("logs")
logs_path.mkdir(exist_ok=True)

# Import unified configuration manager
from config.unified_config import config_manager

# Import necessary managers
from utils.enhanced_error_handler import EnhancedErrorHandler
error_handler = EnhancedErrorHandler()

from utils.enhanced_memory_manager import enhanced_memory_manager
memory_manager = enhanced_memory_manager

from utils.thread_manager import ThreadManager
thread_manager = ThreadManager(
    max_workers=32,
    thread_name_prefix="Main"
)

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
mt5_manager = MT5ConnectionManager(config_manager)

# Import TradingBotManager
from main import TradingBotManager

def stop_all_bots():
    """Stop all running trading bots."""
    logger.info("Initializing bot manager to stop all bots...")
    
    # Create a manager instance
    manager = TradingBotManager(
        config_manager=config_manager,
        error_handler=error_handler,
        mt5_manager=mt5_manager,
        thread_manager=thread_manager,
        memory_manager=memory_manager
    )
    
    # Stop all bots
    logger.info("Stopping all trading bots...")
    success = manager.stop_all_bots()
    
    if success:
        logger.info("All trading bots stopped successfully.")
    else:
        logger.error("Failed to stop all trading bots.")
    
    # Perform full shutdown
    logger.info("Performing full shutdown...")
    manager.shutdown()
    
    return success

if __name__ == "__main__":
    try:
        success = stop_all_bots()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Error stopping bots: {e}", exc_info=True)
        sys.exit(1)
