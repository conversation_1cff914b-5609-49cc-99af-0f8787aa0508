"""
Test Trading Bot

This script tests the trading bot functionality using the multi_mt5_connection.py
approach that preserves Algo Trading in MT5.

It implements a simple trading strategy for testing purposes.
"""

import time
import logging
import argparse
import numpy as np
import pandas as pd
import MetaTrader5 as mt5
from multi_mt5_connection import MT5MultiConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingBot:
    """
    Trading bot that uses the MT5MultiConnector to preserve Algo Trading.
    """

    def __init__(self, symbol="BTCUSD.a", timeframe=mt5.TIMEFRAME_M5, volume=0.01):
        """
        Initialize the trading bot.

        Args:
            symbol: Symbol to trade (default: "BTCUSD.a")
            timeframe: Timeframe to use (default: M5)
            volume: Trading volume (default: 0.01)
        """
        self.symbol = symbol
        self.timeframe = timeframe
        self.volume = volume
        self.connector = MT5MultiConnector()
        self.is_running = False
        self.positions = []

        # Strategy parameters
        self.fast_period = 10
        self.slow_period = 20

        # Initialize previous values
        self.fast_ma_prev = 0
        self.slow_ma_prev = 0

    def initialize(self):
        """
        Initialize the trading bot.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        # Get the current terminal
        current_terminal_id = self.connector.get_current_terminal()
        if not current_terminal_id:
            logger.error("Not connected to any terminal")
            return False

        logger.info(f"Connected to terminal {current_terminal_id}")

        # Check if Algo Trading is enabled
        algo_trading_enabled = self.connector.display_terminal_info()
        if not algo_trading_enabled:
            logger.error("Algo Trading is disabled. Please enable it manually.")
            return False

        # Get account info
        account_info = self.connector.display_account_info()
        if not account_info:
            logger.error("Could not get account info")
            return False

        # Get symbol info
        symbol_info = self.connector.display_symbol_info(self.symbol)
        if not symbol_info:
            logger.error(f"Symbol {self.symbol} not found")
            return False

        # Set minimum volume
        self.volume = max(self.volume, symbol_info["volume_min"])

        logger.info(f"Trading bot initialized for {self.symbol} on {self.timeframe_to_string()} timeframe")
        logger.info(f"Trading volume: {self.volume}")

        return True

    def timeframe_to_string(self):
        """Convert timeframe to string representation."""
        timeframes = {
            mt5.TIMEFRAME_M1: "M1",
            mt5.TIMEFRAME_M5: "M5",
            mt5.TIMEFRAME_M15: "M15",
            mt5.TIMEFRAME_M30: "M30",
            mt5.TIMEFRAME_H1: "H1",
            mt5.TIMEFRAME_H4: "H4",
            mt5.TIMEFRAME_D1: "D1",
            mt5.TIMEFRAME_W1: "W1",
            mt5.TIMEFRAME_MN1: "MN1"
        }
        return timeframes.get(self.timeframe, f"Unknown ({self.timeframe})")

    def get_historical_data(self, bars=100):
        """
        Get historical data for the symbol and calculate indicators.

        Args:
            bars: Number of bars to get

        Returns:
            pd.DataFrame: DataFrame with historical data and indicators
        """
        # Get historical data
        rates = mt5.copy_rates_from_pos(self.symbol, self.timeframe, 0, bars)
        if rates is None or len(rates) == 0:
            logger.error(f"Could not get historical data for {self.symbol}")
            return None

        # Convert to DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')

        # Calculate basic indicators
        df['fast_ma'] = df['close'].rolling(window=self.fast_period).mean()
        df['slow_ma'] = df['close'].rolling(window=self.slow_period).mean()

        # Calculate additional indicators for model
        df['price_change'] = df['close'].pct_change()
        df['volatility'] = df['close'].rolling(window=10).std()
        df['range'] = df['high'] - df['low']
        df['momentum'] = df['close'] - df['close'].shift(5)

        # Generate model prediction
        df = self.apply_model(df)

        return df

    def apply_model(self, df):
        """
        Apply a simple model to predict price movements.

        Args:
            df: DataFrame with historical data and indicators

        Returns:
            pd.DataFrame: DataFrame with model predictions
        """
        # Simple model: predict up if momentum is positive and volatility is low
        df['model_prediction'] = 0.0

        # Fill NaN values
        df = df.fillna(0)

        # Apply model logic
        for i in range(10, len(df)):
            # Positive momentum and low volatility predict up
            if df['momentum'].iloc[i] > 0 and df['volatility'].iloc[i] < df['volatility'].iloc[i-5]:
                df.loc[df.index[i], 'model_prediction'] = 1.0  # Predict up
            # Negative momentum and high volatility predict down
            elif df['momentum'].iloc[i] < 0 and df['volatility'].iloc[i] > df['volatility'].iloc[i-5]:
                df.loc[df.index[i], 'model_prediction'] = -1.0  # Predict down

        logger.info(f"Model prediction: {df['model_prediction'].iloc[-1]}")
        return df

    def check_positions(self):
        """
        Check open positions.

        Returns:
            list: List of open positions
        """
        positions = mt5.positions_get(symbol=self.symbol)
        if positions is None:
            logger.info(f"No open positions for {self.symbol}")
            return []

        # Convert to list of dictionaries
        positions_list = []
        for position in positions:
            position_dict = {
                "ticket": position.ticket,
                "type": "BUY" if position.type == mt5.POSITION_TYPE_BUY else "SELL",
                "volume": position.volume,
                "price_open": position.price_open,
                "price_current": position.price_current,
                "profit": position.profit
            }
            positions_list.append(position_dict)
            logger.info(f"Position: {position_dict}")

        return positions_list

    def execute_strategy(self, df):
        """
        Execute the trading strategy.

        Args:
            df: DataFrame with historical data

        Returns:
            bool: True if strategy executed successfully, False otherwise
        """
        if df is None or len(df) < self.slow_period:
            logger.error("Not enough data to execute strategy")
            return False

        # Get the latest values
        fast_ma = df['fast_ma'].iloc[-1]
        slow_ma = df['slow_ma'].iloc[-1]

        # Store the values for real strategy (currently unused in test mode)
        self.fast_ma_prev = fast_ma
        self.slow_ma_prev = slow_ma

        # Get previous values for comparison
        if len(df) > 1:
            fast_ma_prev = df['fast_ma'].iloc[-2]
            slow_ma_prev = df['slow_ma'].iloc[-2]
        else:
            fast_ma_prev = self.fast_ma_prev
            slow_ma_prev = self.slow_ma_prev

        # Combine traditional strategy with model predictions
        traditional_buy = fast_ma > slow_ma and fast_ma_prev <= slow_ma_prev
        traditional_sell = fast_ma < slow_ma and fast_ma_prev >= slow_ma_prev

        # Get model prediction
        model_prediction = df['model_prediction'].iloc[-1]
        model_buy = model_prediction > 0
        model_sell = model_prediction < 0

        # Generate signals based on both traditional strategy and model
        buy_signal = traditional_buy or model_buy
        sell_signal = traditional_sell or model_sell

        logger.info(f"Traditional signals - Buy: {traditional_buy}, Sell: {traditional_sell}")
        logger.info(f"Model signals - Buy: {model_buy}, Sell: {model_sell}")

        logger.info(f"Iteration: Buy signal: {buy_signal}, Sell signal: {sell_signal}")
        logger.info(f"Fast MA: {fast_ma:.2f}, Slow MA: {slow_ma:.2f}")
        logger.info(f"Fast MA Prev: {fast_ma_prev:.2f}, Slow MA Prev: {slow_ma_prev:.2f}")

        # Check current positions
        positions = self.check_positions()
        has_buy_position = any(p["type"] == "BUY" for p in positions)
        has_sell_position = any(p["type"] == "SELL" for p in positions)

        # Execute signals
        if buy_signal and not has_buy_position:
            logger.info("BUY signal detected")
            success = self.connector.place_order(
                self.symbol, "BUY", self.volume,
                comment=f"Bot BUY signal on {self.timeframe_to_string()}"
            )
            if success:
                logger.info("BUY order placed successfully")
            else:
                logger.error("Failed to place BUY order")

        if sell_signal and not has_sell_position:
            logger.info("SELL signal detected")
            success = self.connector.place_order(
                self.symbol, "SELL", self.volume,
                comment=f"Bot SELL signal on {self.timeframe_to_string()}"
            )
            if success:
                logger.info("SELL order placed successfully")
            else:
                logger.error("Failed to place SELL order")

        # Previous values already stored above

        return True

    def run(self, iterations=10, interval=60):
        """
        Run the trading bot.

        Args:
            iterations: Number of iterations to run (default: 10)
            interval: Interval between iterations in seconds (default: 60)
        """
        if not self.initialize():
            logger.error("Failed to initialize trading bot")
            return

        self.is_running = True
        iteration = 0

        try:
            while self.is_running and (iterations <= 0 or iteration < iterations):
                logger.info(f"Running iteration {iteration + 1}")

                # Get historical data
                df = self.get_historical_data()
                if df is not None:
                    logger.info(f"Got {len(df)} bars of historical data")

                    # Execute strategy
                    self.execute_strategy(df)

                # Increment iteration counter
                iteration += 1

                # Check if we should continue
                if iterations <= 0 or iteration < iterations:
                    logger.info(f"Waiting {interval} seconds for next iteration...")
                    time.sleep(interval)

        except KeyboardInterrupt:
            logger.info("Trading bot stopped by user")

        except Exception as e:
            logger.error(f"Error running trading bot: {str(e)}")

        finally:
            self.is_running = False
            logger.info("Trading bot stopped")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Trading Bot")
    parser.add_argument("--symbol", type=str, default="BTCUSD.a", help="Symbol to trade")
    parser.add_argument("--timeframe", type=str, default="M5", help="Timeframe (M1, M5, M15, M30, H1, H4, D1)")
    parser.add_argument("--volume", type=float, default=0.01, help="Trading volume")
    parser.add_argument("--iterations", type=int, default=10, help="Number of iterations (0 for infinite)")
    parser.add_argument("--interval", type=int, default=60, help="Interval between iterations in seconds")
    args = parser.parse_args()

    # Convert timeframe string to MT5 timeframe
    timeframes = {
        "M1": mt5.TIMEFRAME_M1,
        "M5": mt5.TIMEFRAME_M5,
        "M15": mt5.TIMEFRAME_M15,
        "M30": mt5.TIMEFRAME_M30,
        "H1": mt5.TIMEFRAME_H1,
        "H4": mt5.TIMEFRAME_H4,
        "D1": mt5.TIMEFRAME_D1,
        "W1": mt5.TIMEFRAME_W1,
        "MN1": mt5.TIMEFRAME_MN1
    }
    timeframe = timeframes.get(args.timeframe.upper(), mt5.TIMEFRAME_M5)

    # Create and run the trading bot
    bot = TradingBot(args.symbol, timeframe, args.volume)
    bot.run(args.iterations, args.interval)

if __name__ == "__main__":
    main()
