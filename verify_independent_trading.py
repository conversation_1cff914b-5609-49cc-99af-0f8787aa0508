"""
Verification script for the independent trading system.

This script verifies that the independent trading system is set up correctly
by checking that all components are initialized correctly and can work together.
"""

import logging
from independent_trading_bot import TerminalConfig, IndependentTradingBot, TradingManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def verify_terminal_config():
    """Verify that TerminalConfig is initialized correctly."""
    logger.info("Verifying TerminalConfig...")

    # Create a terminal config
    terminal_config = TerminalConfig(
        terminal_id=1,
        primary_model="lstm",
        allocation_percentage=20
    )

    # Check that model weights are initialized correctly
    assert terminal_config.model_weights is not None, "Model weights not initialized"
    assert terminal_config.model_weights["lstm"] == 0.6, "Primary model weight not set correctly"
    assert terminal_config.model_weights["arima"] == 0.1, "Secondary model weight not set correctly"

    logger.info("TerminalConfig verified successfully")
    return terminal_config

def verify_independent_trading_bot(terminal_config):
    """Verify that IndependentTradingBot is initialized correctly."""
    logger.info("Verifying IndependentTradingBot...")

    # Create a trading bot
    bot = IndependentTradingBot(terminal_config)

    # Check that model manager is initialized correctly
    assert bot.model_manager is not None, "Model manager not initialized"

    # Check that signal generator is initialized correctly
    assert bot.signal_generator is not None, "Signal generator not initialized"

    # Check that model weights are set correctly
    model_weights = bot.model_manager.get_model_weights()
    assert model_weights is not None, "Model weights not set"
    assert model_weights["lstm"] > model_weights["arima"], "Primary model weight not higher than secondary model weight"

    logger.info("IndependentTradingBot verified successfully")
    return bot

def verify_trading_manager():
    """Verify that TradingManager is initialized correctly."""
    logger.info("Verifying TradingManager...")

    # Create a trading manager
    manager = TradingManager()

    # Check that trading bots are initialized correctly
    assert len(manager.trading_bots) == 5, "Trading bots not initialized correctly"

    # Check that each bot has a different primary model
    primary_models = [bot.primary_model for bot in manager.trading_bots.values()]
    assert len(set(primary_models)) == 5, "Not all bots have different primary models"

    # Check that allocations sum to 100%
    total_allocation = sum(bot.allocation_percentage for bot in manager.trading_bots.values())
    assert abs(total_allocation - 100) < 0.01, f"Total allocation is {total_allocation}%, not 100%"

    logger.info("TradingManager verified successfully")
    return manager

def main():
    """Main function."""
    try:
        logger.info("Starting verification of independent trading system...")

        # Verify terminal config
        terminal_config = verify_terminal_config()

        # Verify independent trading bot
        bot = verify_independent_trading_bot(terminal_config)

        # Verify trading manager
        manager = verify_trading_manager()

        logger.info("Independent trading system verified successfully")
        return 0

    except AssertionError as e:
        logger.error(f"Verification failed: {str(e)}")
        return 1

    except Exception as e:
        logger.error(f"Error during verification: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
