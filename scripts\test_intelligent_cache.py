"""
Test script for the intelligent cache system.
"""
import os
import sys
import logging
import time
import numpy as np
import pandas as pd
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_intelligent_cache():
    """Test the intelligent cache system."""
    try:
        from utils.intelligent_cache import (
            IntelligentCache,
            CacheTier,
            EvictionPolicy,
            get_cache,
            get_global_cache,
            cache_get,
            cache_set,
            cache_remove,
            cache_clear,
            get_cache_stats
        )

        logger.info("Testing intelligent cache system")

        # Test basic cache operations
        cache = IntelligentCache(name="test", memory_limit_mb=10, disk_limit_mb=100)

        # Test setting and getting items
        logger.info("Testing basic cache operations")
        cache.set("key1", "value1")
        cache.set("key2", {"name": "test", "value": 123})
        cache.set("key3", [1, 2, 3, 4, 5])

        # Get items
        value1 = cache.get("key1")
        value2 = cache.get("key2")
        value3 = cache.get("key3")

        logger.info(f"Retrieved values: {value1}, {value2}, {value3}")
        assert value1 == "value1", "Value 1 mismatch"
        assert value2["name"] == "test", "Value 2 mismatch"
        assert value3 == [1, 2, 3, 4, 5], "Value 3 mismatch"

        # Test non-existent key
        value4 = cache.get("key4", "default")
        logger.info(f"Non-existent key: {value4}")
        assert value4 == "default", "Default value not returned"

        # Test removing items
        cache.remove("key1")
        value1_after = cache.get("key1", "removed")
        logger.info(f"After removal: {value1_after}")
        assert value1_after == "removed", "Item not removed"

        # Test cache stats
        stats = cache.get_stats()
        logger.info(f"Cache stats: {stats}")
        assert stats["hits"] > 0, "No cache hits recorded"
        assert stats["memory_items"] > 0, "No memory items recorded"

        # Test TTL
        logger.info("Testing TTL")
        cache.set("ttl_key", "expires_soon", ttl=2)
        assert cache.get("ttl_key") == "expires_soon", "TTL item not set"
        time.sleep(3)
        assert cache.get("ttl_key", "expired") == "expired", "TTL item did not expire"

        # Test different tiers
        logger.info("Testing different cache tiers")

        # Small item - should go to memory
        small_item = "small_item"
        cache.set("small_key", small_item)
        assert cache.get("small_key") == small_item, "Small item not cached"

        # Medium item - should go to compressed or memory
        medium_item = {"data": [i for i in range(1000)]}
        cache.set("medium_key", medium_item)
        assert cache.get("medium_key")["data"][500] == 500, "Medium item not cached correctly"

        # Large item - should go to disk
        large_item = np.random.random((1000, 10)).tolist()
        cache.set("large_key", large_item)
        retrieved_large_item = cache.get("large_key")
        assert retrieved_large_item is not None, "Large item not cached"
        assert len(retrieved_large_item) == 1000, "Large item not cached correctly"

        # Test clearing cache
        logger.info("Testing cache clearing")
        cache.clear()
        stats_after_clear = cache.get_stats()
        logger.info(f"Stats after clear: {stats_after_clear}")
        assert stats_after_clear["memory_items"] == 0, "Memory cache not cleared"

        # Test global cache
        logger.info("Testing global cache")
        global_cache = get_global_cache()
        cache_set("global_key", "global_value")
        assert cache_get("global_key") == "global_value", "Global cache not working"

        # Test named caches
        logger.info("Testing named caches")
        cache1 = get_cache("cache1")
        cache2 = get_cache("cache2")

        cache1.set("key", "value1")
        cache2.set("key", "value2")

        assert cache1.get("key") == "value1", "Named cache 1 not working"
        assert cache2.get("key") == "value2", "Named cache 2 not working"

        # Test with pandas DataFrame
        logger.info("Testing with pandas DataFrame")
        df = pd.DataFrame({
            'A': np.random.rand(1000),
            'B': np.random.rand(1000),
            'C': np.random.rand(1000)
        })

        cache.set("dataframe", df)
        df_cached = cache.get("dataframe")
        assert isinstance(df_cached, pd.DataFrame), "DataFrame not cached correctly"
        assert len(df_cached) == 1000, "DataFrame size mismatch"

        # Test eviction
        logger.info("Testing cache eviction")
        # Use a very small memory limit to force evictions
        tiny_cache = IntelligentCache(name="tiny_test", memory_limit_mb=0.01, disk_limit_mb=0.5)

        # Fill cache beyond capacity
        for i in range(20):
            # Each item is about 1KB
            tiny_cache.set(f"fill_key_{i}", "x" * 1024)
            # Access some items to make LRU work properly
            if i > 0 and i % 2 == 0:
                tiny_cache.get(f"fill_key_{i-1}")

        # Try to get an item that should have been evicted
        first_item = tiny_cache.get("fill_key_0", "evicted")
        logger.info(f"First item after eviction test: {first_item}")

        # Check stats
        stats_after_fill = tiny_cache.get_stats()
        logger.info(f"Stats after fill: {stats_after_fill}")

        # Either the item was evicted or we had evictions
        assert first_item == "evicted" or stats_after_fill["evictions"] > 0, "Eviction test failed"

        logger.info("Intelligent cache test passed")
        return True

    except Exception as e:
        logger.error(f"Error testing intelligent cache: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_intelligent_cache():
        logger.info("Intelligent cache test passed")
        sys.exit(0)
    else:
        logger.error("Intelligent cache test failed")
        sys.exit(1)
