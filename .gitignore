# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Sensitive files
config/credentials.py
config/local_config.json
config/.encryption_key
*.bak
test_mt5_connections.py

# Data files
data/
*.csv
*.json
*.pkl
*.h5
*.model
*.npy
*.npz

# Model files
models/saved/
models/*/
*.pt
*.pth
*.hdf5
*.keras
*.pb
*.ckpt
*.joblib
*.scaler.joblib
*.exe
*.data-*
*.index
*.variables
*.fingerprint.pb
*.keras_metadata.pb
*.saved_model.pb

# Images and reports
reports/plots/
*.png

# System files
.DS_Store
Thumbs.db

# Sensitive configuration files
monitoring/
*.json.bak
