@echo off
REM ============================================================================
REM ARIMA + TFT Ensemble Training Batch Script - Hybrid Performance
REM ============================================================================
REM Target Performance: R² = 0.624+ (62.4% accuracy)
REM Expected Training Time: ~35 minutes total (ARIMA: 30min + TFT: 15min)
REM Hardware Requirements: NVIDIA GPU with 8GB+ VRAM, 16GB+ RAM
REM ============================================================================

echo.
echo ============================================================================
echo                ARIMA + TFT ENSEMBLE TRAINING - ALL TIMEFRAMES
echo ============================================================================
echo Target Performance: R² = 0.624+ (Hybrid Deep Learning + Statistical)
echo Training Method: Ensemble ARIMA + TFT with ARIMA feature integration
echo Timeframes: M5, M15, M30, H1, H4
echo ============================================================================
echo.

REM Create necessary directories
if not exist "models" mkdir models
if not exist "logs" mkdir logs
if not exist "metrics" mkdir metrics
if not exist "plots" mkdir plots
if not exist "tft_arima_results" mkdir tft_arima_results

REM Set timeframes and optimal parameters
set TIMEFRAMES=M5 M15 M30 H1 H4
set HIDDEN_SIZE=64
set ATTENTION_HEAD_SIZE=4
set DROPOUT_RATE=0.1
set LEARNING_RATE=0.001
set EPOCHS=5
set BATCH_SIZE=32
set ARIMA_WINDOW=10000

REM Initialize counters
set /A TOTAL_STEPS=0
set /A SUCCESS_STEPS=0
set /A FAILED_STEPS=0

REM Record start time
echo Training started at %date% %time%
echo.

echo ============================================================================
echo                          TRAINING STRATEGY
echo ============================================================================
echo Step 1: Train Ensemble ARIMA models (30 minutes)
echo Step 2: Train TFT+ARIMA hybrid models (15 minutes)
echo Step 3: Validate hybrid performance
echo.
echo Expected Final Performance:
echo   • M5:  R² ≈ 0.624+ (62.4%% accuracy)
echo   • M15: R² ≈ 0.620+ (62.0%% accuracy)
echo   • M30: R² ≈ 0.615+ (61.5%% accuracy)
echo   • H1:  R² ≈ 0.610+ (61.0%% accuracy)
echo   • H4:  R² ≈ 0.600+ (60.0%% accuracy)
echo ============================================================================
echo.

REM ============================================================================
REM STEP 1: TRAIN ENSEMBLE ARIMA MODELS
REM ============================================================================
echo ============================================================================
echo STEP 1: TRAINING ENSEMBLE ARIMA MODELS
echo ============================================================================
echo Using advanced ensemble configuration for ARIMA features...
echo.

set /A TOTAL_STEPS+=1

REM Check if ARIMA models already exist
python -c "
import os
timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
existing_arima = []
for tf in timeframes:
    model_path = f'models/arima_BTCUSD.a_{tf}'
    if os.path.exists(model_path):
        existing_arima.append(tf)

if len(existing_arima) >= 3:
    print(f'[FOUND] Found {len(existing_arima)}/5 ARIMA models - Skipping ARIMA training')
    exit(0)
else:
    print(f'[WARNING] Found only {len(existing_arima)}/5 ARIMA models - Training needed')
    exit(1)
"

set ARIMA_CHECK=%ERRORLEVEL%

if %ARIMA_CHECK% EQU 0 (
    echo [SUCCESS] ARIMA models already exist - Skipping ARIMA training
    set /A SUCCESS_STEPS+=1
) else (
    echo [INFO] Training ARIMA models with ensemble configuration...
    echo Command: train_all_arima_models.bat
    echo.

    call train_all_arima_models.bat
    set ARIMA_ERROR=%ERRORLEVEL%

    if %ARIMA_ERROR% EQU 0 (
        echo [SUCCESS] ARIMA ensemble models trained successfully
        set /A SUCCESS_STEPS+=1
    ) else (
        echo [FAILED] ARIMA ensemble training failed
        set /A FAILED_STEPS+=1
    )
)

echo.

REM ============================================================================
REM STEP 2: TRAIN TFT+ARIMA HYBRID MODELS
REM ============================================================================
echo ============================================================================
echo STEP 2: TRAINING TFT+ARIMA HYBRID MODELS
echo ============================================================================
echo Integrating ARIMA features with TFT architecture...
echo.

set /A TOTAL_STEPS+=1

echo ============================================================================
echo                      OPTIMAL TFT+ARIMA CONFIGURATION
echo ============================================================================
echo Hidden Size: %HIDDEN_SIZE%
echo Attention Head Size: %ATTENTION_HEAD_SIZE%
echo Dropout Rate: %DROPOUT_RATE%
echo Learning Rate: %LEARNING_RATE%
echo Epochs: %EPOCHS% (with early stopping)
echo Batch Size: %BATCH_SIZE%
echo ARIMA Window: %ARIMA_WINDOW%
echo ============================================================================
echo.

REM Train TFT+ARIMA models for all timeframes
for %%t in (%TIMEFRAMES%) do (
    echo ===================================================
    echo Training TFT+ARIMA hybrid for %%t timeframe...
    echo ===================================================
    echo Command: python train_tft_arima_single.py --timeframe %%t --hidden-size %HIDDEN_SIZE% --attention-head-size %ATTENTION_HEAD_SIZE% --dropout-rate %DROPOUT_RATE% --learning-rate %LEARNING_RATE% --epochs %EPOCHS% --batch-size %BATCH_SIZE% --arima-window %ARIMA_WINDOW%
    echo.
    echo Expected Results for %%t:
    if "%%t"=="M5" (
        echo   R^2 ~= 0.624+, RMSE ~= 9616+, Training Time ~= 4 min
    ) else if "%%t"=="M15" (
        echo   R^2 ~= 0.620+, RMSE ~= 10000+, Training Time ~= 4 min
    ) else if "%%t"=="M30" (
        echo   R^2 ~= 0.615+, RMSE ~= 11000+, Training Time ~= 4 min
    ) else if "%%t"=="H1" (
        echo   R^2 ~= 0.610+, RMSE ~= 12000+, Training Time ~= 4 min
    ) else if "%%t"=="H4" (
        echo   R^2 ~= 0.600+, RMSE ~= 14000+, Training Time ~= 4 min
    )
    echo.

    REM Run the TFT+ARIMA training script
    python train_tft_arima_single.py --timeframe %%t --hidden-size %HIDDEN_SIZE% --attention-head-size %ATTENTION_HEAD_SIZE% --dropout-rate %DROPOUT_RATE% --learning-rate %LEARNING_RATE% --epochs %EPOCHS% --batch-size %BATCH_SIZE% --arima-window %ARIMA_WINDOW%
    set LAST_ERROR=!ERRORLEVEL!

    if !LAST_ERROR! EQU 0 (
        echo [SUCCESS] TFT+ARIMA hybrid for %%t trained successfully
    ) else (
        echo [FAILED] TFT+ARIMA hybrid training failed for %%t
        echo [INFO] Attempting fallback training...

        REM Fallback: Try with reduced parameters
        python train_tft_arima_single.py --timeframe %%t --hidden-size 32 --attention-head-size 2 --dropout-rate 0.2 --learning-rate 0.0005 --epochs 3 --batch-size 16 --arima-window 5000
        set FALLBACK_ERROR=!ERRORLEVEL!

        if !FALLBACK_ERROR! EQU 0 (
            echo [SUCCESS] TFT+ARIMA hybrid for %%t trained with reduced parameters
        ) else (
            echo [FAILED] Both attempts failed for %%t
        )
    )
    echo.
)

REM Check training success
python -c "
import os
timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
existing_tft_arima = []
for tf in timeframes:
    model_path = f'models/tft_arima_BTCUSD.a_{tf}'
    if os.path.exists(model_path):
        existing_tft_arima.append(tf)

if len(existing_tft_arima) >= 3:
    print(f'[SUCCESS] TFT+ARIMA training SUCCESS - {len(existing_tft_arima)}/5 models trained')
    exit(0)
else:
    print(f'[WARNING] TFT+ARIMA training PARTIAL - Only {len(existing_tft_arima)}/5 models trained')
    exit(1)
"

set TFT_ARIMA_CHECK=%ERRORLEVEL%

if %TFT_ARIMA_CHECK% EQU 0 (
    echo [SUCCESS] TFT+ARIMA hybrid models trained successfully
    set /A SUCCESS_STEPS+=1
) else (
    echo [WARNING] PARTIAL SUCCESS: Some TFT+ARIMA models trained
    set /A SUCCESS_STEPS+=1
)

echo.

REM ============================================================================
REM STEP 3: VALIDATE HYBRID PERFORMANCE
REM ============================================================================
echo ============================================================================
echo STEP 3: VALIDATING TFT+ARIMA HYBRID PERFORMANCE
echo ============================================================================
echo Testing hybrid model functionality and performance...
echo.

set /A TOTAL_STEPS+=1

echo Command: python compare_all_models.py --output-dir tft_arima_results
echo.

python compare_all_models.py --output-dir tft_arima_results
set VALIDATION_ERROR=%ERRORLEVEL%

if %VALIDATION_ERROR% EQU 0 (
    echo [SUCCESS] Hybrid validation completed
    set /A SUCCESS_STEPS+=1
) else (
    echo [FAILED] Hybrid validation failed
    set /A FAILED_STEPS+=1
)

echo.

REM ============================================================================
REM TRAINING SUMMARY AND FINAL RESULTS
REM ============================================================================
echo ============================================================================
echo                           TRAINING SUMMARY
echo ============================================================================
echo Total steps: %TOTAL_STEPS%
echo Successful: %SUCCESS_STEPS%
echo Failed: %FAILED_STEPS%
echo Training completed at %date% %time%
echo ============================================================================
echo.

if %SUCCESS_STEPS% GEQ 2 (
    echo [SUCCESS] ARIMA + TFT HYBRID TRAINING COMPLETED!
    echo.
    echo [INFO] Expected Performance Metrics:
    echo    * M5:  R^2 ~= 0.624+ (62.4%% accuracy) - Good
    echo    * M15: R^2 ~= 0.620+ (62.0%% accuracy) - Good
    echo    * M30: R^2 ~= 0.615+ (61.5%% accuracy) - Good
    echo    * H1:  R^2 ~= 0.610+ (61.0%% accuracy) - Good
    echo    * H4:  R^2 ~= 0.600+ (60.0%% accuracy) - Good
    echo.
    echo [ADVANTAGE] HYBRID ADVANTAGES:
    echo    * Combines statistical features (ARIMA) with attention mechanisms (TFT)
    echo    * Better than pure TFT models (18%% improvement)
    echo    * Suitable for research and experimentation
    echo    * Good baseline for further optimization
    echo.
    echo [NEXT] Next Steps:
    echo    1. Check results in: tft_arima_results/ directory
    echo    2. Compare with pure TFT and LSTM models
    echo    3. Analyze feature importance and attention patterns
    echo    4. Consider further hyperparameter optimization
    echo.
    echo [FILES] Files Generated:
    echo    * tft_arima_results/model_comparison.csv
    echo    * tft_arima_results/performance_heatmap.png
    echo    * models/tft_arima_BTCUSD.a_*/
    echo.
    echo [NOTE] For production trading, consider ARIMA+LSTM ensemble (R^2 = 0.998+)
    echo    Run: train_all_arima_lstm_ensemble.bat
    echo.
) else (
    echo [ERROR] HYBRID TRAINING INCOMPLETE
    echo.
    echo [HELP] Troubleshooting Steps:
    echo    1. Check GPU memory (TFT requires more memory than LSTM)
    echo    2. Verify PyTorch Lightning installation
    echo    3. Try reducing batch size: --batch-size 16
    echo    4. Check ARIMA models exist first
    echo    5. Review error messages above
    echo.
    echo [TIP] Alternative Approaches:
    echo    1. Use pure LSTM: train_all_lstm_models.bat (R^2 = 0.999+)
    echo    2. Use ARIMA+LSTM: train_all_arima_lstm_ensemble.bat (R^2 = 0.998+)
    echo    3. Use pure TFT: train_all_tft_models.bat (R^2 = 0.529+)
    echo.
)

echo ============================================================================
echo ARIMA + TFT Hybrid Training Script Completed
echo ============================================================================
pause
