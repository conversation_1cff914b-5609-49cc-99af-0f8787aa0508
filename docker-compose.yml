version: '3'

services:
  tensorflow-gpu:
    image: tensorflow/tensorflow:latest-gpu
    container_name: tensorflow-training
    volumes:
      - .:/workspace
    working_dir: /workspace
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    command: python train_with_tensorflow.py --timeframes all --models all
    environment:
      - TF_FORCE_GPU_ALLOW_GROWTH=true
      - CUDA_VISIBLE_DEVICES=0
