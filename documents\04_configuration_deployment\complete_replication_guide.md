# Complete Model Replication Guide

## Executive Summary

This guide provides step-by-step instructions for replicating all 5 model types (LSTM, ARIMA, TFT, ARIMA+LSTM, ARIMA+TFT) in any new project or environment. Based on real current codebase analysis and proven configurations.

**Document Location**: `documents/04_replication_instructions/complete_replication_guide.md`
**Last Updated**: 2025-05-26
**Based on**: Real training scripts and successful configurations

## Prerequisites and Environment Setup

### **System Requirements**

#### **Minimum Requirements**
- **GPU**: NVIDIA GTX 1060 (6GB VRAM)
- **RAM**: 16GB
- **Storage**: 10GB free space
- **OS**: Windows 10/11, Linux, macOS

#### **Recommended Requirements**
- **GPU**: NVIDIA RTX 2070+ (8GB+ VRAM)
- **RAM**: 32GB
- **Storage**: 20GB free space (SSD)
- **OS**: Windows 10/11 with WSL2 or Linux

### **Software Dependencies**

```bash
# Core ML Stack (Exact Versions)
pip install torch==2.6.0+cu118 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install scikit-learn==1.3.0

# Statistical Models
pip install pmdarima==2.0.3
pip install statsmodels==0.14.0

# Visualization
pip install matplotlib==3.7.2
pip install seaborn==0.12.2

# Optional Advanced
pip install pytorch-lightning==2.0.9
pip install pytorch-forecasting==1.0.0
```

### **Data Structure Requirements**

```python
# Required Directory Structure
project_root/
├── data/
│   └── historical/
│       └── btcusd.a/  # Symbol directory
│           ├── BTCUSD.a_M5.parquet
│           ├── BTCUSD.a_M15.parquet
│           ├── BTCUSD.a_M30.parquet
│           ├── BTCUSD.a_H1.parquet
│           └── BTCUSD.a_H4.parquet
├── models/  # Output directory
├── metrics/ # Performance metrics
├── plots/   # Visualizations
└── logs/    # Training logs

# Data Format Requirements
REQUIRED_COLUMNS = [
    "time",        # datetime64[ns], timezone-aware
    "open",        # float64, opening price
    "high",        # float64, highest price
    "low",         # float64, lowest price
    "close",       # float64, closing price (target)
    "real_volume"  # float64, trading volume
]
```

## Model-Specific Replication Instructions

### **1. LSTM Model Replication (R² = 0.999+)**

#### **Training Scripts Available**
- **Batch Training**: `train_lstm_btcusd.py` (all timeframes)
- **Single Training**: `train_lstm_single.py` (individual timeframes)
- **Windows Batch**: `train_all_lstm_models.bat`

#### **Exact Configuration**
```python
LSTM_CONFIG = {
    "hidden_units": 64,      # Optimal for financial data
    "num_layers": 2,         # Best complexity/performance balance
    "dropout_rate": 0.2,     # Prevents overfitting
    "learning_rate": 0.001,  # Adam optimizer default
    "epochs": 100,           # Sufficient for convergence
    "batch_size": 32,        # Optimal for GPU memory
    "sequence_length": 60,   # 60 time steps lookback
}
```

#### **Replication Commands**
```bash
# All timeframes (recommended)
python train_lstm_btcusd.py

# Individual timeframe
python train_lstm_single.py --timeframe M5 --hidden-units 64 --num-layers 2 --dropout-rate 0.2 --learning-rate 0.001 --epochs 100 --batch-size 32

# Windows batch
train_all_lstm_models.bat
```

#### **Expected Results**
- M5: R² = 0.9999, RMSE = 247.58, Training Time = 3 min
- M15: R² = 0.9997, RMSE = 402.26, Training Time = 3 min
- M30: R² = 0.9996, RMSE = 425.14, Training Time = 3 min
- H1: R² = 0.9988, RMSE = 874.45, Training Time = 3 min
- H4: R² = 0.9992, RMSE = 702.38, Training Time = 3 min

### **2. ARIMA Model Replication (R² = 0.978+)**

#### **Training Scripts Available**
- **Single Training**: `train_arima_single.py`
- **Windows Batch**: `train_all_arima_models.bat`
- **Linux Batch**: `train_all_arima_models.sh`

#### **Critical Configuration (Ensemble)**
```python
ARIMA_CONFIG = {
    "max_rows": 50000,           # 5x more data than default (CRITICAL)
    "data_selection": "all",     # Complete dataset (not 'recent')
    "use_ensemble": True,        # Activates 7-model ensemble
    "ensemble_models": 5,        # Meta-learning with 5 meta-models
    "auto_arima": True,          # Optimal parameter selection
}
```

#### **Exact Replication Command**
```bash
# CRITICAL: Exact command for R² = 0.9784 performance
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --max-rows 50000 --data-selection all --use-ensemble --ensemble-models 5
```

#### **Expected Results**
- M5: R² = 0.9784, RMSE = 2,306.45, MAPE = 2.08%, Training Time = 6 min
- M15: R² = 0.975+, Training Time = 5 min
- M30: R² = 0.970+, Training Time = 4 min
- H1: R² = 0.943+, Training Time = 3 min
- H4: R² = 0.920+, Training Time = 2 min

### **3. TFT Model Replication (R² = 0.529+)**

#### **Training Scripts Available**
- **PyTorch TFT**: `train_tft_pytorch.py` (recommended)
- **PyTorch Forecasting**: `train_tft_single.py` (fallback)
- **Windows Batch**: `train_all_tft_models.bat`

#### **Fixed Configuration**
```python
TFT_CONFIG = {
    "hidden_dim": 64,        # Hidden dimension
    "num_heads": 4,          # Multi-head attention
    "num_layers": 2,         # Transformer layers
    "dropout_rate": 0.1,     # Regularization
    "learning_rate": 0.001,  # Adam optimizer
    "epochs": 5,             # Early stopping target
    "batch_size": 32,        # Optimal for GPU
}
```

#### **Replication Commands**
```bash
# Fixed TFT (60.5% improvement over previous versions)
python train_tft_pytorch.py --timeframe M5 --hidden-dim 64 --num-heads 4 --num-layers 2 --dropout-rate 0.1 --learning-rate 0.001 --epochs 5 --batch-size 32

# Windows batch
train_all_tft_models.bat
```

#### **Expected Results**
- M5: R² = 0.529, RMSE = 10,768, Training Time = 3 min
- Early stopping at epoch 4-5
- Healthy training pattern (no overfitting)

### **4. ARIMA+LSTM Ensemble Replication (R² = 0.998+)**

#### **Training Scripts Available**
- **Complete Automation**: `train_all_arima_lstm_ensemble.bat`
- **Manual Steps**: Individual ARIMA + LSTM + ensemble creation

#### **4-Step Process**
```bash
# Step 1: Train ARIMA models (30 min)
train_all_arima_models.bat

# Step 2: Train LSTM models (15 min)
train_all_lstm_models.bat

# Step 3: Create ensemble (5 min)
python compare_all_models.py --output-dir ensemble_results

# Step 4: Validate ensemble
python test_lstm_arima_ensemble.py
```

#### **Optimal Weighting**
- LSTM Weight: 50.54% (slightly higher due to superior performance)
- ARIMA Weight: 49.46% (significant contribution)
- Combination Method: Inverse error weighting

#### **Expected Results**
- M5: R² = 0.9986, RMSE = 581, Training Time = 0 min (uses existing models)
- M15: R² = 0.9965, Training Time = 0 min
- M30: R² = 0.9938, Training Time = 0 min
- H1: R² = 0.9868, Training Time = 0 min
- H4: R² = 0.9486, Training Time = 0 min

### **5. ARIMA+TFT Hybrid Replication (R² = 0.624+)**

#### **Training Scripts Available**
- **Hybrid Training**: `train_tft_arima_single.py`
- **Windows Batch**: `train_all_arima_tft_ensemble.bat`

#### **Hybrid Configuration**
```python
HYBRID_CONFIG = {
    "arima_window": 10000,       # Data window for ARIMA training
    "hidden_size": 64,           # TFT hidden dimension
    "attention_head_size": 4,    # Multi-head attention
    "dropout_rate": 0.1,         # Regularization
    "epochs": 5,                 # Early stopping target
    "batch_size": 32,            # Optimal for GPU
}
```

#### **Replication Commands**
```bash
# TFT+ARIMA hybrid (18% improvement over pure TFT)
python train_tft_arima_single.py --timeframe M5 --hidden-size 64 --attention-head-size 4 --dropout-rate 0.1 --learning-rate 0.001 --epochs 5 --batch-size 32 --arima-window 10000

# Windows batch
train_all_arima_tft_ensemble.bat
```

#### **Expected Results**
- M5: R² = 0.624, RMSE = 9,616, Training Time = 4 min
- 18% improvement over pure TFT
- ARIMA features successfully integrated

## Validation and Success Criteria

### **Performance Thresholds**

| Model Type | M5 Threshold | M15 Threshold | M30 Threshold | H1 Threshold | H4 Threshold |
|------------|--------------|---------------|---------------|--------------|--------------|
| **LSTM** | R² > 0.999 | R² > 0.999 | R² > 0.999 | R² > 0.998 | R² > 0.995 |
| **ARIMA+LSTM** | R² > 0.998 | R² > 0.996 | R² > 0.993 | R² > 0.986 | R² > 0.948 |
| **ARIMA** | R² > 0.975 | R² > 0.970 | R² > 0.965 | R² > 0.940 | R² > 0.915 |
| **ARIMA+TFT** | R² > 0.620 | R² > 0.615 | R² > 0.610 | R² > 0.605 | R² > 0.595 |
| **TFT** | R² > 0.520 | R² > 0.515 | R² > 0.505 | R² > 0.495 | R² > 0.475 |

### **Validation Commands**
```bash
# Test ensemble performance
python test_lstm_arima_ensemble.py

# Compare all models
python compare_all_models.py --output-dir validation_results

# Check model loading
python -c "from models.pytorch_lstm_model import LSTMModel; print('LSTM OK')"
python -c "from models.ensemble_arima_model import EnsembleARIMAModel; print('ARIMA OK')"
```

### **Troubleshooting Common Issues**

#### **GPU Memory Errors**
```bash
# Reduce batch size
python train_lstm_single.py --timeframe M5 --batch-size 16
python train_tft_pytorch.py --timeframe M5 --batch-size 16
```

#### **CUDA Compatibility Issues**
```bash
# Check CUDA availability
python -c "import torch; print(f'CUDA Available: {torch.cuda.is_available()}')"

# Verify PyTorch version
python -c "import torch; print(f'PyTorch Version: {torch.__version__}')"
```

#### **Missing Dependencies**
```bash
# Install core dependencies
pip install torch==2.6.0+cu118 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install pmdarima==2.0.3 statsmodels==0.14.0
pip install numpy==1.24.3 pandas==2.0.3 scikit-learn==1.3.0
```

## Quick Start Workflows

### **For Production Deployment**
```bash
# Step 1: Establish baseline (15 min)
train_all_lstm_models.bat

# Step 2: Create ensemble (45 min)
train_all_arima_lstm_ensemble.bat

# Total: 60 minutes for R² = 0.998+ performance
```

### **For Research & Development**
```bash
# Complete model comparison (80 min)
train_all_lstm_models.bat
train_all_tft_models.bat
train_all_arima_lstm_ensemble.bat
train_all_arima_tft_ensemble.bat
```

### **For Quick Testing**
```bash
# Single best performer (15 min)
train_all_lstm_models.bat
```

This replication guide ensures consistent, optimal performance across all model types and timeframes, with clear commands, expected results, and validation procedures based on the real current codebase.
