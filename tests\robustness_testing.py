"""
Module for robustness testing of models.
"""
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import TimeSeriesSplit
import optuna
from optuna.visualization import plot_optimization_history, plot_param_importances
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import torch
import GPUtil
import psutil
import logging

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tests.models.base_test import BaseModelTest
from config.unified_config import get_all_model_configs
model_config = get_all_model_configs()

class RobustnessTester:
    """Framework for testing model robustness."""

    def __init__(self, model_name: str, data_path: Path):
        """
        Initialize robustness testing framework.

        Args:
            model_name: Name of the model to test
            data_path: Path to the data file
        """
        self.model_name = model_name
        self.data_path = data_path
        self.results = {}

        # Set up logging
        self.logger = logging.getLogger('robustness_testing')
        self.logger.setLevel(logging.INFO)

        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = Path("test_results") / "robustness" / model_name / timestamp
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Add file handler
        log_file = self.output_dir / "robustness_test.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(file_handler)

        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(console_handler)

    def test_market_conditions(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Test model performance under different market conditions."""
        try:
            results = {}

            # Define market conditions
            conditions = {
                'trending_up': data['close'].pct_change().rolling(20).mean() > 0,
                'trending_down': data['close'].pct_change().rolling(20).mean() < 0,
                'high_volatility': data['close'].pct_change().rolling(20).std() > data['close'].pct_change().rolling(20).std().mean(),
                'low_volatility': data['close'].pct_change().rolling(20).std() < data['close'].pct_change().rolling(20).std().mean(),
            }

            # Test each condition
            for condition_name, condition_mask in conditions.items():
                self.logger.info(f"Testing {condition_name} market condition...")

                # Filter data for condition
                condition_data = data[condition_mask]

                if len(condition_data) > 0:
                    # Preprocess data
                    X, y = self._preprocess_data(condition_data)

                    # Initialize model
                    model = self._get_model_instance()

                    # Train and evaluate
                    metrics = self._train_and_evaluate(model, X, y)
                    results[condition_name] = metrics

                    # Plot results
                    self._plot_condition_results(condition_data, metrics, condition_name)

            return results

        except Exception as e:
            self.logger.error(f"Error in market conditions testing: {str(e)}")
            raise

    def test_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Test model robustness to data quality issues."""
        try:
            results = {}

            # Define data quality issues
            issues = {
                'missing_data': self._simulate_missing_data(data),
                'noisy_data': self._simulate_noisy_data(data),
                'outliers': self._simulate_outliers(data),
            }

            # Test each issue
            for issue_name, issue_data in issues.items():
                self.logger.info(f"Testing {issue_name}...")

                # Preprocess data
                X, y = self._preprocess_data(issue_data)

                # Initialize model
                model = self._get_model_instance()

                # Train and evaluate
                metrics = self._train_and_evaluate(model, X, y)
                results[issue_name] = metrics

                # Plot results
                self._plot_issue_results(issue_data, metrics, issue_name)

            return results

        except Exception as e:
            self.logger.error(f"Error in data quality testing: {str(e)}")
            raise

    def test_adversarial(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Test model robustness to adversarial examples."""
        try:
            results = {}

            # Define adversarial attacks
            attacks = {
                'fgsm': self._fgsm_attack,
                'pgd': self._pgd_attack,
            }

            # Test each attack
            for attack_name, attack_fn in attacks.items():
                self.logger.info(f"Testing {attack_name} adversarial attack...")

                # Generate adversarial examples
                X, y = self._preprocess_data(data)
                X_adv = attack_fn(X, y)

                # Initialize model
                model = self._get_model_instance()

                # Train on clean data
                model.train(X, y, X, y)

                # Evaluate on adversarial examples
                y_pred = model.predict(X_adv)
                metrics = self._calculate_metrics(y, y_pred)
                results[attack_name] = metrics

                # Plot results
                self._plot_adversarial_results(X, X_adv, y, y_pred, attack_name)

            return results

        except Exception as e:
            self.logger.error(f"Error in adversarial testing: {str(e)}")
            raise

    def _simulate_missing_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Simulate missing data by randomly dropping values."""
        missing_data = data.copy()
        missing_rate = 0.1  # 10% missing data
        mask = np.random.random(missing_data.shape) < missing_rate
        missing_data[mask] = np.nan
        return missing_data

    def _simulate_noisy_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Simulate noisy data by adding random noise."""
        noisy_data = data.copy()
        noise_std = data.std() * 0.1  # 10% of standard deviation
        noise = np.random.normal(0, noise_std, noisy_data.shape)
        noisy_data += noise
        return noisy_data

    def _simulate_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """Simulate outliers by adding extreme values."""
        outlier_data = data.copy()
        outlier_rate = 0.05  # 5% outliers
        mask = np.random.random(outlier_data.shape) < outlier_rate
        outlier_data[mask] *= 2  # Double the value
        return outlier_data

    def _fgsm_attack(self, X: np.ndarray, y: np.ndarray, epsilon: float = 0.1) -> np.ndarray:
        """Fast Gradient Sign Method attack."""
        X_tensor = torch.tensor(X, requires_grad=True)
        y_tensor = torch.tensor(y)

        # Forward pass
        model = self._get_model_instance()
        output = model(X_tensor)
        loss = torch.nn.functional.mse_loss(output, y_tensor)

        # Backward pass
        loss.backward()

        # Generate adversarial example
        X_adv = X_tensor + epsilon * X_tensor.grad.sign()
        return X_adv.detach().numpy()

    def _pgd_attack(self, X: np.ndarray, y: np.ndarray, epsilon: float = 0.1,
                   alpha: float = 0.01, num_iter: int = 10) -> np.ndarray:
        """Projected Gradient Descent attack."""
        X_adv = X.copy()

        for _ in range(num_iter):
            X_adv = self._fgsm_attack(X_adv, y, alpha)
            X_adv = np.clip(X_adv, X - epsilon, X + epsilon)

        return X_adv

    def _get_model_instance(self):
        """Get model instance."""
        if self.model_name == 'lstm':
            from models.pytorch_lstm_model import LSTMModel
            model = LSTMModel(model_config)
        elif self.model_name == 'tft':
            from models.tft_model import TFTModel
            model = TFTModel(model_config)
        elif self.model_name == 'arima':
            from utils.arima_trainer import ARIMAModel
            model = ARIMAModel(model_config)
        else:
            raise ValueError(f"Unsupported model type: {self.model_name}")

        model.build()
        return model

    def _preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Preprocess data for model training."""
        # Implement data preprocessing logic
        # This should match your existing preprocessing pipeline
        pass

    def _train_and_evaluate(self, model, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """Train and evaluate model."""
        # Split data
        tscv = TimeSeriesSplit(n_splits=5)
        metrics = []

        for train_idx, test_idx in tscv.split(X):
            X_train, X_test = X[train_idx], X[test_idx]
            y_train, y_test = y[train_idx], y[test_idx]

            # Train model
            model.train(X_train, y_train, X_test, y_test)

            # Make predictions
            y_pred = model.predict(X_test)

            # Calculate metrics
            fold_metrics = self._calculate_metrics(y_test, y_pred)
            metrics.append(fold_metrics)

        # Return average metrics
        return pd.DataFrame(metrics).mean().to_dict()

    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate evaluation metrics."""
        metrics = {
            'mae': mean_absolute_error(y_true, y_pred),
            'mse': mean_squared_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
            'r2': r2_score(y_true, y_pred)
        }
        return metrics

    def _plot_condition_results(self, data: pd.DataFrame, metrics: Dict[str, float], condition: str):
        """Plot results for market condition."""
        try:
            # Create interactive plot
            fig = make_subplots(rows=2, cols=1)

            # Plot price data
            fig.add_trace(
                go.Scatter(y=data['close'], name='Price', line=dict(color='blue')),
                row=1, col=1
            )

            # Plot metrics
            fig.add_trace(
                go.Bar(
                    x=list(metrics.keys()),
                    y=list(metrics.values()),
                    name='Metrics',
                    text=[f"{v:.4f}" for v in metrics.values()],
                    textposition='auto',
                ),
                row=2, col=1
            )

            fig.update_layout(
                title=f"Market Condition: {condition}",
                height=800
            )

            # Save plot
            fig.write_html(str(self.output_dir / f"{condition}_results.html"))

        except Exception as e:
            self.logger.warning(f"Error plotting condition results: {str(e)}")

    def _plot_issue_results(self, data: pd.DataFrame, metrics: Dict[str, float], issue: str):
        """Plot results for data quality issue."""
        try:
            # Create interactive plot
            fig = make_subplots(rows=2, cols=1)

            # Plot data with issue
            fig.add_trace(
                go.Scatter(y=data['close'], name='Price', line=dict(color='red')),
                row=1, col=1
            )

            # Plot metrics
            fig.add_trace(
                go.Bar(
                    x=list(metrics.keys()),
                    y=list(metrics.values()),
                    name='Metrics',
                    text=[f"{v:.4f}" for v in metrics.values()],
                    textposition='auto',
                ),
                row=2, col=1
            )

            fig.update_layout(
                title=f"Data Quality Issue: {issue}",
                height=800
            )

            # Save plot
            fig.write_html(str(self.output_dir / f"{issue}_results.html"))

        except Exception as e:
            self.logger.warning(f"Error plotting issue results: {str(e)}")

    def _plot_adversarial_results(self, X: np.ndarray, X_adv: np.ndarray, y: np.ndarray,
                                y_pred: np.ndarray, attack: str):
        """Plot results for adversarial attack."""
        try:
            # Create interactive plot
            fig = make_subplots(rows=3, cols=1)

            # Plot original data
            fig.add_trace(
                go.Scatter(y=X[:, 0], name='Original', line=dict(color='blue')),
                row=1, col=1
            )

            # Plot adversarial data
            fig.add_trace(
                go.Scatter(y=X_adv[:, 0], name='Adversarial', line=dict(color='red')),
                row=2, col=1
            )

            # Plot predictions
            fig.add_trace(
                go.Scatter(y=y, name='True', line=dict(color='blue')),
                row=3, col=1
            )
            fig.add_trace(
                go.Scatter(y=y_pred, name='Predicted', line=dict(color='red')),
                row=3, col=1
            )

            fig.update_layout(
                title=f"Adversarial Attack: {attack}",
                height=1000
            )

            # Save plot
            fig.write_html(str(self.output_dir / f"{attack}_results.html"))

        except Exception as e:
            self.logger.warning(f"Error plotting adversarial results: {str(e)}")