"""
Model Adapter

This module provides adapter classes to handle input shape mismatches between
the data provided by the trading bot and the expected input shapes of the models.
"""

import logging
import numpy as np

logger = logging.getLogger(__name__)

class ModelInputAdapter:
    """Adapter class to transform input data to match model expectations."""

    @staticmethod
    def adapt_input(X, target_shape):
        """
        Adapt input data to match the target shape expected by the model.

        Args:
            X: Input data with shape (batch_size, seq_len, features)
            target_shape: Tuple of (seq_len, features) expected by the model

        Returns:
            Adapted input data with shape (batch_size, target_seq_len, target_features)
        """
        if X is None:
            logger.error("Input data is None")
            return None

        # Get current shape
        if not isinstance(X, np.ndarray):
            try:
                X = np.array(X)
            except Exception as e:
                logger.error(f"Failed to convert input to numpy array: {str(e)}")
                return None

        # Handle different input formats
        if len(X.shape) == 2:  # (batch_size, features)
            batch_size, features = X.shape
            # Reshape to 3D by adding sequence dimension
            X = X.reshape(batch_size, 1, features)

        if len(X.shape) != 3:
            logger.error(f"Expected 3D input, got shape {X.shape}")
            return None

        batch_size, seq_len, features = X.shape
        target_seq_len, target_features = target_shape

        logger.info(f"Adapting input from shape {X.shape} to (batch_size, {target_seq_len}, {target_features})")

        # Handle sequence length mismatch
        if seq_len > target_seq_len:
            # Truncate sequence
            X = X[:, -target_seq_len:, :]
        elif seq_len < target_seq_len:
            # Pad sequence with zeros
            padding = np.zeros((batch_size, target_seq_len - seq_len, features))
            X = np.concatenate([padding, X], axis=1)

        # After handling sequence length, get the new sequence length
        _, seq_len, _ = X.shape

        # Handle feature dimension mismatch
        if features > target_features:
            # Select subset of features
            X = X[:, :, :target_features]
        elif features < target_features:
            # Pad features with zeros
            padding = np.zeros((batch_size, seq_len, target_features - features))
            X = np.concatenate([X, padding], axis=2)

        return X
