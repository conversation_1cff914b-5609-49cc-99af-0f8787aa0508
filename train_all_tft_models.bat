@echo off
REM ============================================================================
REM TFT Model Training Batch Script - Optimal Performance Configuration
REM ============================================================================
REM Target Performance: R² = 0.529+ (52.9% accuracy)
REM Expected Training Time: ~15 minutes total
REM Hardware Requirements: NVIDIA GPU with 8GB+ VRAM, 16GB+ RAM
REM ============================================================================

echo.
echo ============================================================================
echo                     TFT MODEL TRAINING - ALL TIMEFRAMES
echo ============================================================================
echo Target Performance: R² = 0.529+ (Modern Deep Learning)
echo Training Method: Temporal Fusion Transformer with GPU acceleration
echo Timeframes: M5, M15, M30, H1, H4
echo ============================================================================
echo.

REM Create necessary directories
if not exist "models" mkdir models
if not exist "logs" mkdir logs
if not exist "metrics" mkdir metrics
if not exist "plots" mkdir plots

REM Set timeframes and optimal parameters
set TIMEFRAMES=M5 M15 M30 H1 H4
set HIDDEN_DIM=64
set NUM_HEADS=4
set NUM_LAYERS=2
set DROPOUT_RATE=0.1
set LEARNING_RATE=0.001
set EPOCHS=5
set BATCH_SIZE=32

REM Initialize counters
set /A TOTAL_COUNT=0
set /A SUCCESS_COUNT=0
set /A FAILED_COUNT=0

REM Record start time
echo Training started at %date% %time%
echo.

echo ============================================================================
echo                        OPTIMAL TFT CONFIGURATION
echo ============================================================================
echo Hidden Dimension: %HIDDEN_DIM%
echo Attention Heads: %NUM_HEADS%
echo Transformer Layers: %NUM_LAYERS%
echo Dropout Rate: %DROPOUT_RATE%
echo Learning Rate: %LEARNING_RATE%
echo Epochs: %EPOCHS% (with early stopping)
echo Batch Size: %BATCH_SIZE%
echo ============================================================================
echo.

REM Train TFT models for all timeframes
for %%t in (%TIMEFRAMES%) do (
    set /A TOTAL_COUNT+=1
    echo ===================================================
    echo Training TFT model for %%t timeframe...
    echo ===================================================
    echo Command: python train_tft_pytorch.py --timeframe %%t --hidden-dim %HIDDEN_DIM% --num-heads %NUM_HEADS% --num-layers %NUM_LAYERS% --dropout-rate %DROPOUT_RATE% --learning-rate %LEARNING_RATE% --epochs %EPOCHS% --batch-size %BATCH_SIZE%
    echo.
    echo Expected Results for %%t:
    if "%%t"=="M5" (
        echo   R^2 ~= 0.529+, RMSE ~= 10768+, Training Time ~= 3 min
    ) else if "%%t"=="M15" (
        echo   R^2 ~= 0.520+, RMSE ~= 11000+, Training Time ~= 3 min
    ) else if "%%t"=="M30" (
        echo   R^2 ~= 0.510+, RMSE ~= 12000+, Training Time ~= 3 min
    ) else if "%%t"=="H1" (
        echo   R^2 ~= 0.500+, RMSE ~= 13000+, Training Time ~= 3 min
    ) else if "%%t"=="H4" (
        echo   R^2 ~= 0.480+, RMSE ~= 15000+, Training Time ~= 3 min
    )
    echo.

    REM Run the Python script with optimal parameters
    python train_tft_pytorch.py --timeframe %%t --hidden-dim %HIDDEN_DIM% --num-heads %NUM_HEADS% --num-layers %NUM_LAYERS% --dropout-rate %DROPOUT_RATE% --learning-rate %LEARNING_RATE% --epochs %EPOCHS% --batch-size %BATCH_SIZE%
    set LAST_ERROR=!ERRORLEVEL!

    if !LAST_ERROR! EQU 0 (
        echo [SUCCESS] TFT model for %%t trained successfully
        set /A SUCCESS_COUNT+=1
    ) else (
        echo [FAILED] TFT model training failed for %%t
        echo [INFO] Trying alternative TFT implementation...
        echo.

        REM Fallback to PyTorch Forecasting implementation
        python train_tft_single.py --timeframe %%t --hidden-size %HIDDEN_DIM% --attention-head-size %NUM_HEADS% --dropout-rate %DROPOUT_RATE% --learning-rate %LEARNING_RATE% --epochs 10 --batch-size %BATCH_SIZE%
        set FALLBACK_ERROR=!ERRORLEVEL!

        if !FALLBACK_ERROR! EQU 0 (
            echo [SUCCESS] TFT model for %%t trained with fallback implementation
            set /A SUCCESS_COUNT+=1
        ) else (
            echo [FAILED] Both TFT implementations failed for %%t
            set /A FAILED_COUNT+=1
        )
    )
    echo.
)

REM ============================================================================
REM TRAINING SUMMARY AND VALIDATION
REM ============================================================================
echo.
echo ============================================================================
echo                           TRAINING SUMMARY
echo ============================================================================
echo Total models trained: %TOTAL_COUNT%
echo Successful: %SUCCESS_COUNT%
echo Failed: %FAILED_COUNT%
echo Training completed at %date% %time%
echo ============================================================================
echo.

if %SUCCESS_COUNT% GTR 0 (
    echo ============================================================================
    echo                        PERFORMANCE VALIDATION
    echo ============================================================================
    echo Running validation test...
    echo.

    REM Test TFT model loading and basic functionality
    python validate_tft_models.py

    set VALIDATION_ERROR=%ERRORLEVEL%

    if %VALIDATION_ERROR% EQU 0 (
        echo.
        echo [SUCCESS] TFT MODEL TRAINING COMPLETED SUCCESSFULLY!
        echo.
        echo [INFO] Expected Performance Metrics:
        echo    * M5:  R^2 ~= 0.529+ (52.9%% accuracy)
        echo    * M15: R^2 ~= 0.520+ (52.0%% accuracy)
        echo    * M30: R^2 ~= 0.510+ (51.0%% accuracy)
        echo    * H1:  R^2 ~= 0.500+ (50.0%% accuracy)
        echo    * H4:  R^2 ~= 0.480+ (48.0%% accuracy)
        echo.
        echo [NEXT] Next Steps:
        echo    1. Run: python compare_all_models.py --output-dir tft_results
        echo    2. Compare with LSTM performance
        echo    3. Consider TFT+ARIMA hybrid for better results
        echo.
        echo [NOTE] TFT models are experimental and show moderate performance.
        echo    For production use, consider LSTM models (R^2 = 0.999+) instead.
        echo.
    ) else (
        echo [ERROR] VALIDATION FAILED - Please check error messages above
    )
) else (
    echo [ERROR] NO MODELS TRAINED SUCCESSFULLY
    echo.
    echo [HELP] Troubleshooting Steps:
    echo    1. Check GPU availability: python -c "import torch; print(torch.cuda.is_available())"
    echo    2. Verify PyTorch Lightning installation: pip install pytorch-lightning
    echo    3. Check data files exist in: data/historical/btcusd.a/
    echo    4. Try reducing batch size: --batch-size 16
    echo    5. Review error messages above
    echo.
    echo [TIP] Alternative: Try LSTM models for better performance
    echo    Run: train_all_lstm_models.bat
    echo.
)

echo ============================================================================
echo TFT Model Training Script Completed
echo ============================================================================
pause
