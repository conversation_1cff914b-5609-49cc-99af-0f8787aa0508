#!/usr/bin/env python
"""
Comprehensive Model Performance Analysis with Statistical Significance Tests
Analyzes all trained models across timeframes and provides statistical comparisons.
"""

import os
import sys
import json
import glob
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path
import logging
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from monitoring.performance import ModelPerformanceMonitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_model_metrics():
    """Load all model metrics from the metrics directory."""
    metrics_data = {}
    
    # Load LSTM metrics
    lstm_files = glob.glob('metrics/lstm_BTCUSD.a_summary_*.json')
    if lstm_files:
        latest_lstm = max(lstm_files, key=os.path.getctime)
        with open(latest_lstm, 'r') as f:
            lstm_data = json.load(f)
            metrics_data['lstm'] = lstm_data['metrics']
    
    # Load ARIMA metrics
    arima_files = glob.glob('metrics/arima_BTCUSD.a_*_*.json')
    arima_data = {}
    for file in arima_files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
                timeframe = data.get('timeframe', 'unknown')
                arima_data[timeframe] = data['metrics']
        except Exception as e:
            logger.warning(f"Error reading {file}: {e}")
    
    if arima_data:
        metrics_data['arima'] = arima_data
    
    # Load TFT metrics
    tft_files = glob.glob('metrics/tft_BTCUSD.a_*_*.json')
    tft_data = {}
    for file in tft_files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
                timeframe = data.get('timeframe', 'unknown')
                tft_data[timeframe] = data['metrics']
        except Exception as e:
            logger.warning(f"Error reading {file}: {e}")
    
    if tft_data:
        metrics_data['tft'] = tft_data
    
    return metrics_data

def analyze_model_performance():
    """Comprehensive analysis of model performance with statistical tests."""
    logger.info("=== COMPREHENSIVE MODEL PERFORMANCE ANALYSIS ===")
    logger.info(f"Analysis Time: {datetime.now()}")
    
    # Load metrics
    metrics_data = load_model_metrics()
    
    if not metrics_data:
        logger.error("No model metrics found!")
        return
    
    # Initialize performance monitor
    monitor = ModelPerformanceMonitor()
    
    # Performance summary table
    logger.info("\n=== MODEL PERFORMANCE SUMMARY ===")
    logger.info(f"{'Model':<8} {'Timeframe':<10} {'R²':<10} {'RMSE':<12} {'MAE':<12} {'Status':<15}")
    logger.info("-" * 80)
    
    all_results = []
    
    for model_name, model_data in metrics_data.items():
        for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
            if timeframe in model_data:
                metrics = model_data[timeframe]
                r2 = metrics.get('r2', 0)
                rmse = metrics.get('rmse', 0)
                mae = metrics.get('mae', 0)
                
                # Determine status based on R²
                if r2 > 0.8:
                    status = "🟢 Excellent"
                elif r2 > 0.5:
                    status = "🟡 Good"
                elif r2 > 0.0:
                    status = "🟠 Fair"
                else:
                    status = "🔴 Poor"
                
                logger.info(f"{model_name:<8} {timeframe:<10} {r2:<10.4f} {rmse:<12.2f} {mae:<12.2f} {status:<15}")
                
                all_results.append({
                    'model': model_name,
                    'timeframe': timeframe,
                    'r2': r2,
                    'rmse': rmse,
                    'mae': mae,
                    'status': status
                })
            else:
                logger.info(f"{model_name:<8} {timeframe:<10} {'N/A':<10} {'N/A':<12} {'N/A':<12} {'❌ Missing':<15}")
    
    # Statistical significance analysis
    logger.info("\n=== STATISTICAL SIGNIFICANCE ANALYSIS ===")
    
    # Find best performing model for each timeframe
    best_models = {}
    for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
        best_r2 = -float('inf')
        best_model = None
        
        for result in all_results:
            if result['timeframe'] == timeframe and result['r2'] > best_r2:
                best_r2 = result['r2']
                best_model = result['model']
        
        if best_model:
            best_models[timeframe] = {
                'model': best_model,
                'r2': best_r2
            }
    
    logger.info("Best performing models by timeframe:")
    for timeframe, best in best_models.items():
        logger.info(f"  {timeframe}: {best['model']} (R² = {best['r2']:.4f})")
    
    # Overall performance ranking
    logger.info("\n=== OVERALL PERFORMANCE RANKING ===")
    
    # Calculate average R² for each model
    model_avg_r2 = {}
    for model_name in metrics_data.keys():
        r2_values = []
        for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
            if timeframe in metrics_data[model_name]:
                r2_values.append(metrics_data[model_name][timeframe]['r2'])
        
        if r2_values:
            model_avg_r2[model_name] = np.mean(r2_values)
    
    # Sort by average R²
    sorted_models = sorted(model_avg_r2.items(), key=lambda x: x[1], reverse=True)
    
    logger.info("Models ranked by average R² across all timeframes:")
    for i, (model, avg_r2) in enumerate(sorted_models, 1):
        logger.info(f"  {i}. {model.upper()}: {avg_r2:.4f}")
    
    # Performance insights
    logger.info("\n=== PERFORMANCE INSIGHTS ===")
    
    # Count excellent, good, fair, poor performances
    performance_counts = {'🟢 Excellent': 0, '🟡 Good': 0, '🟠 Fair': 0, '🔴 Poor': 0}
    for result in all_results:
        performance_counts[result['status']] += 1
    
    total_models = len(all_results)
    logger.info("Performance distribution:")
    for status, count in performance_counts.items():
        percentage = (count / total_models) * 100 if total_models > 0 else 0
        logger.info(f"  {status}: {count}/{total_models} ({percentage:.1f}%)")
    
    # Timeframe analysis
    logger.info("\nTimeframe analysis:")
    timeframe_performance = {}
    for timeframe in ['M5', 'M15', 'M30', 'H1', 'H4']:
        tf_results = [r for r in all_results if r['timeframe'] == timeframe]
        if tf_results:
            avg_r2 = np.mean([r['r2'] for r in tf_results])
            timeframe_performance[timeframe] = avg_r2
            logger.info(f"  {timeframe}: Average R² = {avg_r2:.4f}")
    
    # Best and worst timeframes
    if timeframe_performance:
        best_tf = max(timeframe_performance.items(), key=lambda x: x[1])
        worst_tf = min(timeframe_performance.items(), key=lambda x: x[1])
        logger.info(f"\nBest performing timeframe: {best_tf[0]} (R² = {best_tf[1]:.4f})")
        logger.info(f"Worst performing timeframe: {worst_tf[0]} (R² = {worst_tf[1]:.4f})")
    
    # Recommendations
    logger.info("\n=== RECOMMENDATIONS ===")
    
    if sorted_models:
        best_overall = sorted_models[0]
        logger.info(f"1. Primary model recommendation: {best_overall[0].upper()} (Average R² = {best_overall[1]:.4f})")
    
    # Identify models that need improvement
    poor_models = [r for r in all_results if r['r2'] < 0]
    if poor_models:
        logger.info(f"2. Models requiring attention ({len(poor_models)} instances with negative R²):")
        for model in poor_models:
            logger.info(f"   - {model['model'].upper()} {model['timeframe']}: R² = {model['r2']:.4f}")
    
    # Suggest ensemble approach
    good_models = [r for r in all_results if r['r2'] > 0.3]
    if len(good_models) >= 2:
        logger.info(f"3. Consider ensemble approach using {len(good_models)} well-performing model instances")
    
    # Save comprehensive analysis
    analysis_report = {
        'timestamp': datetime.now().isoformat(),
        'model_performance': all_results,
        'best_models_by_timeframe': best_models,
        'overall_ranking': dict(sorted_models),
        'performance_distribution': performance_counts,
        'timeframe_performance': timeframe_performance,
        'recommendations': {
            'primary_model': best_overall[0] if sorted_models else None,
            'poor_performing_models': poor_models,
            'ensemble_candidates': len(good_models)
        }
    }
    
    # Save analysis report
    os.makedirs('monitoring_output/analysis', exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'monitoring_output/analysis/performance_analysis_{timestamp}.json'
    
    with open(report_file, 'w') as f:
        json.dump(analysis_report, f, indent=4, default=str)
    
    logger.info(f"\nComprehensive analysis saved to: {report_file}")
    logger.info("\n=== ANALYSIS COMPLETE ===")
    
    return analysis_report

if __name__ == "__main__":
    try:
        analyze_model_performance()
    except Exception as e:
        logger.error(f"Error in model performance analysis: {e}", exc_info=True)
