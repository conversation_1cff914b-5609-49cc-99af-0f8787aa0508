"""
Test script for the enhanced error handler.
"""
import os
import sys
import logging
import time
import random
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_error_handler():
    """Test the enhanced error handler."""
    try:
        from utils.enhanced_error_handler import (
            enhanced_error_handler,
            handle_error,
            with_error_handling,
            with_circuit_breaker,
            with_retry,
            ErrorCategory,
            ErrorSeverity
        )

        logger.info("Testing enhanced error handler")

        # Test basic error handling
        try:
            raise ValueError("Test error")
        except Exception as e:
            error_info = handle_error(
                exception=e,
                context={"test": "context"},
                source="test_error_handler",
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA
            )
            logger.info(f"Error handled: {error_info.error_id}")

        # Test with_error_handling decorator
        @with_error_handling
        def function_with_error():
            raise RuntimeError("Test error in function")

        # Register a recovery handler for RuntimeError
        def runtime_error_handler(exception, context):
            logger.info(f"Runtime error handler called for: {str(exception)}")
            return True

        enhanced_error_handler.register_recovery_handler(RuntimeError, runtime_error_handler)

        # Now the function should recover
        try:
            function_with_error()
            logger.info("Function recovered successfully")
        except Exception as e:
            logger.info(f"Error caught from function_with_error: {str(e)}")

        # Test with_retry decorator
        @with_retry(max_retries=3, retry_delay=0.1)
        def function_with_retry():
            if random.random() < 0.8:  # 80% chance of failure
                raise ConnectionError("Random connection error")
            return "Success"

        try:
            result = function_with_retry()
            logger.info(f"Retry result: {result}")
        except Exception as e:
            logger.info(f"Retry failed: {str(e)}")

        # Test circuit breaker
        circuit_name = "test_circuit"

        def circuit_protected_function(should_fail=True):
            if should_fail:
                raise TimeoutError("Test timeout error")
            return "Success"

        # Trigger circuit breaker to open
        for i in range(6):  # Should open after 5 failures
            try:
                with_circuit_breaker(circuit_name, circuit_protected_function)
            except Exception as e:
                logger.info(f"Circuit breaker test {i+1}: {str(e)}")

        # Test error recovery
        def custom_recovery_handler(exception, context):
            logger.info(f"Custom recovery handler called for: {str(exception)}")
            return True

        enhanced_error_handler.register_category_recovery_handler(
            ErrorCategory.DATA,
            custom_recovery_handler
        )

        try:
            raise ValueError("Test error for recovery")
        except Exception as e:
            error_info = handle_error(
                exception=e,
                context={"test": "recovery"},
                source="test_error_handler",
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA
            )
            logger.info(f"Recovery attempted: {error_info.recovery_attempted}, succeeded: {error_info.recovery_succeeded}")

        # Test error statistics
        stats = enhanced_error_handler.get_error_stats()
        logger.info(f"Error stats: {stats}")

        # Test recent errors
        recent_errors = enhanced_error_handler.get_recent_errors(limit=5)
        logger.info(f"Recent errors: {len(recent_errors)}")

        return True

    except Exception as e:
        logger.error(f"Error testing enhanced error handler: {str(e)}")
        return False

if __name__ == "__main__":
    if test_error_handler():
        logger.info("Enhanced error handler test passed")
        sys.exit(0)
    else:
        logger.error("Enhanced error handler test failed")
        sys.exit(1)
