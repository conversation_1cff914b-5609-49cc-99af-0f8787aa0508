"""
Test script for the unified configuration manager.
"""
import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_unified_config():
    """Test the unified configuration manager."""
    try:
        from config.unified_config import UnifiedConfigManager
        
        # Initialize configuration manager
        config_manager = UnifiedConfigManager()
        
        # Validate configuration
        if not config_manager.validate():
            logger.error("Configuration validation failed")
            return False
        
        # Get configuration
        config = config_manager.get_config()
        
        # Print configuration details
        logger.info("Configuration loaded successfully")
        logger.info(f"MT5 Terminals: {len(config.mt5.terminals)}")
        logger.info(f"Strategy Symbol: {config.strategy.symbol}")
        logger.info(f"Models: {list(config.models.keys())}")
        logger.info(f"Data Base Path: {config.data_base_path}")
        logger.info(f"Models Base Path: {config.models_base_path}")
        
        # Create example configuration
        if config_manager.create_example_config():
            logger.info("Example configuration created successfully")
        else:
            logger.error("Failed to create example configuration")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing unified configuration: {str(e)}")
        return False

if __name__ == "__main__":
    if test_unified_config():
        logger.info("Unified configuration test passed")
        sys.exit(0)
    else:
        logger.error("Unified configuration test failed")
        sys.exit(1)
