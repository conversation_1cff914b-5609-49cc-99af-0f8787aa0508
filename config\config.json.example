{"mt5": {"max_connections": 5, "timeout": 60000, "retry_interval": 5, "terminals": {"1": {"login": "YOUR_LOGIN", "password": "YOUR_PASSWORD", "server": "YOUR_SERVER", "path": "C:/Path/To/MT5/terminal64.exe", "trade_mode": true, "auto_trading": true}}}, "strategy": {"symbol": "BTCUSD.a", "timeframes": ["M5", "M15", "M30"], "sequence_length": 288, "lot_size": 0.01, "max_positions": 2, "stop_loss_pips": 200, "take_profit_pips": 400, "max_spread_pips": 50, "risk_per_trade": 0.01, "max_daily_loss": 50.0, "max_daily_trades": 5, "cooldown_period": 600, "volatility_threshold": 2.0, "trend_threshold": 0.7, "position_sizing_factor": 0.5}, "models": {"lstm": {"model_path": "lstm_model.h5", "input_dim": 50, "output_dim": 1, "weight": 0.2, "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "FEATURE_COLUMNS": ["open", "high", "low", "close", "real_volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "gru": {"model_path": "gru_model.h5", "input_dim": 50, "output_dim": 1, "weight": 0.2, "sequence_length": 100, "batch_size": 64, "epochs": 100, "patience": 10, "learning_rate": 0.001, "dropout_rate": 0.2, "hidden_units": 64, "num_layers": 2, "dense_units": 32, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "xgboost": {"model_path": "xgboost_model.json", "input_dim": 50, "output_dim": 1, "weight": 0.2, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "gamma": 0, "learning_rate": 0.01, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}, "lightgbm": {"model_path": "lightgbm_model.txt", "input_dim": 50, "output_dim": 1, "weight": 0.2, "max_depth": 6, "n_estimators": 100, "subsample": 0.8, "colsample_bytree": 0.8, "min_child_weight": 1, "num_leaves": 31, "learning_rate": 0.01, "FEATURE_COLUMNS": ["open", "high", "low", "close", "volume", "sma_20", "sma_50", "rsi_14", "macd", "macd_signal", "bb_upper", "bb_middle", "bb_lower", "atr_14"]}}, "data_base_path": "data/", "models_base_path": "models/", "confidence_threshold": 0.65, "update_interval": 60, "max_memory_usage": 85.0, "log_level": "INFO", "debug_mode": false}