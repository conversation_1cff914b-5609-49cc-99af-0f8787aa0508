@echo off
echo ===================================================
echo Legacy Files Cleanup Script
echo ===================================================
echo This script will help you clean up legacy GPU setup and training files.
echo.

echo The following files are considered legacy and can be safely removed:
echo.
echo GPU Setup Scripts:
echo - fix_tensorflow_cuda.bat
echo - fix_tensorflow_2_19_gpu.bat
echo - fix_tensorflow_2_19_gpu_complete.bat
echo - setup_tensorflow_gpu.bat
echo.
echo Training Scripts:
echo - train_with_tensorflow.bat
echo - train_with_pytorch.bat
echo - train_tf_neural_models.bat
echo - train_tf_tree_models.bat
echo.
echo Test Scripts:
echo - test_tensorflow_gpu.bat
echo - test_tensorflow_gpu.py
echo.

choice /C YN /M "Do you want to remove these legacy files"
if errorlevel 2 goto end
if errorlevel 1 goto remove_files

:remove_files
echo.
echo Removing legacy files...

REM GPU Setup Scripts
if exist fix_tensorflow_cuda.bat del fix_tensorflow_cuda.bat
if exist fix_tensorflow_2_19_gpu.bat del fix_tensorflow_2_19_gpu.bat
if exist fix_tensorflow_2_19_gpu_complete.bat del fix_tensorflow_2_19_gpu_complete.bat
if exist setup_tensorflow_gpu.bat del setup_tensorflow_gpu.bat

REM Training Scripts
if exist train_with_tensorflow.bat del train_with_tensorflow.bat
if exist train_with_pytorch.bat del train_with_pytorch.bat
if exist train_tf_neural_models.bat del train_tf_neural_models.bat
if exist train_tf_tree_models.bat del train_tf_tree_models.bat

REM Test Scripts
if exist test_tensorflow_gpu.bat del test_tensorflow_gpu.bat
if exist test_tensorflow_gpu.py del test_tensorflow_gpu.py

echo.
echo Legacy files have been removed.
echo.
echo The following new files are now available:
echo.
echo GPU Setup and Testing:
echo - setup_gpu.bat
echo - test_gpu.bat
echo - test_gpu_support.py
echo.
echo Training Scripts:
echo - train_models.bat
echo - train_all.bat
echo - train_neural_models.bat
echo - train_tree_models.bat
echo.
echo Docker Support:
echo - train_with_docker.bat
echo.
echo Documentation:
echo - README_GPU_SETUP.md
echo - GPU_SETUP_GUIDE.md
echo - DOCKER_SETUP.md
echo.

:end
echo.
echo Script completed.
pause
