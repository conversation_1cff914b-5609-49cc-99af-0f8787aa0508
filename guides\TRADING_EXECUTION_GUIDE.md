# Trading Execution Guide

This guide provides detailed instructions for configuring and running the trading bot system for live trading.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Configuration](#configuration)
   - [MT5 Terminal Configuration](#mt5-terminal-configuration)
   - [Trading Strategy Configuration](#trading-strategy-configuration)
   - [Model Selection Configuration](#model-selection-configuration)
   - [Risk Management Configuration](#risk-management-configuration)
4. [Running the Trading Bot](#running-the-trading-bot)
   - [Standard Trading Bot](#standard-trading-bot)
   - [Independent Trading System](#independent-trading-system)
   - [Testing the Trading System](#testing-the-trading-system)
5. [Monitoring and Management](#monitoring-and-management)
   - [Monitoring Performance](#monitoring-performance)
   - [Managing Open Positions](#managing-open-positions)
   - [Handling Errors](#handling-errors)
6. [Advanced Configuration](#advanced-configuration)
   - [Custom Strategies](#custom-strategies)
   - [Advanced Risk Management](#advanced-risk-management)
   - [Performance Optimization](#performance-optimization)
7. [Troubleshooting](#troubleshooting)

## Overview

The trading bot system executes trades based on predictions from multiple machine learning models. It connects to MetaTrader 5 terminals, generates trading signals, and manages positions according to the configured strategy and risk parameters.

## Prerequisites

Before running the trading bot, ensure you have:

1. **Trained Models**:
   - LSTM models for all timeframes
   - TFT models for all timeframes
   - ARIMA models for all timeframes
   - See the [Model Training Guide](MODEL_TRAINING_GUIDE.md) for details

2. **MetaTrader 5 Setup**:
   - MetaTrader 5 installed and configured
   - Trading accounts with sufficient balance
   - Automated trading enabled
   - DLL imports enabled

3. **System Configuration**:
   - Python environment activated
   - Dependencies installed
   - Configuration files updated

## Configuration

### MT5 Terminal Configuration

Update the MT5 terminal configuration in `config/config.json`:

```json
{
  "mt5": {
    "max_connections": 5,
    "timeout": 60000,
    "retry_interval": 5,
    "terminals": {
      "1": {
        "login": "YOUR_LOGIN_1",
        "password": "YOUR_PASSWORD_1",
        "server": "YOUR_SERVER_1",
        "path": "C:/Path/To/MT5_Terminal_1/terminal64.exe"
      },
      "2": {
        "login": "YOUR_LOGIN_2",
        "password": "YOUR_PASSWORD_2",
        "server": "YOUR_SERVER_2",
        "path": "C:/Path/To/MT5_Terminal_2/terminal64.exe"
      },
      "3": {
        "login": "YOUR_LOGIN_3",
        "password": "YOUR_PASSWORD_3",
        "server": "YOUR_SERVER_3",
        "path": "C:/Path/To/MT5_Terminal_3/terminal64.exe"
      }
    }
  }
}
```

Ensure that:
- The terminal paths are correct
- Login credentials are valid
- Server names are correct
- Each terminal has a unique ID

### Trading Strategy Configuration

Configure the trading strategy in `config/config.json`:

```json
{
  "strategy": {
    "symbol": "BTCUSD.a",
    "timeframes": ["M5", "M15", "M30", "H1", "H4"],
    "sequence_length": 60,
    "prediction_horizon": 1,
    "signal_threshold": 0.6,
    "confirmation_timeframes": ["M5", "M15"],
    "use_ensemble": true,
    "ensemble_method": "weighted_average",
    "rebalance_interval": 86400,
    "trading_hours": {
      "enabled": false,
      "start_hour": 8,
      "end_hour": 20,
      "timezone": "UTC"
    },
    "filters": {
      "volatility": {
        "enabled": true,
        "min_atr": 100,
        "max_atr": 5000,
        "period": 14
      },
      "trend": {
        "enabled": true,
        "indicator": "ema",
        "fast_period": 20,
        "slow_period": 50
      }
    }
  }
}
```

Key parameters:
- `symbol`: Trading symbol (e.g., BTCUSD.a)
- `timeframes`: Timeframes to use for prediction
- `sequence_length`: Number of time steps to use as input
- `prediction_horizon`: Number of time steps to predict ahead
- `signal_threshold`: Threshold for generating trading signals
- `confirmation_timeframes`: Timeframes required for signal confirmation
- `use_ensemble`: Whether to use ensemble of models
- `ensemble_method`: Method for combining model predictions
- `rebalance_interval`: Interval for rebalancing model weights (in seconds)
- `trading_hours`: Optional trading hour restrictions
- `filters`: Additional filters for signal generation

### Model Selection Configuration

Configure model selection in `config/config.json`:

```json
{
  "models": {
    "lstm": {
      "enabled": true,
      "weight": 0.4,
      "model_path": "models/lstm_BTCUSD.a_{timeframe}",
      "params": {
        "hidden_units": 64,
        "num_layers": 2,
        "dropout_rate": 0.2
      }
    },
    "tft": {
      "enabled": true,
      "weight": 0.4,
      "model_path": "models/tft_BTCUSD.a_{timeframe}",
      "params": {
        "hidden_dim": 64,
        "num_heads": 4,
        "num_layers": 2,
        "dropout_rate": 0.1
      }
    },
    "arima": {
      "enabled": true,
      "weight": 0.2,
      "model_path": "models/arima_BTCUSD.a_{timeframe}",
      "params": {
        "auto_arima": true
      }
    }
  }
}
```

Key parameters:
- `enabled`: Whether to use this model type
- `weight`: Weight of this model in the ensemble
- `model_path`: Path to the model directory (with {timeframe} placeholder)
- `params`: Model-specific parameters

### Risk Management Configuration

Configure risk management in `config/config.json`:

```json
{
  "risk_management": {
    "max_open_positions": 3,
    "max_daily_trades": 10,
    "max_drawdown_percent": 5.0,
    "position_sizing": {
      "method": "fixed_risk",
      "risk_percent": 1.0,
      "fixed_lot_size": 0.01,
      "max_lot_size": 0.1
    },
    "stop_loss": {
      "enabled": true,
      "method": "atr_multiple",
      "atr_period": 14,
      "atr_multiple": 2.0,
      "fixed_pips": 100
    },
    "take_profit": {
      "enabled": true,
      "method": "risk_reward",
      "risk_reward_ratio": 2.0,
      "fixed_pips": 200
    },
    "trailing_stop": {
      "enabled": true,
      "activation_percent": 0.5,
      "step_percent": 0.1
    }
  }
}
```

Key parameters:
- `max_open_positions`: Maximum number of open positions
- `max_daily_trades`: Maximum number of trades per day
- `max_drawdown_percent`: Maximum allowed drawdown percentage
- `position_sizing`: Position sizing method and parameters
- `stop_loss`: Stop loss configuration
- `take_profit`: Take profit configuration
- `trailing_stop`: Trailing stop configuration

## Running the Trading Bot

### Standard Trading Bot

To run the standard trading bot:

```bash
python main.py
```

This will:
1. Load the configuration from `config/config.json`
2. Connect to all configured MT5 terminals
3. Load all enabled models
4. Start generating trading signals
5. Execute trades based on the signals
6. Log all activities to the console and log files

### Stopping the Trading Bot

There are several ways to stop the trading bot:

#### Method 1: Using Keyboard Interrupt (Ctrl+C)

The simplest way to stop the bot is to press `Ctrl+C` in the terminal where the bot is running. The application has signal handlers set up to catch this interrupt and perform a clean shutdown.

#### Method 2: Using the Stop Script

You can use the provided stop script to programmatically stop all bots:

```bash
python stop_bots.py
```

This script will:
1. Initialize all necessary managers
2. Create a TradingBotManager instance
3. Call the stop_all_bots() method to stop all running bots
4. Perform a full shutdown of all resources

#### Method 3: Using the Bot Control Interface

For more control over bot operations, use the bot_control.py script:

```bash
# To stop all bots
python bot_control.py stop

# To stop a specific bot (by terminal ID)
python bot_control.py stop --terminal 1
```

The bot_control.py script also provides commands for starting bots and checking their status.

### Independent Trading System

The independent trading system runs each terminal with its own dedicated primary model:

```bash
python independent_trading_bot.py
```

This will:
1. Load the configuration from `config/config.json`
2. Connect to all configured MT5 terminals
3. Assign primary models to each terminal based on the configuration
4. Start generating trading signals for each terminal
5. Execute trades independently on each terminal
6. Log all activities to the console and log files

### Testing the Trading System

To test the trading system without executing real trades:

```bash
python test_trading_bot.py --mode simulation
```

To test specific terminals:

```bash
python test_independent_trading.py --terminal 1 --iterations 3 --interval 300
```

To test all terminals:

```bash
python test_independent_trading.py --iterations 3 --interval 300
```

## Monitoring and Management

### Monitoring Performance

To monitor the trading bot's performance:

1. **View Logs**:
   ```bash
   tail -f logs/main.log
   ```

2. **Monitor Performance Metrics**:
   ```bash
   python monitoring/monitor.py
   ```

3. **Generate Performance Reports**:
   ```bash
   python reporting/generate_report.py --start-date 2023-01-01 --end-date 2023-12-31
   ```

### Managing Open Positions

The trading bot automatically manages open positions based on the configured risk management parameters. However, you can also manually manage positions:

1. **View Open Positions**:
   ```bash
   python utils/position_manager.py --list
   ```

2. **Close Specific Position**:
   ```bash
   python utils/position_manager.py --close-position 12345
   ```

3. **Close All Positions**:
   ```bash
   python utils/position_manager.py --close-all
   ```

### Handling Errors

The trading bot includes comprehensive error handling:

1. **View Error Logs**:
   ```bash
   grep ERROR logs/main.log
   ```

2. **Restart After Error**:
   ```bash
   python utils/error_recovery.py --restart
   ```

3. **Check System Health**:
   ```bash
   python utils/system_health.py
   ```

## Advanced Configuration

### Custom Strategies

To implement custom trading strategies:

1. Create a new strategy class in `trading/strategies/`
2. Implement the required methods (generate_signals, calculate_entry_exit, etc.)
3. Update the configuration to use your custom strategy

Example custom strategy configuration:

```json
{
  "strategy": {
    "type": "custom",
    "class_name": "MyCustomStrategy",
    "module_path": "trading.strategies.my_custom_strategy",
    "params": {
      "custom_param_1": 10,
      "custom_param_2": "value"
    }
  }
}
```

### Advanced Risk Management

For advanced risk management:

1. Create a custom risk manager in `trading/risk_managers/`
2. Implement the required methods (calculate_position_size, calculate_stop_loss, etc.)
3. Update the configuration to use your custom risk manager

Example custom risk manager configuration:

```json
{
  "risk_management": {
    "type": "custom",
    "class_name": "MyCustomRiskManager",
    "module_path": "trading.risk_managers.my_custom_risk_manager",
    "params": {
      "custom_param_1": 10,
      "custom_param_2": "value"
    }
  }
}
```

### Performance Optimization

To optimize the trading bot's performance:

1. **Memory Optimization**:
   ```json
   {
     "system": {
       "memory_management": {
         "enabled": true,
         "max_memory_usage_percent": 80,
         "cleanup_interval": 3600
       }
     }
   }
   ```

2. **CPU Optimization**:
   ```json
   {
     "system": {
       "cpu_management": {
         "enabled": true,
         "max_cpu_usage_percent": 70,
         "thread_pool_size": 4
       }
     }
   }
   ```

3. **Network Optimization**:
   ```json
   {
     "system": {
       "network_management": {
         "enabled": true,
         "connection_timeout": 30000,
         "retry_interval": 5000,
         "max_retries": 3
       }
     }
   }
   ```

## Troubleshooting

### Common Issues

1. **MT5 Connection Issues**:
   - Ensure MetaTrader 5 is running
   - Check terminal paths in configuration
   - Verify login credentials
   - Make sure automated trading is enabled
   - Check network connectivity

2. **Model Loading Issues**:
   - Ensure models are trained and saved correctly
   - Check model paths in configuration
   - Verify that model files exist
   - Check for compatibility issues between saved models and current code

3. **Trading Execution Issues**:
   - Check if the symbol is available in your MT5 terminal
   - Ensure you have sufficient balance for trading
   - Verify that trading is allowed for your account
   - Check for any active trading restrictions

4. **Performance Issues**:
   - Monitor system resource usage
   - Reduce the number of models or timeframes
   - Optimize data processing
   - Use more efficient model loading strategies

### Solutions

1. **For MT5 Connection Issues**:
   ```bash
   python utils/mt5_connection_test.py --terminal-id 1
   ```

2. **For Model Loading Issues**:
   ```bash
   python utils/model_validator.py --model-path models/lstm_BTCUSD.a_M5
   ```

3. **For Trading Execution Issues**:
   ```bash
   python utils/trading_test.py --symbol BTCUSD.a --terminal-id 1
   ```

4. **For Performance Issues**:
   ```bash
   python utils/performance_analyzer.py
   ```

---

This guide covers the essential aspects of trading execution for the trading bot system. For more information on data collection and model training, refer to the other guides in this directory.
