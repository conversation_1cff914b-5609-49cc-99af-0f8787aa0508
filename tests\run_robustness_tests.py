"""
<PERSON><PERSON>t to run robustness testing of models.
"""
import os
import sys
from pathlib import Path
import logging
import pandas as pd

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from tests.robustness_testing import RobustnessTester

def main():
    """Run robustness testing."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    try:
        # Define models to test
        models = ['lstm', 'tft', 'arima']

        # Define data path
        data_path = project_root / "data" / "terminal_1" / "M5_data.csv"

        # Load data
        logger.info("Loading data...")
        data = pd.read_csv(data_path)

        # Test each model
        for model_name in models:
            logger.info(f"\nTesting {model_name} model...")

            # Initialize robustness tester
            tester = RobustnessTester(model_name, data_path)

            # Test market conditions
            logger.info("Testing market conditions...")
            market_results = tester.test_market_conditions(data)

            # Test data quality
            logger.info("Testing data quality...")
            quality_results = tester.test_data_quality(data)

            # Test adversarial robustness
            logger.info("Testing adversarial robustness...")
            adversarial_results = tester.test_adversarial(data)

            # Log results
            logger.info(f"\n{model_name} model test results:")
            logger.info("Market conditions:")
            for condition, metrics in market_results.items():
                logger.info(f"{condition}: {metrics}")

            logger.info("\nData quality:")
            for issue, metrics in quality_results.items():
                logger.info(f"{issue}: {metrics}")

            logger.info("\nAdversarial robustness:")
            for attack, metrics in adversarial_results.items():
                logger.info(f"{attack}: {metrics}")

        logger.info("Robustness testing completed successfully")

    except Exception as e:
        logger.error(f"Error in robustness testing: {str(e)}")
        raise

if __name__ == "__main__":
    main()