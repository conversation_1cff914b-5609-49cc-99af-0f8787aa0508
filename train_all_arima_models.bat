@echo off
REM Script to train ARIMA models for all timeframes
setlocal EnableDelayedExpansion

echo ===================================================
echo ARIMA Model Training for All Timeframes
echo ===================================================
echo.

REM Create necessary directories
echo Creating necessary directories...
mkdir models 2>nul
mkdir logs 2>nul
mkdir metrics 2>nul
mkdir plots 2>nul
echo.

REM Set timeframes
set TIMEFRAMES=M5 M15 M30 H1 H4
set SUCCESS_COUNT=0
set TOTAL_COUNT=0

REM Train ARIMA models for all timeframes
for %%t in (%TIMEFRAMES%) do (
    set /A TOTAL_COUNT+=1
    echo ===================================================
    echo Training ARIMA model for %%t timeframe...
    echo ===================================================

    REM Run the Python script and capture its exit code
    python train_arima_single.py --timeframe %%t --target-column close --auto-arima --max-rows 50000 --data-selection all --use-ensemble --ensemble-models 5
    set LAST_ERROR=!ERRORLEVEL!

    REM Check if training was successful
    if !LAST_ERROR! EQU 0 (
        echo.
        echo [SUCCESS] ARIMA model for %%t trained successfully!
        echo Model saved to models\arima_BTCUSD.a_%%t
        echo.
        set /A SUCCESS_COUNT+=1
    ) else (
        echo.
        echo [ERROR] Failed to train ARIMA model for %%t (Exit code: !LAST_ERROR!)
        echo Please check the logs for more information.
        echo.
    )
)

echo ===================================================
echo Training Summary
echo ===================================================
echo Successfully trained !SUCCESS_COUNT! out of !TOTAL_COUNT! models.

if !SUCCESS_COUNT! EQU !TOTAL_COUNT! (
    echo All ARIMA models trained successfully!
) else (
    echo Some models failed to train. Please check the logs for details.
)
echo.
echo Press any key to exit...
pause

endlocal
