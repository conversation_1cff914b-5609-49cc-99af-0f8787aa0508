"""
Safe MT5 Status Checker

This script safely connects to MT5 terminals and displays their state and trading balance
without disabling Algo Trading. It follows best practices for MT5 connection management.

Key features:
1. Uses portable=True to preserve Algo Trading
2. Never calls mt5.shutdown() which would disable Algo Trading
3. <PERSON>perly checks Algo Trading status using terminal_info.trade_allowed
4. Safely disconnects without disabling Algo Trading
"""

import os
import time
import logging
import MetaTrader5 as mt5

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import MT5 credentials from config
from config.credentials import MT5_TERMINALS

# Convert MT5_TERMINALS from int keys to string keys for consistency
MT5_TERMINALS = {str(k): v for k, v in MT5_TERMINALS.items()}

# Add descriptions for better readability
MT5_TERMINALS["1"]["description"] = "Pepperstone Demo 1"
MT5_TERMINALS["2"]["description"] = "Pepperstone Demo 2"
MT5_TERMINALS["3"]["description"] = "IC Markets Demo 1"
MT5_TERMINALS["4"]["description"] = "IC Markets Demo 2"
MT5_TERMINALS["5"]["description"] = "IC Markets Demo 3"

def safe_connect_mt5(terminal_id: str) -> bool:
    """
    Safely connect to an MT5 terminal without disabling Algo Trading.

    Args:
        terminal_id: ID of the terminal to connect to

    Returns:
        bool: True if connection successful, False otherwise
    """
    # Get terminal configuration
    if terminal_id not in MT5_TERMINALS:
        logger.error(f"Terminal {terminal_id} not found in configuration")
        return False

    terminal_config = MT5_TERMINALS[terminal_id]

    # Check if terminal path exists
    if not os.path.exists(terminal_config["path"]):
        logger.error(f"Terminal path does not exist: {terminal_config['path']}")
        return False

    logger.info(f"Connecting to terminal {terminal_id}: {terminal_config['description']}")
    logger.info(f"Path: {terminal_config['path']}")
    logger.info(f"Server: {terminal_config['server']}")

    # Check if MT5 is already initialized
    if mt5.initialize():
        logger.info("MT5 already initialized")

        # Check if we're connected to the right terminal
        account_info = mt5.account_info()
        if account_info and str(account_info.login) == str(terminal_config["login"]):
            logger.info(f"Already connected to terminal {terminal_id}")
            return True

        # We're connected to a different terminal
        # Instead of calling mt5.shutdown(), we'll just disconnect
        logger.info("Connected to a different terminal, disconnecting...")
        # DO NOT call mt5.shutdown() as it will disable Algo Trading
        # Just let the next initialize call handle the reconnection

    # Initialize MT5 with proper parameters
    logger.info(f"Initializing MT5 for terminal {terminal_id}")
    if not mt5.initialize(
        path=terminal_config["path"],
        login=int(terminal_config["login"]),
        password=terminal_config["password"],
        server=terminal_config["server"],
        portable=True,     # CRITICAL: Must be True to preserve Algo Trading
        trade_mode=True,   # Enable trading
        auto_trading=True  # Enable auto trading
    ):
        error = mt5.last_error()
        logger.error(f"Failed to initialize MT5 for terminal {terminal_id}: {error}")
        return False

    logger.info(f"Successfully connected to terminal {terminal_id}")
    return True

def check_terminal_status(terminal_id: str) -> None:
    """
    Check and display terminal status and trading balance.

    Args:
        terminal_id: ID of the terminal to check
    """
    # Connect to the terminal
    if not safe_connect_mt5(terminal_id):
        logger.error(f"Could not connect to terminal {terminal_id}")
        return

    # Check terminal info
    terminal_info = mt5.terminal_info()
    if not terminal_info:
        logger.error(f"Could not get terminal info for terminal {terminal_id}")
        return

    # Check account info
    account_info = mt5.account_info()
    if not account_info:
        logger.error(f"Could not get account info for terminal {terminal_id}")
        return

    # Display terminal status
    logger.info("\n=== Terminal Status ===")
    logger.info(f"Terminal Name: {terminal_info.name}")
    logger.info(f"Terminal Path: {terminal_info.path}")
    logger.info(f"Terminal Connected: {terminal_info.connected}")
    logger.info(f"Community Connection: {terminal_info.community_connection}")
    logger.info(f"Community Balance: {terminal_info.community_balance}")
    logger.info(f"Retransmission: {terminal_info.retransmission}")

    # Check if Algo Trading is enabled
    if terminal_info.trade_allowed:
        logger.info("Algo Trading: ENABLED ✓")
    else:
        logger.warning("Algo Trading: DISABLED ✗")
        logger.warning("Please enable Algo Trading in the MT5 terminal:")
        logger.warning(f"1. Open the terminal at {MT5_TERMINALS[terminal_id]['path']}")
        logger.warning("2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
        logger.warning("3. Verify that 'Algo Trading enabled' appears in the status bar")

    # Display account info
    logger.info("\n=== Account Information ===")
    logger.info(f"Login: {account_info.login}")
    logger.info(f"Server: {account_info.server}")
    logger.info(f"Currency: {account_info.currency}")
    logger.info(f"Company: {account_info.company}")
    logger.info(f"Balance: {account_info.balance}")
    logger.info(f"Equity: {account_info.equity}")
    logger.info(f"Margin: {account_info.margin}")
    logger.info(f"Margin Free: {account_info.margin_free}")
    logger.info(f"Margin Level: {account_info.margin_level}%")
    logger.info(f"Leverage: 1:{account_info.leverage}")

    # Display additional terminal info
    logger.info("\n=== Additional Terminal Info ===")
    logger.info(f"Build: {terminal_info.build}")
    logger.info(f"MQL ID: {terminal_info.mqid}")
    logger.info(f"Language: {terminal_info.language}")

    # Display symbol info for BTCUSD
    symbol = "BTCUSD.a"
    logger.info(f"\n=== Symbol Information for {symbol} ===")
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info:
        logger.info(f"Bid: {symbol_info.bid}")
        logger.info(f"Ask: {symbol_info.ask}")
        logger.info(f"Spread: {symbol_info.spread} points")
        logger.info(f"Volume Min: {symbol_info.volume_min}")
        logger.info(f"Volume Max: {symbol_info.volume_max}")
        logger.info(f"Volume Step: {symbol_info.volume_step}")
    else:
        logger.warning(f"Symbol {symbol} not found")

    # DO NOT call mt5.shutdown() as it will disable Algo Trading

def main():
    """Main function to check all terminals or a specific terminal."""
    import argparse

    parser = argparse.ArgumentParser(description="Safe MT5 Status Checker")
    parser.add_argument("--terminal", type=str, help="Terminal ID to check (1-5)")
    parser.add_argument("--all", action="store_true", help="Check all terminals")
    args = parser.parse_args()

    if args.terminal:
        if args.terminal not in MT5_TERMINALS:
            logger.error(f"Invalid terminal ID: {args.terminal}")
            logger.info(f"Available terminals: {', '.join(MT5_TERMINALS.keys())}")
            return

        check_terminal_status(args.terminal)

    elif args.all:
        for terminal_id in MT5_TERMINALS:
            logger.info(f"\n{'=' * 50}")
            logger.info(f"Checking Terminal {terminal_id}: {MT5_TERMINALS[terminal_id]['description']}")
            logger.info(f"{'=' * 50}")
            check_terminal_status(terminal_id)

            # Wait a bit between terminals to avoid connection issues
            if terminal_id != list(MT5_TERMINALS.keys())[-1]:
                logger.info("Waiting 5 seconds before checking next terminal...")
                time.sleep(5)

    else:
        # Default to terminal 1 if no arguments provided
        check_terminal_status("1")

    logger.info("\nStatus check completed")
    # DO NOT call mt5.shutdown() as it will disable Algo Trading

if __name__ == "__main__":
    main()
