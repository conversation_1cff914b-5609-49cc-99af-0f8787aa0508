# Trading Bot Requirements
# -----------------------
# This file contains pinned versions of all dependencies to ensure compatibility.
# The versions have been carefully selected to avoid conflicts, especially
# between numpy, pmdarima, and tensorflow.
#
# IMPORTANT: USE A VIRTUAL ENVIRONMENT
# ------------------------------------
# Due to dependency conflicts, it's STRONGLY RECOMMENDED to use a virtual environment:
#
#   1. Create a virtual environment:
#      python -m venv trading_bot_env
#
#   2. Activate the environment:
#      - Windows: trading_bot_env\Scripts\activate
#      - Linux/Mac: source trading_bot_env/bin/activate
#
#   3. Install core dependencies first:
#      pip install numpy==1.23.5
#      pip install pmdarima==2.0.3
#      pip install tensorflow==2.12.0
#
#   4. Then install remaining dependencies:
#      pip install -r requirements.txt
#
# Alternatively, use the setup_environment.py script which will guide you through this process.
#
# DEPENDENCY CONFLICTS
# -------------------
# Known conflicts that this configuration resolves:
# - pmdar<PERSON> requires numpy with specific binary compatibility
# - tensorflow 2.12.0 works with numpy 1.23.5
# - bayesian-optimization may require newer numpy versions
#
# Core dependencies - PINNED VERSIONS FOR COMPATIBILITY
numpy==1.23.5  # Pinned to ensure compatibility with pmdarima and tensorflow
pandas==2.0.3  # Compatible with numpy 1.23.5
scikit-learn==1.3.0  # Compatible with numpy 1.23.5
matplotlib==3.7.2
seaborn==0.12.2
dash==2.11.1
plotly==5.15.0
bayesian-optimization==1.4.3
psutil==5.9.5
GPUtil==1.4.0

# Testing and development tools
pytest==7.4.0
pytest-cov==4.1.0
black==23.7.0
flake8==6.1.0
mypy==1.4.1
isort==5.12.0
pre-commit==3.3.3

# PyTorch ecosystem with CUDA support
--find-links https://download.pytorch.org/whl/cu118
torch==2.6.0+cu118
torchvision==0.19.0+cu118
torchaudio==2.6.0+cu118
pytorch-lightning==2.0.9
pytorch-forecasting==1.0.0

# TensorFlow ecosystem
tensorflow==2.12.0  # Compatible with numpy 1.23.5
# tensorflow-gpu is included in tensorflow since 2.10

# MT5 and trading dependencies
MetaTrader5==5.0.45  # MT5 API
ta==0.10.2  # Technical analysis library

# Communication and notifications
python-telegram-bot==20.3  # For Telegram notifications

# Utilities
tqdm==4.65.0  # Progress bars
optuna==3.2.0  # Hyperparameter optimization
wandb==0.15.5  # Experiment tracking
joblib==1.3.1  # Parallel processing
h5py==3.9.0  # HDF5 support
pyyaml==6.0.1  # Configuration files
pyarrow==12.0.1  # Parquet file support
fastparquet==2023.7.0  # For parquet files

# ARIMA dependencies - CRITICAL FOR COMPATIBILITY
statsmodels==0.14.0  # For ARIMA/SARIMAX models
pmdarima==2.0.3  # Pinned for compatibility with numpy 1.23.5
scipy==1.10.1  # Required by statsmodels and pmdarima

# Type checking
typing-extensions==4.7.1  # Extended type hints

# Additional utilities
requests==2.31.0  # HTTP requests
python-dotenv==1.0.0  # Environment variables
beautifulsoup4==4.12.2  # Web scraping
ccxt==3.1.54  # Cryptocurrency exchange API
yfinance==0.2.28  # Yahoo Finance API
forex-python==1.8  # Forex rates

# Note: The following packages are part of the Python standard library
# and don't need to be installed via pip:
# - socket
# - logging
# - threading
# - uuid
