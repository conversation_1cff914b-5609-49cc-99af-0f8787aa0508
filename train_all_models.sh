#!/bin/bash
# Script to train all models (TFT and TFT+ARIMA) on all timeframes

# Create necessary directories
mkdir -p models
mkdir -p metrics
mkdir -p plots
mkdir -p comparison_results

# Set timeframes
TIMEFRAMES=("M5" "M15" "M30" "H1" "H4")

# Set common parameters
EPOCHS=10
BATCH_SIZE=32
SEQUENCE_LENGTH=60
HIDDEN_SIZE=64
ATTENTION_HEAD_SIZE=4
DROPOUT_RATE=0.1
LEARNING_RATE=0.001
FEATURE_COLUMNS="open,high,low,close,real_volume"
TARGET_COLUMN="close"
USE_GPU=true

echo "Starting model training..."

# Train TFT models for all timeframes
for TIMEFRAME in "${TIMEFRAMES[@]}"
do
    echo "Training TFT model for $TIMEFRAME timeframe..."
    python train_tft_single.py \
        --timeframe $TIMEFRAME \
        --feature-columns $FEATURE_COLUMNS \
        --target-column $TARGET_COLUMN \
        --sequence-length $SEQUENCE_LENGTH \
        --hidden-size $HIDDEN_SIZE \
        --attention-head-size $ATTENTION_HEAD_SIZE \
        --dropout-rate $DROPOUT_RATE \
        --learning-rate $LEARNING_RATE \
        --epochs $EPOCHS \
        --batch-size $BATCH_SIZE \
        --use-gpu $USE_GPU
    
    echo "TFT model training for $TIMEFRAME completed."
done

# Train TFT+ARIMA models for all timeframes
for TIMEFRAME in "${TIMEFRAMES[@]}"
do
    echo "Training TFT+ARIMA model for $TIMEFRAME timeframe..."
    python train_tft_arima_single.py \
        --timeframe $TIMEFRAME \
        --feature-columns $FEATURE_COLUMNS \
        --target-column $TARGET_COLUMN \
        --sequence-length $SEQUENCE_LENGTH \
        --hidden-size $HIDDEN_SIZE \
        --attention-head-size $ATTENTION_HEAD_SIZE \
        --dropout-rate $DROPOUT_RATE \
        --learning-rate $LEARNING_RATE \
        --epochs $EPOCHS \
        --batch-size $BATCH_SIZE \
        --use-gpu $USE_GPU \
        --arima-window 10000
    
    echo "TFT+ARIMA model training for $TIMEFRAME completed."
done

# Compare all models
echo "Comparing all models..."
python compare_all_models.py --output-dir comparison_results

echo "All model training and comparison completed."
echo "Results are available in the comparison_results directory."
