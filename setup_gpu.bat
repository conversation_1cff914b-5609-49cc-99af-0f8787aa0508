@echo off
echo ===================================================
echo Unified TensorFlow GPU Setup Script
echo ===================================================
echo This script will set up TensorFlow with GPU support
echo for your specific CUDA version and environment.
echo.

REM Check Python version
echo Checking Python version...
python --version
echo.

REM Check for NVIDIA GPU
echo Checking for NVIDIA GPU...
where nvidia-smi >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: nvidia-smi not found. NVIDIA GPU drivers may not be installed.
    echo Please install NVIDIA drivers from https://www.nvidia.com/Download/index.aspx
    echo.
    goto no_gpu
) else (
    echo NVIDIA GPU detected. Checking details...
    nvidia-smi
    echo.
)

REM Check CUDA version
echo Checking CUDA version...
where nvcc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: nvcc not found. CUDA may not be installed.
    echo Please install CUDA from https://developer.nvidia.com/cuda-downloads
    echo.
    goto no_cuda
) else (
    for /f "tokens=*" %%i in ('nvcc --version ^| findstr "release"') do set CUDA_VERSION_LINE=%%i
    echo %CUDA_VERSION_LINE%
    
    REM Extract CUDA version
    for /f "tokens=5" %%i in ("%CUDA_VERSION_LINE%") do set CUDA_VERSION=%%i
    echo Detected CUDA version: %CUDA_VERSION%
    echo.
)

REM Check current TensorFlow installation
echo Checking current TensorFlow installation...
python -c "import tensorflow as tf; print(f'TensorFlow version: {tf.__version__}')" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo TensorFlow is not installed.
    set TF_INSTALLED=0
) else (
    for /f "tokens=*" %%i in ('python -c "import tensorflow as tf; print(tf.__version__)"') do set TF_VERSION=%%i
    echo Current TensorFlow version: %TF_VERSION%
    set TF_INSTALLED=1
)
echo.

REM Display options
echo Available options:
echo 1. Install TensorFlow 2.19.0 (for CUDA 12.x)
echo 2. Install TensorFlow 2.16.1 (for CUDA 12.x)
echo 3. Install TensorFlow 2.15.0 (for CUDA 11.8)
echo 4. Auto-detect and install compatible TensorFlow
echo 5. Force CPU-only mode (no GPU)
echo 6. Exit without changes
echo.

choice /C 123456 /M "Choose an option"

if errorlevel 6 goto end
if errorlevel 5 goto cpu_only
if errorlevel 4 goto auto_detect
if errorlevel 3 goto tf_2_15
if errorlevel 2 goto tf_2_16
if errorlevel 1 goto tf_2_19

:auto_detect
echo.
echo Auto-detecting compatible TensorFlow version...
echo %CUDA_VERSION% | findstr /r "^12\." >nul
if %ERRORLEVEL% EQU 0 (
    echo CUDA 12.x detected, recommending TensorFlow 2.19.0
    choice /C YN /M "Install TensorFlow 2.19.0"
    if errorlevel 2 goto tf_2_16
    if errorlevel 1 goto tf_2_19
) else (
    echo %CUDA_VERSION% | findstr /r "^11\.8" >nul
    if %ERRORLEVEL% EQU 0 (
        echo CUDA 11.8 detected, recommending TensorFlow 2.15.0
        choice /C YN /M "Install TensorFlow 2.15.0"
        if errorlevel 2 goto end
        if errorlevel 1 goto tf_2_15
    ) else (
        echo Your CUDA version (%CUDA_VERSION%) is not directly compatible with TensorFlow.
        echo Options:
        echo 1. Install CUDA 11.8 for TensorFlow 2.15.0
        echo 2. Install CUDA 12.x for TensorFlow 2.19.0
        echo 3. Continue with CPU-only TensorFlow
        
        choice /C 123 /M "Choose an option"
        
        if errorlevel 3 goto cpu_only
        if errorlevel 2 goto cuda_12_instructions
        if errorlevel 1 goto cuda_11_instructions
    )
)

:tf_2_19
echo.
echo Installing TensorFlow 2.19.0 with GPU support...
echo.

echo Step 1: Installing compatible NumPy version...
pip install numpy==1.24.3
echo.

echo Step 2: Installing TensorFlow 2.19.0...
pip install tensorflow==2.19.0
echo.

echo Step 3: Installing NVIDIA cuDNN package...
pip install nvidia-cudnn-cu12
echo.

echo Step 4: Installing NVIDIA CUDA Python packages...
pip install nvidia-cuda-runtime-cu12
pip install nvidia-cuda-cupti-cu12
pip install nvidia-cufft-cu12
pip install nvidia-curand-cu12
pip install nvidia-cusolver-cu12
pip install nvidia-cusparse-cu12
pip install nvidia-nccl-cu12
echo.

echo Step 5: Installing other dependencies...
pip install matplotlib pandas scikit-learn xgboost lightgbm
echo.

REM Set environment variables
echo Setting environment variables...
setx CUDA_VISIBLE_DEVICES "0"
setx TF_FORCE_GPU_ALLOW_GROWTH "true"
echo.

echo Setting temporary environment variables for current session...
set CUDA_VISIBLE_DEVICES=0
set TF_FORCE_GPU_ALLOW_GROWTH=true
echo.

goto test_gpu

:tf_2_16
echo.
echo Installing TensorFlow 2.16.1 with GPU support...
echo.

echo Step 1: Installing compatible NumPy version...
pip install numpy==1.24.3
echo.

echo Step 2: Installing TensorFlow 2.16.1...
pip install tensorflow==2.16.1
echo.

echo Step 3: Installing other dependencies...
pip install matplotlib pandas scikit-learn xgboost lightgbm
echo.

REM Set environment variables
echo Setting environment variables...
setx CUDA_VISIBLE_DEVICES "0"
setx TF_FORCE_GPU_ALLOW_GROWTH "true"
echo.

echo Setting temporary environment variables for current session...
set CUDA_VISIBLE_DEVICES=0
set TF_FORCE_GPU_ALLOW_GROWTH=true
echo.

goto test_gpu

:tf_2_15
echo.
echo Installing TensorFlow 2.15.0 with GPU support...
echo.

echo Step 1: Installing compatible NumPy version...
pip install numpy==1.24.3
echo.

echo Step 2: Installing TensorFlow 2.15.0...
pip install tensorflow==2.15.0
echo.

echo Step 3: Installing other dependencies...
pip install matplotlib pandas scikit-learn xgboost lightgbm
echo.

REM Set environment variables
echo Setting environment variables...
setx CUDA_VISIBLE_DEVICES "0"
setx TF_FORCE_GPU_ALLOW_GROWTH "true"
echo.

echo Setting temporary environment variables for current session...
set CUDA_VISIBLE_DEVICES=0
set TF_FORCE_GPU_ALLOW_GROWTH=true
echo.

goto test_gpu

:cpu_only
echo.
echo Setting up CPU-only mode...
echo.

echo Step 1: Installing compatible NumPy version...
pip install numpy==1.24.3
echo.

echo Step 2: Installing TensorFlow...
pip install tensorflow
echo.

echo Step 3: Installing other dependencies...
pip install matplotlib pandas scikit-learn xgboost lightgbm
echo.

REM Set environment variables
echo Setting environment variables to force CPU usage...
setx CUDA_VISIBLE_DEVICES "-1"
echo.

echo Setting temporary environment variables for current session...
set CUDA_VISIBLE_DEVICES=-1
echo.

goto test_gpu

:no_gpu
echo.
echo No NVIDIA GPU detected. Options:
echo 1. Install CPU-only TensorFlow
echo 2. Exit and install NVIDIA drivers
echo.

choice /C 12 /M "Choose an option"

if errorlevel 2 goto end
if errorlevel 1 goto cpu_only

:no_cuda
echo.
echo CUDA not detected. Options:
echo 1. Install CPU-only TensorFlow
echo 2. Install CUDA 12.x for TensorFlow 2.19.0
echo 3. Install CUDA 11.8 for TensorFlow 2.15.0
echo 4. Exit
echo.

choice /C 1234 /M "Choose an option"

if errorlevel 4 goto end
if errorlevel 3 goto cuda_11_instructions
if errorlevel 2 goto cuda_12_instructions
if errorlevel 1 goto cpu_only

:cuda_12_instructions
echo.
echo Please download and install CUDA 12.x from:
echo https://developer.nvidia.com/cuda-downloads
echo.
echo After installation, run this script again and select option 1 or 2.
goto end

:cuda_11_instructions
echo.
echo Please download and install CUDA 11.8 from:
echo https://developer.nvidia.com/cuda-11-8-0-download-archive
echo.
echo After installation, run this script again and select option 3.
goto end

:test_gpu
echo.
echo ===================================================
echo Testing TensorFlow GPU Support
echo ===================================================
echo.

REM Create a test script
echo Creating GPU test script...
echo import tensorflow as tf > test_gpu.py
echo import os >> test_gpu.py
echo print("TensorFlow version:", tf.__version__) >> test_gpu.py
echo print("Num GPUs Available:", len(tf.config.list_physical_devices('GPU'))) >> test_gpu.py
echo print("CUDA_VISIBLE_DEVICES:", os.environ.get('CUDA_VISIBLE_DEVICES')) >> test_gpu.py
echo print("TF_FORCE_GPU_ALLOW_GROWTH:", os.environ.get('TF_FORCE_GPU_ALLOW_GROWTH')) >> test_gpu.py
echo if len(tf.config.list_physical_devices('GPU')) ^> 0: >> test_gpu.py
echo     print("GPU is available!") >> test_gpu.py
echo     with tf.device('/GPU:0'): >> test_gpu.py
echo         a = tf.constant([[1.0, 2.0], [3.0, 4.0]]) >> test_gpu.py
echo         b = tf.constant([[5.0, 6.0], [7.0, 8.0]]) >> test_gpu.py
echo         c = tf.matmul(a, b) >> test_gpu.py
echo         print("Matrix multiplication result:") >> test_gpu.py
echo         print(c) >> test_gpu.py
echo else: >> test_gpu.py
echo     print("No GPU available") >> test_gpu.py
echo     if os.environ.get('CUDA_VISIBLE_DEVICES') == '-1': >> test_gpu.py
echo         print("CPU-only mode is enabled") >> test_gpu.py
echo     else: >> test_gpu.py
echo         print("Checking TensorFlow GPU configuration:") >> test_gpu.py
echo         print("Available devices:") >> test_gpu.py
echo         from tensorflow.python.client import device_lib >> test_gpu.py
echo         for device in device_lib.list_local_devices(): >> test_gpu.py
echo             print(f"  {device.name} - {device.device_type}") >> test_gpu.py

echo.
echo Running GPU test...
python test_gpu.py

echo.
echo ===================================================
echo Setup Complete
echo ===================================================
echo.
echo If GPU is not detected but should be available:
echo 1. Check GPU_SETUP_GUIDE.md for troubleshooting steps
echo 2. Ensure your NVIDIA drivers are up to date
echo 3. Verify that CUDA and cuDNN are properly installed
echo 4. Restart your computer and try again
echo.
echo For Docker-based alternative, see DOCKER_SETUP.md
echo.

:end
pause
