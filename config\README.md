# Configuration System

This directory contains the configuration system for the trading bot. The configuration system is responsible for loading, validating, and providing access to configuration values.

## Overview

The configuration system consists of the following components:

1. **UnifiedConfigManager**: The main configuration manager that handles loading, validation, and access to configuration values.
2. **Configuration Files**: JSON files that contain configuration values.
3. **Schema Files**: JSON Schema files that define the structure and validation rules for configuration files.
4. **Credential Management**: Secure handling of sensitive data like MT5 credentials.

## Configuration Files

The following configuration files are used:

- `config.json`: The main configuration file that contains all non-sensitive configuration values.
- `config.json.example`: An example configuration file that can be used as a template.
- `local_config.json`: A local configuration file that contains sensitive data like MT5 credentials.
- `local_config_template.json`: A template for the local configuration file.

## Usage

### Basic Usage

```python
from config import config_manager, get_config

# Get the main configuration
config = get_config()

# Access configuration values
symbol = config.strategy.symbol
timeframes = config.strategy.timeframes
```

### Advanced Usage

```python
from config import config_manager

# Get specific configurations
mt5_config = config_manager.get_mt5_config()
strategy_config = config_manager.get_strategy_config()
model_config = config_manager.get_model_config('lstm')

# Get base paths
data_base_path = config_manager.get_data_base_path()
models_base_path = config_manager.get_models_base_path()
```

## Configuration Structure

The configuration is structured as follows:

- `mt5`: MT5 connection configuration
  - `max_connections`: Maximum number of connections
  - `timeout`: Connection timeout in milliseconds
  - `retry_interval`: Retry interval in seconds
  - `terminals`: Dictionary of terminal configurations
    - `<terminal_id>`: Terminal configuration
      - `path`: Path to the MT5 terminal executable
      - `login`: MT5 login
      - `password`: MT5 password
      - `server`: MT5 server
      - `trade_mode`: Whether to enable trade mode
      - `auto_trading`: Whether to enable auto trading

- `strategy`: Trading strategy configuration
  - `symbol`: Trading symbol
  - `timeframes`: List of timeframes to use
  - `sequence_length`: Length of the sequence for ML models
  - `lot_size`: Lot size for trading
  - `max_positions`: Maximum number of open positions
  - `stop_loss_pips`: Stop loss in pips
  - `take_profit_pips`: Take profit in pips
  - `max_spread_pips`: Maximum spread in pips
  - `risk_per_trade`: Risk per trade as a percentage of account balance
  - `max_daily_loss`: Maximum daily loss in account currency
  - `max_daily_trades`: Maximum number of trades per day
  - `cooldown_period`: Cooldown period in seconds after a trade

- `models`: Dictionary of model configurations
  - `<model_name>`: Model configuration
    - `model_path`: Path to the model file
    - `input_dim`: Input dimension
    - `output_dim`: Output dimension
    - `weight`: Weight of the model in the ensemble
    - `FEATURE_COLUMNS`: List of feature columns
    - ... (model-specific parameters)

- `data_base_path`: Base path for data
- `models_base_path`: Base path for models
- `confidence_threshold`: Confidence threshold for trading signals
- `update_interval`: Update interval in seconds
- `max_memory_usage`: Maximum memory usage as a percentage
- `log_level`: Logging level
- `debug_mode`: Whether to enable debug mode

## Credential Management

Sensitive data like MT5 credentials are stored in the `local_config.json` file, which is not committed to version control. The `local_config_template.json` file provides a template for creating the local configuration file.

The configuration system will load credentials from the following sources, in order of precedence:

1. Environment variables
2. `local_config.json`
3. `credentials.py`
4. `config.json`

## Directory Structure

- `config.json`: Main configuration file
- `config.json.example`: Example configuration file
- `local_config.json`: Local configuration file with sensitive data
- `local_config_template.json`: Template for local configuration file
- `credentials.py`: Python module with credentials
- `credentials_template.py`: Template for credentials module
- `unified_config.py`: Unified configuration manager
- `config.py`: Legacy configuration manager (for backwards compatibility)
- `service.py`: Legacy configuration service (for backwards compatibility)
- `schemas/`: Directory with JSON Schema files
  - `unified_config.json`: Schema for the unified configuration
  - `config.json`: Legacy schema for the configuration

## Best Practices

1. **Never commit sensitive data**: Always use `local_config.json` or environment variables for sensitive data.
2. **Use the unified configuration manager**: Always use the `config_manager` from `config` module.
3. **Validate configuration**: Always validate configuration values before using them.
4. **Use consistent path handling**: Always use `Path` objects for path handling.
5. **Document configuration changes**: Always document changes to the configuration structure.
