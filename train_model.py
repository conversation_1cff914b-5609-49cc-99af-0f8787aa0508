#!/usr/bin/env python
"""
Model Training Utility

This script provides a command-line interface for training different types of models
for the trading bot.

Usage:
    python train_model.py --model-type lstm --symbol EURUSD --timeframe M5 [--options]
    python train_model.py --model-type arima --symbol EURUSD --timeframe M5 [--options]
"""

import argparse
import logging
from config.unified_config import UnifiedConfigManager
from utils.pytorch_lstm_trainer import PyTorchLSTMModelTrainer
from utils.arima_trainer import ARIMATrainer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train models for the trading bot')

    # Required arguments
    parser.add_argument('--model-type', type=str, required=True, choices=['lstm', 'arima', 'tft'],
                        help='Type of model to train (lstm, arima, or tft)')
    parser.add_argument('--symbol', type=str, required=True,
                        help='Trading symbol (e.g., BTCUSD.a)')
    timeframe_group = parser.add_mutually_exclusive_group(required=True)
    timeframe_group.add_argument('--timeframe', type=str,
                        help='Timeframe (e.g., M5, H1)')
    timeframe_group.add_argument('--timeframes', type=str,
                        help='Comma-separated list of timeframes (e.g., M5,H1,H4)')

    # Common optional arguments
    parser.add_argument('--model-name', type=str,
                        help='Name of the model (defaults to model type)')
    parser.add_argument('--sequence-length', type=int, default=60,
                        help='Length of input sequences')
    parser.add_argument('--feature-columns', type=str,
                        help='Comma-separated list of feature columns')
    parser.add_argument('--target-column', type=str, default='close',
                        help='Column to use as target')
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Proportion of data to use for testing')
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    parser.add_argument('--file-format', type=str, choices=['csv', 'parquet'], default='csv',
                        help='File format of the data')
    parser.add_argument('--data-dir', type=str,
                        help='Data directory (if None, use config)')
    parser.add_argument('--save-metrics', action='store_true', default=True,
                        help='Save metrics to a file')
    parser.add_argument('--use-gpu', action='store_true', default=True,
                        help='Use GPU for training if available')

    # LSTM-specific arguments
    lstm_group = parser.add_argument_group('LSTM options')
    lstm_group.add_argument('--epochs', type=int, default=100,
                           help='Number of training epochs')
    lstm_group.add_argument('--batch-size', type=int, default=32,
                           help='Batch size for training')
    lstm_group.add_argument('--validation-split', type=float, default=0.1,
                           help='Proportion of training data to use for validation')
    lstm_group.add_argument('--lstm-units', type=int, default=50,
                           help='Number of LSTM units')
    lstm_group.add_argument('--num-layers', type=int, default=2,
                           help='Number of LSTM layers')
    lstm_group.add_argument('--dropout-rate', type=float, default=0.2,
                           help='Dropout rate')
    lstm_group.add_argument('--learning-rate', type=float, default=0.001,
                           help='Learning rate')

    # ARIMA-specific arguments
    arima_group = parser.add_argument_group('ARIMA options')
    arima_group.add_argument('--p', type=int, default=1,
                            help='AR order')
    arima_group.add_argument('--d', type=int, default=1,
                            help='Differencing')
    arima_group.add_argument('--q', type=int, default=1,
                            help='MA order')
    arima_group.add_argument('--seasonal-p', type=int, default=0,
                            help='Seasonal AR order')
    arima_group.add_argument('--seasonal-d', type=int, default=0,
                            help='Seasonal differencing')
    arima_group.add_argument('--seasonal-q', type=int, default=0,
                            help='Seasonal MA order')
    arima_group.add_argument('--seasonal-m', type=int, default=0,
                            help='Seasonal period')
    arima_group.add_argument('--use-seasonal', action='store_true',
                            help='Use seasonal component')
    arima_group.add_argument('--auto-arima', action='store_true', default=True,
                            help='Use auto ARIMA')
    arima_group.add_argument('--use-exog', action='store_true',
                            help='Use exogenous variables')
    arima_group.add_argument('--exog-columns', type=str,
                            help='Comma-separated list of exogenous variable columns')

    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Initialize configuration manager
    config_manager = UnifiedConfigManager()

    # Parse feature columns
    feature_columns = None
    if args.feature_columns:
        feature_columns = args.feature_columns.split(',')

    # Set default model name if not provided
    model_name = args.model_name or args.model_type

    # Determine timeframes to process
    timeframes = []
    if args.timeframe:
        timeframes = [args.timeframe]
    elif args.timeframes:
        timeframes = args.timeframes.split(',')

    # Store metrics for all timeframes
    all_metrics = {}

    # Process each timeframe
    for timeframe in timeframes:
        logger.info(f"Processing timeframe: {timeframe}")

        # Set model name with timeframe if multiple timeframes
        current_model_name = f"{model_name}_{timeframe}" if len(timeframes) > 1 else model_name

        # Train model based on type
        if args.model_type == 'lstm':
            trainer = PyTorchLSTMModelTrainer(config_manager)
            result = trainer.train(
                symbol=args.symbol,
                timeframe=timeframe,
                model_name=current_model_name,
                sequence_length=args.sequence_length,
                feature_columns=feature_columns,
                target_column=args.target_column,
                test_size=args.test_size,
                random_state=args.random_state,
                epochs=args.epochs,
                batch_size=args.batch_size,
                validation_split=args.validation_split,
                hidden_units=args.lstm_units,
                num_layers=args.num_layers,
                dropout_rate=args.dropout_rate,
                learning_rate=args.learning_rate,
                file_format=args.file_format,
                data_dir=args.data_dir,
                use_gpu=args.use_gpu
            )

            # Log GPU usage information
            if result and 'config' in result:
                if result['config'].get('gpu_available', False) and args.use_gpu:
                    logger.info(f"LSTM model trained using GPU: {result['config'].get('device', 'unknown')}")
                else:
                    logger.info("LSTM model trained using CPU")

        elif args.model_type == 'arima':
            # Parse exogenous columns if provided
            exog_columns = None
            if args.exog_columns:
                exog_columns = args.exog_columns.split(',')

            trainer = ARIMATrainer(config_manager)
            result = trainer.train(
                symbol=args.symbol,
                timeframe=timeframe,
                model_name=current_model_name,
                feature_columns=feature_columns,
                target_column=args.target_column,
                test_size=args.test_size,
                random_state=args.random_state,
                p=args.p,
                d=args.d,
                q=args.q,
                seasonal_p=args.seasonal_p,
                seasonal_d=args.seasonal_d,
                seasonal_q=args.seasonal_q,
                seasonal_m=args.seasonal_m,
                use_seasonal=args.use_seasonal,
                auto_arima=args.auto_arima,
                use_exog=args.use_exog,
                exog_columns=exog_columns,
                file_format=args.file_format,
                data_dir=args.data_dir,
                plot=True  # Enable plotting
            )

        elif args.model_type == 'tft':
            # Import TFT trainer only if needed
            try:
                from utils.tft_trainer import TFTTrainer
                trainer = TFTTrainer(config_manager)
                result = trainer.train(
                    symbol=args.symbol,
                    timeframe=timeframe,
                    model_name=current_model_name,
                    sequence_length=args.sequence_length,
                    feature_columns=feature_columns,
                    target_column=args.target_column,
                    test_size=args.test_size,
                    random_state=args.random_state,
                    epochs=args.epochs,
                    batch_size=args.batch_size,
                    hidden_size=args.lstm_units if hasattr(args, 'lstm_units') else 64,
                    dropout_rate=args.dropout_rate if hasattr(args, 'dropout_rate') else 0.1,
                    learning_rate=args.learning_rate if hasattr(args, 'learning_rate') else 0.001,
                    file_format=args.file_format,
                    data_dir=args.data_dir,
                    use_gpu=args.use_gpu
                )

                # Log GPU usage information
                if result and 'config' in result and 'gpu_info' in result['config']:
                    gpu_info = result['config']['gpu_info']
                    if gpu_info['available'] and args.use_gpu:
                        logger.info(f"TFT model trained using GPU: {gpu_info['device_name']}")
                    else:
                        logger.info("TFT model trained using CPU")
            except ImportError:
                logger.error("TFT trainer not available. Make sure PyTorch and PyTorch Forecasting are installed.")
                result = None

        else:
            logger.error(f"Unsupported model type: {args.model_type}")
            return

        # Print results and store metrics
        if result:
            logger.info(f"Successfully trained {args.model_type} model for {args.symbol} {timeframe}")
            logger.info(f"Metrics: MSE={result['metrics']['mse']:.6f}, RMSE={result['metrics']['rmse']:.6f}, MAE={result['metrics']['mae']:.6f}, R²={result['metrics'].get('r2', 0.0):.6f}")

            # Store metrics for this timeframe
            all_metrics[timeframe] = result['metrics']
        else:
            logger.error(f"Failed to train {args.model_type} model for {args.symbol} {timeframe}")

    # Save all metrics to a summary file if multiple timeframes were processed or if save_metrics is True
    if len(timeframes) > 1 or args.save_metrics:
        import json
        import os
        from datetime import datetime

        # Create metrics directory if it doesn't exist
        os.makedirs('metrics', exist_ok=True)

        # Create a summary file with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        summary_file = f"metrics/{args.model_type}_{args.symbol}_summary_{timestamp}.json"

        with open(summary_file, 'w') as f:
            json.dump({
                'model_type': args.model_type,
                'symbol': args.symbol,
                'timeframes': timeframes,
                'metrics': all_metrics
            }, f, indent=4)

        logger.info(f"Metrics summary saved to {summary_file}")

        # Print comparison table
        logger.info("Metrics comparison across timeframes:")
        logger.info(f"{'Timeframe':<10} {'MSE':<15} {'RMSE':<15} {'MAE':<15} {'R²':<15}")
        logger.info("-" * 70)

        for tf in timeframes:
            if tf in all_metrics:
                metrics = all_metrics[tf]
                r2_value = metrics.get('r2', 0.0)  # Use 0.0 as default if r2 is not present
                logger.info(f"{tf:<10} {metrics['mse']:<15.6f} {metrics['rmse']:<15.6f} {metrics['mae']:<15.6f} {r2_value:<15.6f}")
            else:
                logger.info(f"{tf:<10} {'Failed':<15} {'Failed':<15} {'Failed':<15} {'Failed':<15}")

if __name__ == "__main__":
    main()
