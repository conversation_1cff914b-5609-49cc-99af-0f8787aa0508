"""
MT5 Terminal Launcher

This script simply launches an MT5 terminal without connecting to it through the MT5 API,
which should avoid disabling Algo Trading.

Usage:
    python launch_mt5_terminal.py --terminal 1
"""

import os
import argparse
import logging
import subprocess
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MT5 Terminal configurations
MT5_TERMINALS = {
    1: {
        "path": r"C:\Users\<USER>\Desktop\MT5 Pepper 03\terminal64.exe",
        "description": "Pepperstone Demo 1"
    },
    2: {
        "path": r"C:\Users\<USER>\Desktop\MT5 Pepper 02\terminal64.exe",
        "description": "Pepperstone Demo 2"
    },
    3: {
        "path": r"C:\Users\<USER>\Desktop\MT5 IC 01\terminal64.exe",
        "description": "IC Markets Demo 1"
    },
    4: {
        "path": r"C:\Users\<USER>\Desktop\MT5 IC 02\terminal64.exe",
        "description": "IC Markets Demo 2"
    },
    5: {
        "path": r"C:\Users\<USER>\Desktop\MT5 IC 03\terminal64.exe",
        "description": "IC Markets Demo 3"
    }
}

def launch_terminal(terminal_id: int):
    """
    Launch an MT5 terminal without connecting to it through the MT5 API.
    
    Args:
        terminal_id: ID of the terminal to launch (1-5)
    """
    # Get terminal configuration
    if terminal_id not in MT5_TERMINALS:
        logger.error(f"Terminal {terminal_id} not found in configuration")
        return False
    
    terminal_config = MT5_TERMINALS[terminal_id]
    terminal_path = terminal_config["path"]
    
    # Check if terminal path exists
    if not os.path.exists(terminal_path):
        logger.error(f"Terminal path does not exist: {terminal_path}")
        return False
    
    logger.info(f"Launching terminal {terminal_id}: {terminal_config['description']}")
    logger.info(f"Path: {terminal_path}")
    
    try:
        # Launch the terminal directly without using the MT5 API
        process = subprocess.Popen(
            [terminal_path],
            shell=False,  # CRITICAL: Must be False
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        
        logger.info(f"Terminal {terminal_id} launched with process ID: {process.pid}")
        logger.info("Please check Algo Trading status manually in the terminal")
        logger.info("1. Look for the 'Algo Trading' button in the toolbar")
        logger.info("2. If it's not green, click it to enable Algo Trading")
        logger.info("3. Verify that 'Algo Trading enabled' appears in the status bar")
        
        return True
    
    except Exception as e:
        logger.error(f"Error launching terminal {terminal_id}: {str(e)}")
        return False

def main():
    """Main function to launch a specific terminal."""
    parser = argparse.ArgumentParser(description="MT5 Terminal Launcher")
    parser.add_argument("--terminal", type=int, required=True, help="Terminal ID to launch (1-5)")
    parser.add_argument("--all", action="store_true", help="Launch all terminals")
    args = parser.parse_args()
    
    if args.all:
        logger.info("Launching all terminals...")
        for terminal_id in MT5_TERMINALS:
            success = launch_terminal(terminal_id)
            if success:
                logger.info(f"Terminal {terminal_id} launched successfully")
            else:
                logger.error(f"Failed to launch terminal {terminal_id}")
            
            # Wait a bit between launching terminals
            if terminal_id < len(MT5_TERMINALS):
                time.sleep(5)
    else:
        launch_terminal(args.terminal)

if __name__ == "__main__":
    main()
