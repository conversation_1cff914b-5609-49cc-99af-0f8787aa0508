# Comprehensive Trading Bot Guide

This guide provides detailed instructions for setting up, configuring, and using the trading bot system. It covers data collection, model training, and trading execution.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Installation](#installation)
3. [Data Collection](#data-collection)
   - [Collecting Historical Data](#collecting-historical-data)
   - [Verifying Data Quality](#verifying-data-quality)
   - [Data Preprocessing](#data-preprocessing)
4. [Model Training](#model-training)
   - [Training LSTM Models](#training-lstm-models)
   - [Training TFT Models](#training-tft-models)
   - [Training ARIMA Models](#training-arima-models)
   - [Model Comparison](#model-comparison)
5. [Trading Execution](#trading-execution)
   - [Configuration](#configuration)
   - [Running the Trading Bot](#running-the-trading-bot)
   - [Monitoring Performance](#monitoring-performance)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Usage](#advanced-usage)

## System Requirements

- **Operating System**: Windows 10/11 (required for MetaTrader 5)
- **Python**: Version 3.10+ (recommended)
- **Hardware**:
  - CPU: Multi-core processor (4+ cores recommended)
  - RAM: Minimum 8GB (16GB+ recommended)
  - Storage: 10GB+ free space
  - GPU: CUDA-capable NVIDIA GPU (optional but recommended for faster training)
- **Software**:
  - MetaTrader 5 (with active account)
  - Git (for cloning the repository)

## Installation

1. **Clone the Repository**:
   ```bash
   git clone https://github.com/yourusername/trading-bot.git
   cd trading-bot
   ```

2. **Create a Virtual Environment**:
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```

3. **Install Core Dependencies First**:
   ```bash
   pip install numpy==1.23.5
   pip install pmdarima==2.0.3
   pip install tensorflow==2.12.0
   ```

4. **Install Remaining Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

5. **Configure MetaTrader 5**:
   - Install MetaTrader 5 from the [official website](https://www.metatrader5.com/en/download)
   - Create and configure your trading account
   - Enable automated trading (Tools → Options → Expert Advisors → Allow automated trading)
   - Enable DLL imports (Tools → Options → Expert Advisors → Allow DLL imports)

6. **Update Configuration**:
   - Copy `config/config.example.json` to `config/config.json`
   - Update the MT5 terminal paths, login credentials, and server information
   - Adjust model and strategy parameters as needed

## Data Collection

### Collecting Historical Data

The trading bot includes a unified data collection script (`collect_data.py`) that can collect historical data from MetaTrader 5 for multiple symbols and timeframes.

1. **Basic Data Collection**:
   ```bash
   python collect_data.py --symbol BTCUSD.a --timeframe M5 --start-date 2018-01-01 --end-date 2023-12-31
   ```

2. **Collecting Data for Multiple Timeframes**:
   ```bash
   python collect_data.py --symbol BTCUSD.a --timeframes M5,M15,M30,H1,H4 --start-date 2018-01-01 --end-date 2023-12-31
   ```

3. **Collecting Data from All Terminals**:
   ```bash
   python collect_data.py --symbol BTCUSD.a --timeframes M5,M15,M30,H1,H4 --start-date 2018-01-01 --end-date 2023-12-31 --all-terminals
   ```

4. **Collecting Data with Validation**:
   ```bash
   python collect_data.py --symbol BTCUSD.a --timeframes M5,M15,M30,H1,H4 --start-date 2018-01-01 --end-date 2023-12-31 --validate
   ```

### Verifying Data Quality

After collecting data, it's important to verify its quality:

1. **Check for Missing Data**:
   ```bash
   python utils/data_validator.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --check-missing
   ```

2. **Check for Outliers**:
   ```bash
   python utils/data_validator.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --check-outliers
   ```

3. **Visualize Data**:
   ```bash
   python utils/data_visualizer.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet
   ```

### Data Preprocessing

The data preprocessing is handled automatically during model training, but you can also preprocess data separately:

```bash
python utils/data_preprocessor.py --file data/historical/btcusd.a/BTCUSD.a_M5.parquet --output data/processed/BTCUSD.a_M5_processed.parquet
```

## Model Training

The trading bot supports three types of models: LSTM, TFT (Temporal Fusion Transformer), and ARIMA. Each model has its own training script.

### Training LSTM Models

LSTM models are trained using the `train_lstm_single.py` script:

```bash
python train_lstm_single.py --timeframe M5 --feature-columns open,high,low,close,real_volume --target-column close --sequence-length 60 --hidden-units 64 --num-layers 2 --dropout-rate 0.2 --learning-rate 0.001 --epochs 100 --batch-size 32 --use-gpu
```

Key parameters:
- `--timeframe`: The timeframe to train on (M5, M15, M30, H1, H4)
- `--feature-columns`: Comma-separated list of feature columns
- `--target-column`: The column to predict
- `--sequence-length`: Number of time steps to use as input
- `--hidden-units`: Number of hidden units in LSTM layers
- `--num-layers`: Number of LSTM layers
- `--dropout-rate`: Dropout rate for regularization
- `--learning-rate`: Learning rate for optimization
- `--epochs`: Number of training epochs
- `--batch-size`: Batch size for training
- `--use-gpu`: Use GPU for training if available

### Training TFT Models

TFT models are trained using the `train_tft_pytorch.py` script:

```bash
python train_tft_pytorch.py --timeframe M5 --feature-columns open,high,low,close,real_volume --target-column close --sequence-length 60 --hidden-dim 64 --num-heads 4 --num-layers 2 --dropout-rate 0.1 --learning-rate 0.001 --epochs 10 --batch-size 32 --use-gpu
```

Key parameters:
- `--timeframe`: The timeframe to train on (M5, M15, M30, H1, H4)
- `--feature-columns`: Comma-separated list of feature columns
- `--target-column`: The column to predict
- `--sequence-length`: Number of time steps to use as input
- `--hidden-dim`: Hidden dimension for TFT model
- `--num-heads`: Number of attention heads
- `--num-layers`: Number of LSTM layers
- `--dropout-rate`: Dropout rate for regularization
- `--learning-rate`: Learning rate for optimization
- `--epochs`: Number of training epochs
- `--batch-size`: Batch size for training
- `--use-gpu`: Use GPU for training if available
- `--with-arima`: Include ARIMA predictions as features

### Training ARIMA Models

The trading bot supports two types of ARIMA models:

1. **Standard ARIMA**: A statistical time series forecasting method that captures linear temporal dependencies
2. **Ensemble ARIMA**: An advanced approach that combines multiple ARIMA models with feature engineering

ARIMA models are trained using the `train_arima_single.py` script:

```bash
# Train standard ARIMA model
python train_arima_single.py --timeframe M5 --target-column close --auto-arima

# Train ensemble ARIMA model
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --use-ensemble --ensemble-models 5

# Train with data selection options
python train_arima_single.py --timeframe M5 --target-column close --auto-arima --max-rows 50000 --data-selection all
```

Key parameters:
- `--timeframe`: The timeframe to train on (M5, M15, M30, H1, H4)
- `--target-column`: The column to predict
- `--auto-arima`: Use auto_arima to find the best parameters
- `--p`, `--d`, `--q`: ARIMA order parameters (if not using auto_arima)
- `--use-seasonal`: Use seasonal component
- `--seasonal-p`, `--seasonal-d`, `--seasonal-q`, `--seasonal-m`: Seasonal ARIMA parameters
- `--use-ensemble`: Use ensemble ARIMA model for improved performance
- `--ensemble-models`: Number of models in the ensemble (default: 7)
- `--max-rows`: Maximum number of rows to use for training (0 for all data)
- `--data-selection`: Method for selecting data ('recent', 'all', or 'sample')
- `--sample-interval`: Interval for sampling data (only used with --data-selection=sample)

#### ARIMA Model Examples

**Standard ARIMA with Auto Parameter Selection:**
```bash
python train_arima_single.py --timeframe M5 --target-column close --auto-arima
```

**Ensemble ARIMA with All Historical Data:**
```bash
python train_arima_single.py --timeframe M5 --target-column close --use-ensemble --data-selection all
```

**Ensemble ARIMA with Recent Data Only:**
```bash
python train_arima_single.py --timeframe M5 --target-column close --use-ensemble --data-selection recent --max-rows 50000
```

**Standard ARIMA with Specific Parameters:**
```bash
python train_arima_single.py --timeframe M5 --target-column close --p 5 --d 1 --q 5
```

### Training All Models for All Timeframes

To train all models for all timeframes, you can use the following script:

```bash
python train_all_models.py --timeframes M5,M15,M30,H1,H4 --use-gpu
```

### Model Comparison

After training models, you can compare their performance using the `compare_all_models.py` script:

```bash
python compare_all_models.py
```

This will generate comparison tables and visualizations in the `comparison_results` directory.

## Trading Execution

### Configuration

Before running the trading bot, you need to configure it properly:

1. **Update MT5 Terminal Configuration**:
   - Open `config/config.json`
   - Update the `mt5.terminals` section with your terminal paths and credentials
   - Ensure the paths to the terminal executables are correct

2. **Configure Trading Parameters**:
   - Update the `strategy` section in `config/config.json`
   - Set the symbol, timeframes, and other strategy parameters
   - Configure risk management parameters

3. **Configure Model Selection**:
   - Update the `models` section in `config/config.json`
   - Specify which models to use and their weights
   - Configure model-specific parameters

### Running the Trading Bot

To run the trading bot:

```bash
python main.py
```

For independent trading with multiple terminals:

```bash
python independent_trading_bot.py
```

### Stopping the Trading Bot

There are several ways to stop the trading bot:

#### Method 1: Using Keyboard Interrupt (Ctrl+C)

The simplest way to stop the bot is to press `Ctrl+C` in the terminal where the bot is running. The application has signal handlers set up to catch this interrupt and perform a clean shutdown.

#### Method 2: Using the Stop Script

You can use the provided stop script to programmatically stop all bots:

```bash
python stop_bots.py
```

This script will:
1. Initialize all necessary managers
2. Create a TradingBotManager instance
3. Call the stop_all_bots() method to stop all running bots
4. Perform a full shutdown of all resources

#### Method 3: Using the Bot Control Interface

For more control over bot operations, use the bot_control.py script:

```bash
# To stop all bots
python bot_control.py stop

# To stop a specific bot (by terminal ID)
python bot_control.py stop --terminal 1

# To start all bots
python bot_control.py start

# To see all available commands
python bot_control.py --help
```

### Monitoring Performance

The trading bot logs performance metrics and trading activities:

1. **View Logs**:
   ```bash
   tail -f logs/main.log
   ```

2. **Monitor Performance Metrics**:
   ```bash
   python monitoring/monitor.py
   ```

## Troubleshooting

### Common Issues

1. **MT5 Connection Issues**:
   - Ensure MetaTrader 5 is installed and configured correctly
   - Check that the terminal paths in `config/config.json` are correct
   - Verify that your login credentials are valid
   - Make sure automated trading is enabled in MT5

2. **Model Training Issues**:
   - Check that you have sufficient data for training
   - Ensure your GPU is properly configured (if using GPU)
   - Try reducing batch size if you encounter memory issues
   - Check for NaN values in your data

3. **ARIMA Model Issues**:
   - If the model returns zeros, check that the model files exist in the expected location
   - Verify that the model was trained with the correct parameters
   - Check the logs for any errors during model loading
   - Try retraining the model with the `--use-ensemble` flag for better performance
   - Ensure the model files are in one of the supported locations (see documentation)

4. **Trading Execution Issues**:
   - Verify that models are trained and saved correctly
   - Check that the symbol is available in your MT5 terminal
   - Ensure you have sufficient balance for trading
   - Check that the risk parameters are configured correctly

### Getting Help

If you encounter issues not covered in this guide:

1. Check the logs in the `logs/` directory
2. Review the error messages in the console
3. Open an issue on the GitHub repository
4. Contact the maintainers for support

## Advanced Usage

### Custom Model Integration

You can integrate custom models by:

1. Creating a new model class in the `models/` directory
2. Implementing the required methods (train, predict, save, load)
3. Updating the `ModelManager` to support your new model type
4. Adding configuration options in `config/config.json`

### Backtesting

To backtest your strategy:

```bash
python backtest.py --symbol BTCUSD.a --timeframe M5 --start-date 2022-01-01 --end-date 2022-12-31 --strategy default
```

### Hyperparameter Optimization

To optimize model hyperparameters:

```bash
python optimize_hyperparameters.py --model-type lstm --timeframe M5 --n-trials 100
```

---

This guide covers the basic and advanced usage of the trading bot system. For more detailed information on specific components, refer to the other guides in this directory.
