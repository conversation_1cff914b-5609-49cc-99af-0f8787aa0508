"""
MT5 Terminal Launcher utility for automatically starting MT5 terminals.

This module provides functionality to check if MT5 terminals are running
and start them if needed. It supports both regular and portable MT5 installations.
"""

import os
import time
import logging
import subprocess
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

logger = logging.getLogger(__name__)

class MT5TerminalLauncher:
    """
    Utility class for launching and managing MT5 terminals.
    """

    def __init__(self, terminal_paths: Dict[str, str] = None, auto_start: bool = True):
        """
        Initialize the MT5 Terminal Launcher.

        Args:
            terminal_paths: Dictionary mapping terminal IDs to terminal paths
            auto_start: Whether to automatically start terminals when needed
        """
        self.terminal_paths = terminal_paths or {}
        self.auto_start = auto_start
        self.running_terminals: Dict[str, int] = {}  # terminal_id -> process_id

        # Default portable MT5 paths
        self.default_paths = {
            "1": r"C:\Users\<USER>\Desktop\MT5 Pepper 03\terminal64.exe",
            "2": r"C:\Users\<USER>\Desktop\MT5 Pepper 02\terminal64.exe",
            "3": r"C:\Users\<USER>\Desktop\MT5 IC 01\terminal64.exe",
            "4": r"C:\Users\<USER>\Desktop\MT5 IC 02\terminal64.exe",
            "5": r"C:\Users\<USER>\Desktop\MT5 IC 03\terminal64.exe",
        }

        # Update with any provided paths
        if terminal_paths:
            self.default_paths.update(terminal_paths)

        logger.info(f"MT5TerminalLauncher initialized with auto_start={auto_start}")

    def is_terminal_running(self, terminal_id: Union[str, int]) -> bool:
        """
        Check if a specific MT5 terminal is running.

        Args:
            terminal_id: ID of the terminal to check

        Returns:
            bool: True if the terminal is running, False otherwise
        """
        terminal_id_str = str(terminal_id)

        # Check if we have a record of this terminal running
        if terminal_id_str in self.running_terminals:
            pid = self.running_terminals[terminal_id_str]
            try:
                # Check if process is still running
                process = psutil.Process(pid)
                if process.is_running() and "terminal64" in process.name().lower():
                    return True
                else:
                    # Process no longer running or not a terminal
                    del self.running_terminals[terminal_id_str]
            except psutil.NoSuchProcess:
                # Process no longer exists
                del self.running_terminals[terminal_id_str]

        # Check if terminal is running by looking for its process
        terminal_path = self._get_terminal_path(terminal_id_str)
        if not terminal_path:
            logger.warning(f"No path found for terminal {terminal_id_str}")
            return False

        terminal_dir = Path(terminal_path).parent

        # Look for terminal64.exe processes
        for process in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if "terminal64" in process.info['name'].lower():
                    # Check if this is the terminal we're looking for
                    if process.info['cmdline'] and terminal_dir.name in " ".join(process.info['cmdline']):
                        # Found the terminal
                        self.running_terminals[terminal_id_str] = process.info['pid']
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        return False

    def start_terminal(self, terminal_id: Union[str, int]) -> bool:
        """
        Start a specific MT5 terminal.

        Args:
            terminal_id: ID of the terminal to start

        Returns:
            bool: True if the terminal was started successfully, False otherwise
        """
        terminal_id_str = str(terminal_id)

        # Check if terminal is already running
        if self.is_terminal_running(terminal_id_str):
            logger.info(f"Terminal {terminal_id_str} is already running")
            return True

        # Get terminal path
        terminal_path = self._get_terminal_path(terminal_id_str)
        if not terminal_path:
            logger.error(f"No path found for terminal {terminal_id_str}")
            return False

        # Check if path exists
        if not os.path.exists(terminal_path):
            logger.error(f"Terminal path does not exist: {terminal_path}")
            return False

        try:
            # Start the terminal
            logger.info(f"Starting MT5 terminal {terminal_id_str} from {terminal_path}")

            # CRITICAL: The way we start MT5 is crucial for preserving algorithmic trading
            # We need to start it directly without any arguments or shell=True
            # This is the only way to ensure algorithmic trading remains enabled

            # Start the terminal directly without any arguments
            # DO NOT use shell=True as it can cause algorithmic trading to be disabled
            # DO NOT pass any command line arguments
            process = subprocess.Popen(
                [terminal_path],
                shell=False,  # CRITICAL: Must be False to preserve algorithmic trading
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )

            # Wait a bit for the process to start
            time.sleep(10)  # Increased wait time to ensure terminal is fully loaded

            # Check if process is running
            if process.poll() is None:
                # Process is still running
                self.running_terminals[terminal_id_str] = process.pid
                logger.info(f"MT5 terminal {terminal_id_str} started successfully (PID: {process.pid})")
                return True
            else:
                # Process exited
                logger.error(f"MT5 terminal {terminal_id_str} failed to start")
                return False

        except Exception as e:
            logger.error(f"Error starting MT5 terminal {terminal_id_str}: {str(e)}")
            return False

    def stop_terminal(self, terminal_id: Union[str, int]) -> bool:
        """
        Stop a specific MT5 terminal.

        Args:
            terminal_id: ID of the terminal to stop

        Returns:
            bool: True if the terminal was stopped successfully, False otherwise
        """
        terminal_id_str = str(terminal_id)

        # Check if terminal is running
        if not self.is_terminal_running(terminal_id_str):
            logger.info(f"Terminal {terminal_id_str} is not running")
            return True

        try:
            # Get process ID
            pid = self.running_terminals[terminal_id_str]

            # Kill the process
            process = psutil.Process(pid)
            process.terminate()

            # Wait for process to terminate
            gone, still_alive = psutil.wait_procs([process], timeout=5)

            if still_alive:
                # Force kill
                for p in still_alive:
                    p.kill()

            # Remove from running terminals
            del self.running_terminals[terminal_id_str]

            logger.info(f"MT5 terminal {terminal_id_str} stopped successfully")
            return True

        except Exception as e:
            logger.error(f"Error stopping MT5 terminal {terminal_id_str}: {str(e)}")
            return False

    def ensure_terminal_running(self, terminal_id: Union[str, int]) -> bool:
        """
        Ensure a specific MT5 terminal is running, starting it if needed.

        Args:
            terminal_id: ID of the terminal to ensure is running

        Returns:
            bool: True if the terminal is running, False otherwise
        """
        terminal_id_str = str(terminal_id)

        # Check if terminal is already running
        if self.is_terminal_running(terminal_id_str):
            return True

        # Check if auto-start is enabled
        if not self.auto_start:
            logger.warning(f"Auto-start is disabled, terminal {terminal_id_str} is not running")
            return False

        # Start the terminal
        return self.start_terminal(terminal_id_str)

    def start_all_terminals(self) -> Dict[str, bool]:
        """
        Start all configured MT5 terminals.

        Returns:
            Dict[str, bool]: Dictionary mapping terminal IDs to success status
        """
        results = {}

        for terminal_id in self.default_paths.keys():
            results[terminal_id] = self.start_terminal(terminal_id)

        return results

    def stop_all_terminals(self) -> Dict[str, bool]:
        """
        Stop all running MT5 terminals.

        Returns:
            Dict[str, bool]: Dictionary mapping terminal IDs to success status
        """
        results = {}

        for terminal_id in list(self.running_terminals.keys()):
            results[terminal_id] = self.stop_terminal(terminal_id)

        return results

    def get_running_terminals(self) -> List[str]:
        """
        Get a list of running terminal IDs.

        Returns:
            List[str]: List of running terminal IDs
        """
        # Refresh the list of running terminals
        for terminal_id in list(self.default_paths.keys()):
            self.is_terminal_running(terminal_id)

        return list(self.running_terminals.keys())

    def _get_terminal_path(self, terminal_id: str) -> Optional[str]:
        """
        Get the path for a specific terminal.

        Args:
            terminal_id: ID of the terminal

        Returns:
            Optional[str]: Path to the terminal or None if not found
        """
        # Check if terminal ID is in the provided paths
        if terminal_id in self.terminal_paths:
            return self.terminal_paths[terminal_id]

        # Check if terminal ID is in the default paths
        if terminal_id in self.default_paths:
            return self.default_paths[terminal_id]

        # Check if "terminal" + ID is in the provided paths
        terminal_key = f"terminal{terminal_id}"
        if terminal_key in self.terminal_paths:
            return self.terminal_paths[terminal_key]

        # Check if "terminal" + ID is in the default paths
        if terminal_key in self.default_paths:
            return self.default_paths[terminal_key]

        return None

# Create a global instance for convenience
mt5_launcher = MT5TerminalLauncher()

def ensure_terminal_running(terminal_id: Union[str, int]) -> bool:
    """
    Ensure a specific MT5 terminal is running, starting it if needed.

    Args:
        terminal_id: ID of the terminal to ensure is running

    Returns:
        bool: True if the terminal is running, False otherwise
    """
    return mt5_launcher.ensure_terminal_running(terminal_id)

def start_terminal(terminal_id: Union[str, int]) -> bool:
    """
    Start a specific MT5 terminal.

    Args:
        terminal_id: ID of the terminal to start

    Returns:
        bool: True if the terminal was started successfully, False otherwise
    """
    return mt5_launcher.start_terminal(terminal_id)

def stop_terminal(terminal_id: Union[str, int]) -> bool:
    """
    Stop a specific MT5 terminal.

    Args:
        terminal_id: ID of the terminal to stop

    Returns:
        bool: True if the terminal was stopped successfully, False otherwise
    """
    return mt5_launcher.stop_terminal(terminal_id)

def is_terminal_running(terminal_id: Union[str, int]) -> bool:
    """
    Check if a specific MT5 terminal is running.

    Args:
        terminal_id: ID of the terminal to check

    Returns:
        bool: True if the terminal is running, False otherwise
    """
    return mt5_launcher.is_terminal_running(terminal_id)

def start_all_terminals() -> Dict[str, bool]:
    """
    Start all configured MT5 terminals.

    Returns:
        Dict[str, bool]: Dictionary mapping terminal IDs to success status
    """
    return mt5_launcher.start_all_terminals()

def stop_all_terminals() -> Dict[str, bool]:
    """
    Stop all running MT5 terminals.

    Returns:
        Dict[str, bool]: Dictionary mapping terminal IDs to success status
    """
    return mt5_launcher.stop_all_terminals()

def get_running_terminals() -> List[str]:
    """
    Get a list of running terminal IDs.

    Returns:
        List[str]: List of running terminal IDs
    """
    return mt5_launcher.get_running_terminals()
