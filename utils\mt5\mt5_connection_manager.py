"""
Improved MT5 connection manager for handling all MT5-related operations.
Provides centralized connection management with proper error handling and resource cleanup.
Ensures Algo Trading functionality is preserved by avoiding unnecessary shutdowns.
"""
import logging
import time
import MetaTrader5 as mt5
from typing import Dict, Optional, Tuple, Any, Union
from threading import Lock
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

# Import MT5 Terminal Launcher
try:
    import utils.mt5_launcher
    HAS_MT5_LAUNCHER = True
except ImportError:
    HAS_MT5_LAUNCHER = False
    logging.warning("MT5 Terminal Launcher not available. Automatic terminal startup disabled.")

logger = logging.getLogger(__name__)

@dataclass
class MT5Connection:
    """Represents an MT5 connection with its state and metadata."""
    terminal_id: str  # Using string for consistency
    path: str
    login: Union[str, int]
    password: str
    server: str
    portable: bool = True     # CRITICAL: Must be True to preserve algorithmic trading in other terminals
    trade_mode: bool = True   # Enable trading
    auto_trading: bool = True # Enable auto trading
    is_connected: bool = False
    last_health_check: Optional[datetime] = None
    retry_count: int = 0
    last_error: Optional[str] = None
    connection_time: Optional[datetime] = None

    def __post_init__(self):
        """Convert login to int if it's a string."""
        if isinstance(self.login, str) and self.login.isdigit():
            self.login = int(self.login)

class MT5ConnectionManager:
    """
    Manages MT5 connections with proper error handling and resource management.
    Implements connection pooling and ensures Algo Trading functionality is preserved.
    """

    def __init__(self, config_manager, max_retries: int = 3, retry_delay: int = 5, health_check_interval: int = 60):
        """
        Initialize the MT5 connection manager.

        Args:
            config_manager: Configuration manager instance
            max_retries: Maximum number of connection retries
            retry_delay: Delay between retries in seconds
            health_check_interval: Interval between health checks in seconds
        """
        self.config_manager = config_manager
        self._connections: Dict[str, MT5Connection] = {}
        self._lock = Lock()
        self._terminal_locks: Dict[str, Lock] = {}
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self._health_check_interval = health_check_interval
        self._active_connection: Optional[str] = None

        # Get auto_start setting from config
        mt5_config = self.config_manager.get_mt5_config()
        self.auto_start = getattr(mt5_config, 'auto_start_terminals', True)

        logger.info("MT5ConnectionManager initialized")

    def get_connection(self, terminal_id: Union[str, int], force_new: bool = False) -> Optional[MT5Connection]:
        """
        Get or create an MT5 connection with proper error handling.

        Args:
            terminal_id: ID of the MT5 terminal (string or int)
            force_new: Whether to force a new connection even if one exists

        Returns:
            Optional[MT5Connection]: Connection object if successful, None otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            # Check if connection exists and is healthy
            if not force_new and terminal_id_str in self._connections:
                conn = self._connections[terminal_id_str]
                if self._is_connection_healthy(conn):
                    self._active_connection = terminal_id_str
                    return conn

            # Get terminal configuration
            mt5_config = self.config_manager.get_mt5_config()
            if not mt5_config or not hasattr(mt5_config, 'terminals'):
                logger.error(f"MT5 configuration not found for terminal {terminal_id_str}")
                return None

            terminal_config = mt5_config.terminals.get(terminal_id_str)
            if not terminal_config:
                logger.error(f"Terminal configuration not found for terminal {terminal_id_str}")
                return None

            # Initialize or reinitialize connection
            success, conn = self._initialize_connection(terminal_id_str, terminal_config)
            if success:
                self._active_connection = terminal_id_str
                return conn
            return None

    def _is_connection_healthy(self, conn: MT5Connection) -> bool:
        """
        Check if a connection is healthy and if Algo Trading is enabled.

        Args:
            conn: MT5Connection object

        Returns:
            bool: True if connection is healthy and Algo Trading is enabled, False otherwise
        """
        if not conn.is_connected:
            return False

        current_time = datetime.now()
        if (conn.last_health_check is None or
            (current_time - conn.last_health_check).total_seconds() > self._health_check_interval):
            # Perform health check
            try:
                # Check if MT5 is already initialized
                terminal_info = mt5.terminal_info()
                if not terminal_info:
                    conn.is_connected = False
                    conn.last_error = "Terminal info check failed"
                    logger.warning(f"MT5 connection health check failed for terminal {conn.terminal_id}")
                    return False

                # Check if Algo Trading is enabled
                # IMPORTANT: We use terminal_info.trade_allowed instead of account_info.trade_expert
                # because trade_allowed directly reflects the state of the Algo Trading button
                if not terminal_info.trade_allowed:
                    conn.is_connected = True  # Connection is still active
                    logger.warning(f"[WARNING] Algorithmic trading is DISABLED for terminal {conn.terminal_id}")
                    logger.warning(f"Please enable Algorithmic trading in the MT5 terminal {conn.terminal_id}")
                    logger.warning(f"1. Open the terminal at {conn.path}")
                    logger.warning(f"2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                    logger.warning(f"3. Verify that 'Algo Trading enabled' appears in the status bar")
                    # We don't return False here because the connection is still active
                    # The trading bot should handle this situation appropriately

                # Update health check timestamp
                conn.last_health_check = current_time
                return True
            except Exception as e:
                conn.is_connected = False
                conn.last_error = str(e)
                logger.error(f"Error during MT5 connection health check for terminal {conn.terminal_id}: {str(e)}")
                return False
        return True

    def _initialize_connection(self, terminal_id: str, terminal_config: Any) -> Tuple[bool, Optional[MT5Connection]]:
        """
        Initialize a new MT5 connection with retry logic.

        Args:
            terminal_id: ID of the MT5 terminal
            terminal_config: Terminal configuration object

        Returns:
            Tuple[bool, Optional[MT5Connection]]: (success, connection)
        """
        # Get or create connection object
        conn = self._connections.get(terminal_id)
        if not conn:
            conn = MT5Connection(
                terminal_id=terminal_id,
                path=terminal_config.path,
                login=terminal_config.login,
                password=terminal_config.password,
                server=terminal_config.server,
                portable=True,     # CRITICAL: Must be True to preserve algorithmic trading in other terminals
                trade_mode=getattr(terminal_config, 'trade_mode', True),
                auto_trading=getattr(terminal_config, 'auto_trading', True)
            )

        # Check if we've exceeded max retries
        if conn.retry_count >= self._max_retries:
            error_msg = f"Max retries ({self._max_retries}) exceeded for terminal {terminal_id}"
            logger.error(error_msg)
            return False, None

        try:
            # Check if MT5 is already initialized
            if mt5.terminal_info():
                # MT5 is already initialized
                if self._active_connection and self._active_connection != terminal_id:
                    # Different terminal, need to shutdown and reinitialize
                    # This is a critical section that can disable algorithmic trading
                    # We need to be very careful here
                    logger.warning(f"Switching from terminal {self._active_connection} to {terminal_id}")
                    logger.warning("This operation may disable algorithmic trading in the current terminal")

                    # Try to ensure the new terminal is running before shutting down the current one
                    if HAS_MT5_LAUNCHER and self.auto_start:
                        try:
                            from utils.mt5_launcher import start_terminal
                            terminal_running = start_terminal(terminal_id)
                            if not terminal_running:
                                logger.warning(f"Could not start terminal {terminal_id} before switching")
                        except Exception as e:
                            logger.warning(f"Error starting terminal {terminal_id}: {str(e)}")

                    # CRITICAL: We need to be very careful with shutting down MT5
                    # This is the main cause of algorithmic trading being disabled
                    # Instead of shutting down, we'll try to reuse the connection if possible

                    # Check if the new terminal is already running before shutting down
                    terminal_running = False
                    if HAS_MT5_LAUNCHER:
                        try:
                            from utils.mt5_launcher import is_terminal_running
                            terminal_running = is_terminal_running(terminal_id)
                        except Exception as e:
                            logger.warning(f"Error checking if terminal {terminal_id} is running: {str(e)}")

                    # NEVER shutdown MT5 as it disables Algo Trading
                    # Instead, reinitialize with portable=True to preserve settings
                    logger.warning(f"Reinitializing with portable=True to switch to terminal {terminal_id}")
                    mt5.initialize(portable=True)
                    time.sleep(1)  # Give a moment for settings to apply
                else:
                    # Same terminal or no active connection, just update connection object
                    conn.is_connected = True
                    conn.last_health_check = datetime.now()
                    conn.retry_count = 0
                    conn.last_error = None
                    conn.connection_time = datetime.now()
                    self._connections[terminal_id] = conn
                    logger.info(f"MT5 already initialized for terminal {terminal_id}")
                    return True, conn

            # Initialize new connection
            logger.info(f"Initializing MT5 connection for terminal {terminal_id}")

            # Try to ensure terminal is running if MT5 Terminal Launcher is available
            mt5_config = self.config_manager.get_mt5_config()
            auto_start = getattr(mt5_config, 'auto_start_terminals', True)
            # Make auto_start available to the outer scope
            self.auto_start = auto_start

            if HAS_MT5_LAUNCHER and auto_start:
                logger.info(f"Attempting to start MT5 terminal {terminal_id} if not running")
                try:
                    from utils.mt5_launcher import start_terminal
                    terminal_started = start_terminal(terminal_id)
                    if not terminal_started:
                        logger.warning(f"Could not start MT5 terminal {terminal_id}")
                except Exception as e:
                    logger.warning(f"Error starting MT5 terminal {terminal_id}: {str(e)}")
            elif not HAS_MT5_LAUNCHER and auto_start:
                logger.warning(f"MT5 Terminal Launcher not available, but auto_start_terminals is enabled")
            elif not auto_start:
                logger.info(f"Auto-start terminals is disabled, skipping terminal startup")

            # Ensure path exists
            path = Path(conn.path)
            if not path.exists():
                error_msg = f"MT5 terminal path not found: {conn.path}"
                logger.error(error_msg)
                conn.last_error = error_msg
                conn.retry_count += 1
                return False, None

            # Initialize MT5 with full credentials for proper authentication
            # Use portable=True to preserve algorithmic trading settings
            if not mt5.initialize(
                path=conn.path,
                login=conn.login,
                password=conn.password,
                server=conn.server,
                portable=True  # CRITICAL: Must be True to preserve algorithmic trading
            ):
                error = mt5.last_error()
                conn.last_error = str(error)
                conn.retry_count += 1
                logger.error(f"Failed to initialize MT5 for terminal {terminal_id}: {error}")
                return False, None

            # Test connection
            if not mt5.terminal_info():
                error = "Terminal info check failed"
                conn.last_error = error
                conn.retry_count += 1
                logger.error(f"MT5 connection test failed for terminal {terminal_id}")
                return False, None

            # Connection successful
            conn.is_connected = True
            conn.last_health_check = datetime.now()
            conn.retry_count = 0
            conn.last_error = None
            conn.connection_time = datetime.now()
            self._connections[terminal_id] = conn

            logger.info(f"MT5 connection established for terminal {terminal_id}")
            return True, conn

        except Exception as e:
            conn.last_error = str(e)
            conn.retry_count += 1
            logger.error(f"Error initializing MT5 connection for terminal {terminal_id}: {str(e)}")
            return False, None

    def shutdown(self, terminal_id: Union[str, int]) -> bool:
        """
        Shutdown an MT5 connection with warnings about Algo Trading impact.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            bool: True if shutdown successful, False otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            if terminal_id_str in self._connections:
                conn = self._connections[terminal_id_str]
                try:
                    if conn.is_connected:
                        # Only shutdown if this is the active connection
                        if self._active_connection == terminal_id_str:
                            # Check if Algo Trading is enabled before shutting down
                            try:
                                terminal_info = mt5.terminal_info()
                                if terminal_info and terminal_info.trade_allowed:
                                    logger.warning(f"[WARNING] Terminal {terminal_id_str} has Algo Trading ENABLED")
                                    logger.warning(f"[WARNING] Shutting down will disable Algo Trading in this terminal")
                                    logger.warning(f"Consider using disconnect() instead if you need to maintain Algo Trading")
                            except Exception:
                                # Ignore errors during check
                                pass

                            logger.info(f"Disconnecting MT5 connection for terminal {terminal_id_str} without shutdown")
                            # DO NOT call mt5.shutdown() as it disables Algo Trading
                            # Just reinitialize with portable=True to preserve settings
                            mt5.initialize(portable=True)
                            self._active_connection = None

                            # Provide instructions for re-enabling Algo Trading
                            logger.warning(f"[WARNING] To re-enable Algo Trading for terminal {terminal_id_str}:")
                            logger.warning(f"1. Open the terminal at {conn.path}")
                            logger.warning(f"2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                            logger.warning(f"3. Verify that 'Algo Trading enabled' appears in the status bar")
                        conn.is_connected = False
                    del self._connections[terminal_id_str]
                    logger.info(f"MT5 connection removed for terminal {terminal_id_str}")
                    return True
                except Exception as e:
                    logger.error(f"Error shutting down MT5 connection for terminal {terminal_id_str}: {str(e)}")
                    return False
            return True

    def disconnect(self, terminal_id: Union[str, int]) -> bool:
        """
        Disconnect from an MT5 connection without shutting down MT5.
        This preserves Algo Trading settings in the terminal.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            bool: True if disconnection successful, False otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            if terminal_id_str in self._connections:
                conn = self._connections[terminal_id_str]
                try:
                    # Just mark as disconnected without shutting down MT5
                    conn.is_connected = False

                    # Reset active connection if this was the active one
                    if self._active_connection == terminal_id_str:
                        self._active_connection = None

                    # Remove from connections dictionary
                    del self._connections[terminal_id_str]
                    logger.info(f"MT5 connection to terminal {terminal_id_str} disconnected (without shutdown)")
                    logger.info(f"Algo Trading settings in terminal {terminal_id_str} are preserved")
                    return True
                except Exception as e:
                    logger.error(f"Error disconnecting from terminal {terminal_id_str}: {str(e)}")
                    return False
            return True

    def shutdown_all(self) -> None:
        """Shutdown all MT5 connections with minimal impact on Algo Trading."""
        with self._lock:
            if self._active_connection:
                try:
                    # IMPORTANT: We need to be extremely careful with shutting down MT5
                    # This can disable algorithmic trading in all terminals
                    # Only do this at the end of the program or when absolutely necessary
                    logger.info("Shutting down all MT5 connections")
                    logger.warning("[WARNING] This operation may disable algorithmic trading in all terminals")
                    logger.warning("Only proceed if you're exiting the application completely")

                    # Check if any terminal has Algo Trading enabled
                    algo_trading_enabled = False
                    for terminal_id_str, conn in self._connections.items():
                        try:
                            # Only check if the connection is active
                            if conn.is_connected:
                                terminal_info = mt5.terminal_info()
                                if terminal_info and terminal_info.trade_allowed:
                                    algo_trading_enabled = True
                                    logger.warning(f"[WARNING] Terminal {terminal_id_str} has Algo Trading ENABLED")
                                    logger.warning(f"[WARNING] Shutting down will disable Algo Trading in this terminal")
                        except Exception:
                            # Ignore errors during check
                            pass

                    if algo_trading_enabled:
                        logger.warning("[WARNING] CRITICAL WARNING: Algo Trading is currently ENABLED in at least one terminal")
                        logger.warning("[WARNING] Shutting down will disable Algo Trading and require manual re-enabling")
                        logger.warning("[WARNING] Consider using disconnect_all() instead if you need to maintain Algo Trading")

                    # Instead of immediately shutting down, we'll mark connections as disconnected first
                    for terminal_id_str, conn in self._connections.items():
                        conn.is_connected = False

                    # DO NOT call mt5.shutdown() as it disables Algo Trading
                    # Just reinitialize with portable=True to preserve settings
                    logger.info("Reinitializing with portable=True to preserve Algo Trading")
                    mt5.initialize(portable=True)
                    self._active_connection = None

                    # Provide instructions for re-enabling Algo Trading
                    if algo_trading_enabled:
                        logger.warning("[WARNING] To re-enable Algo Trading after shutdown:")
                        logger.warning("1. Open each MT5 terminal manually")
                        logger.warning("2. Click the 'Algo Trading' button in the toolbar (it should turn green)")
                        logger.warning("3. Verify that 'Algo Trading enabled' appears in the status bar")

                except Exception as e:
                    logger.error(f"Error shutting down all MT5 connections: {str(e)}")

            # Clear connections dictionary
            self._connections.clear()

    def disconnect_all(self) -> None:
        """Disconnect from all MT5 connections without shutting down MT5.
        This preserves Algo Trading settings in all terminals."""
        with self._lock:
            logger.info("Disconnecting from all MT5 connections without shutdown")
            logger.info("This preserves Algo Trading settings in all terminals")

            # Mark all connections as disconnected
            for terminal_id_str, conn in self._connections.items():
                conn.is_connected = False
                logger.info(f"Marked connection to terminal {terminal_id_str} as disconnected")

            # Reset active connection
            self._active_connection = None

            # Clear connections dictionary
            self._connections.clear()

    def get_connection_status(self, terminal_id: Union[str, int]) -> Optional[MT5Connection]:
        """
        Get the status of an MT5 connection.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            Optional[MT5Connection]: Connection status if exists, None otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            return self._connections.get(terminal_id_str)

    def get_all_connections(self) -> Dict[str, MT5Connection]:
        """
        Get all MT5 connections.

        Returns:
            Dict[str, MT5Connection]: Dictionary of all connections
        """
        with self._lock:
            return self._connections.copy()

    def get_active_connection_id(self) -> Optional[str]:
        """
        Get the ID of the active MT5 connection.

        Returns:
            Optional[str]: ID of the active connection if any, None otherwise
        """
        with self._lock:
            return self._active_connection

    def _get_terminal_lock(self, terminal_id: str) -> Lock:
        """
        Get a lock for a specific terminal.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            Lock: Lock for the terminal
        """
        with self._lock:
            if terminal_id not in self._terminal_locks:
                self._terminal_locks[terminal_id] = Lock()
            return self._terminal_locks[terminal_id]

    def execute_mt5_operation(self, terminal_id: Union[str, int], operation_func, *args, **kwargs):
        """
        Thread-safe execution of MT5 operations.

        Args:
            terminal_id: ID of the MT5 terminal
            operation_func: Function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Result of the operation or None if failed
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        # Get connection
        conn = self.get_connection(terminal_id_str)
        if not conn or not conn.is_connected:
            logger.error(f"Cannot execute operation: No connection for terminal {terminal_id_str}")
            return None

        # Acquire lock for this terminal
        terminal_lock = self._get_terminal_lock(terminal_id_str)
        with terminal_lock:
            try:
                return operation_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"MT5 operation failed: {str(e)}")
                return None

    def thread_safe_mt5_operation(self, terminal_id: Union[str, int]):
        """
        Decorator for thread-safe MT5 operations.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            Decorated function
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                return self.execute_mt5_operation(terminal_id, func, *args, **kwargs)
            return wrapper
        return decorator

    def reconnect(self, terminal_id: Union[str, int]) -> bool:
        """
        Reconnect to an MT5 terminal while preserving Algo Trading settings.

        Args:
            terminal_id: ID of the MT5 terminal

        Returns:
            bool: True if reconnection successful, False otherwise
        """
        # Use standardized terminal ID normalization
        try:
            from utils.common import normalize_terminal_id
            terminal_id_str = normalize_terminal_id(terminal_id)
        except ImportError:
            # Fallback to simple string conversion if common utils not available
            terminal_id_str = str(terminal_id)

        with self._lock:
            # Check if terminal exists in configuration
            mt5_config = self.config_manager.get_mt5_config()
            if not mt5_config or not hasattr(mt5_config, 'terminals'):
                logger.error(f"MT5 configuration not found for terminal {terminal_id_str}")
                return False

            terminal_config = mt5_config.terminals.get(terminal_id_str)
            if not terminal_config:
                logger.error(f"Terminal configuration not found for terminal {terminal_id_str}")
                return False

            # Check if Algo Trading is enabled before reconnecting
            algo_trading_enabled = False
            if terminal_id_str in self._connections and self._connections[terminal_id_str].is_connected:
                try:
                    terminal_info = mt5.terminal_info()
                    if terminal_info and terminal_info.trade_allowed:
                        algo_trading_enabled = True
                        logger.info(f"Algo Trading is currently ENABLED for terminal {terminal_id_str}")
                        logger.info(f"Will attempt to preserve this setting during reconnection")
                except Exception:
                    # Ignore errors during check
                    pass

            # Use disconnect instead of shutdown to preserve Algo Trading
            if algo_trading_enabled:
                logger.info(f"Using disconnect instead of shutdown to preserve Algo Trading")
                self.disconnect(terminal_id_str)
            else:
                # Shutdown existing connection
                self.shutdown(terminal_id_str)

            # Wait before reconnecting
            time.sleep(self._retry_delay)

            # Initialize new connection with portable=True to preserve Algo Trading
            success, _ = self._initialize_connection(terminal_id_str, terminal_config)

            if success:
                logger.info(f"Successfully reconnected to terminal {terminal_id_str}")

                # Check if Algo Trading was preserved
                if algo_trading_enabled:
                    try:
                        terminal_info = mt5.terminal_info()
                        if terminal_info and terminal_info.trade_allowed:
                            logger.info(f"✅ Algo Trading was successfully preserved for terminal {terminal_id_str}")
                        else:
                            logger.warning(f"⚠️ Algo Trading was DISABLED during reconnection for terminal {terminal_id_str}")
                            logger.warning(f"Please manually re-enable Algo Trading in the terminal")
                    except Exception:
                        # Ignore errors during check
                        pass

                return True
            else:
                logger.error(f"Failed to reconnect to terminal {terminal_id_str}")
                return False
