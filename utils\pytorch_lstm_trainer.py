"""
PyTorch LSTM Trainer

This module provides utilities for training LSTM models using PyTorch.
"""

import logging
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from typing import Dict, List, Optional, Any
from pathlib import Path
# Configure logging
logger = logging.getLogger(__name__)

# Import required modules
from utils.model_trainer import ModelTrainer
from config.unified_config import UnifiedConfigManager
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer as PyTorchLSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

class PyTorchLSTMModelTrainer(ModelTrainer):
    """Class for training LSTM models using PyTorch."""

    def __init__(self, config_manager: Optional[UnifiedConfigManager] = None):
        """
        Initialize the PyTorchLSTMModelTrainer.

        Args:
            config_manager: Configuration manager instance
        """
        super().__init__(config_manager)

    def train(
        self,
        symbol: str,
        timeframe: str,
        model_name: str = 'lstm',
        sequence_length: int = 60,
        feature_columns: Optional[List[str]] = None,
        target_column: str = 'close',
        test_size: float = 0.2,
        random_state: int = 42,
        epochs: int = 100,
        batch_size: int = 32,
        validation_split: float = 0.1,
        hidden_units: int = 50,
        num_layers: int = 2,
        dropout_rate: float = 0.2,
        learning_rate: float = 0.001,
        file_format: str = 'csv',
        data_dir: Optional[str] = None,
        use_gpu: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Train an LSTM model using PyTorch.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            model_name: Name of the model
            sequence_length: Length of input sequences
            feature_columns: Columns to use as features (if None, use OHLCV)
            target_column: Column to use as target
            test_size: Proportion of data to use for testing
            random_state: Random state for reproducibility
            epochs: Number of training epochs
            batch_size: Batch size for training
            validation_split: Proportion of training data to use for validation
            hidden_units: Number of hidden units in LSTM layers
            num_layers: Number of LSTM layers
            dropout_rate: Dropout rate
            learning_rate: Learning rate
            file_format: File format ('csv' or 'parquet')
            data_dir: Data directory (if None, use config)
            use_gpu: Whether to use GPU for training

        Returns:
            Dictionary with training results or None if training failed
        """
        try:
            # Configure GPU
            gpu_info = get_gpu_info()
            if gpu_info['gpu_available'] and use_gpu:
                logger.info(f"GPU is available: {gpu_info['gpu_devices']}")
                configure_gpu_memory()
                device = select_device(use_gpu=True)
                logger.info(f"Using device: {device}")
            else:
                if not gpu_info['gpu_available']:
                    logger.warning("No GPU available, using CPU for training")
                elif not use_gpu:
                    logger.warning("GPU available but use_gpu=False, using CPU for training")
                device = torch.device('cpu')

            # Load data
            df = self.load_data(symbol, timeframe, file_format, data_dir)
            if df is None:
                return None

            # Preprocess data
            X_train, X_test, y_train, y_test, X_scaler, y_scaler = self.preprocess_data(
                df, sequence_length, target_column, feature_columns, test_size, random_state
            )

            # Convert data to PyTorch tensors
            X_train_tensor = torch.FloatTensor(X_train)
            y_train_tensor = torch.FloatTensor(y_train)
            X_test_tensor = torch.FloatTensor(X_test)
            y_test_tensor = torch.FloatTensor(y_test)

            # Create datasets and data loaders
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

            # Split training data for validation
            train_size = int((1 - validation_split) * len(train_dataset))
            val_size = len(train_dataset) - train_size
            train_dataset, val_dataset = torch.utils.data.random_split(
                train_dataset, [train_size, val_size],
                generator=torch.Generator().manual_seed(random_state)
            )

            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size)
            test_loader = DataLoader(test_dataset, batch_size=batch_size)

            # Get input shape
            _, timesteps, features = X_train.shape

            # Create model
            model = LSTMModel(
                input_dim=features,
                hidden_dim=hidden_units,
                num_layers=num_layers,
                output_dim=1,
                dropout_rate=dropout_rate
            )

            # Create trainer
            trainer = PyTorchLSTMTrainer(
                model=model,
                learning_rate=learning_rate,
                device=device
            )

            # Train model
            logger.info(f"Training PyTorch LSTM model for {symbol} {timeframe} on {device}")
            history = trainer.train(
                train_loader=train_loader,
                val_loader=val_loader,
                epochs=epochs,
                patience=10,
                verbose=True
            )

            # Evaluate model
            test_loss = trainer.evaluate(test_loader)
            logger.info(f"Test loss: {test_loss}")

            # Make predictions
            y_pred = trainer.predict(test_loader)

            # Inverse transform predictions and actual values
            y_pred_inv = y_scaler.inverse_transform(y_pred)
            y_test_inv = y_scaler.inverse_transform(y_test)

            # Calculate metrics
            mse = np.mean((y_pred_inv - y_test_inv) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y_pred_inv - y_test_inv))

            # Calculate R² (coefficient of determination)
            y_mean = np.mean(y_test_inv)
            ss_total = np.sum((y_test_inv - y_mean) ** 2)
            ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
            r2 = 1 - (ss_residual / ss_total)

            logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

            # Save model
            model_dir = Path(self.model_dir) / f"{model_name}_{symbol}_{timeframe}"
            model_dir.mkdir(parents=True, exist_ok=True)
            model.save(str(model_dir))

            # Save scalers
            scalers = {'X_scaler': X_scaler, 'y_scaler': y_scaler}
            torch.save(scalers, model_dir / "scalers.pt")

            # Save configuration
            config = {
                'model_type': 'pytorch_lstm',
                'symbol': symbol,
                'timeframe': timeframe,
                'sequence_length': sequence_length,
                'feature_columns': feature_columns,
                'target_column': target_column,
                'hidden_units': hidden_units,
                'num_layers': num_layers,
                'dropout_rate': dropout_rate,
                'learning_rate': learning_rate,
                'input_shape': (None, timesteps, features),
                'device': str(device),
                'gpu_available': gpu_info['gpu_available'],
                'gpu_used': gpu_info['gpu_available'] and use_gpu,
                'pytorch_version': torch.__version__,
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2)
                }
            }

            # Save configuration as JSON
            with open(model_dir / "config.json", "w") as f:
                import json
                json.dump(config, f, indent=4)

            logger.info(f"Model saved to {model_dir}")

            return {
                'model': model,
                'history': history,
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2)
                },
                'config': config
            }

        except Exception as e:
            logger.error(f"Error training PyTorch LSTM model: {str(e)}", exc_info=True)
            return None
