"""
Utility for splitting data into training, validation, and test sets.
"""

from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

from config.unified_config import UnifiedConfigManager as ConfigurationManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_splitting.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataSplitter:
    """Class for splitting data into training, validation, and test sets."""

    def __init__(self,
                 config_manager: ConfigurationManager,
                 source_subdir: str = "raw",
                 train_subdir: str = "training",
                 val_subdir: str = "validation",
                 test_subdir: str = "test",
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 test_ratio: float = 0.15):
        """Initialize the data splitter using base path from config."""
        self.config_manager = config_manager
        base_path = Path(self.config_manager.get_data_base_path())

        self.source_dir = base_path / source_subdir
        self.train_dir = base_path / train_subdir
        self.val_dir = base_path / val_subdir
        self.test_dir = base_path / test_subdir

        # Validate ratios
        if not np.isclose(train_ratio + val_ratio + test_ratio, 1.0):
            raise ValueError("Ratios must sum to 1.0")

        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio

        # Create output directories
        for directory in [self.train_dir, self.val_dir, self.test_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def _get_file_groups(self) -> Dict[str, List[Path]]:
        """Group files by symbol and timeframe."""
        file_groups = {}
        for file_path in self.source_dir.glob("*.parquet"):
            # Extract symbol and timeframe from filename
            parts = file_path.stem.split('_')
            if len(parts) >= 2:
                symbol = parts[0]
                timeframe = parts[1]
                key = f"{symbol}_{timeframe}"
                if key not in file_groups:
                    file_groups[key] = []
                file_groups[key].append(file_path)
        return file_groups

    def _split_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Split a dataframe into train, validation, and test sets."""
        # Sort by time
        df = df.sort_index()

        # Calculate split points
        total_rows = len(df)
        train_end = int(total_rows * self.train_ratio)
        val_end = train_end + int(total_rows * self.val_ratio)

        # Split the data
        train_df = df.iloc[:train_end]
        val_df = df.iloc[train_end:val_end]
        test_df = df.iloc[val_end:]

        return train_df, val_df, test_df

    def split_data(self) -> None:
        """Split all data files into training, validation, and test sets."""
        file_groups = self._get_file_groups()

        for group_key, files in file_groups.items():
            logger.info(f"Processing group: {group_key}")

            # Read and concatenate all files in the group
            dfs = []
            for file_path in files:
                try:
                    df = pd.read_parquet(file_path)
                    dfs.append(df)
                except Exception as e:
                    logger.error(f"Error reading {file_path}: {str(e)}")
                    continue

            if not dfs:
                logger.warning(f"No valid data found for group {group_key}")
                continue

            # Combine all dataframes
            combined_df = pd.concat(dfs, axis=0)

            # Remove duplicates and sort
            combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
            combined_df = combined_df.sort_index()

            # Split the data
            train_df, val_df, test_df = self._split_dataframe(combined_df)

            # Save the splits
            for df, directory in [(train_df, self.train_dir),
                                (val_df, self.val_dir),
                                (test_df, self.test_dir)]:
                if len(df) > 0:
                    output_path = directory / f"{group_key}.parquet"
                    df.to_parquet(output_path)
                    logger.info(f"Saved {len(df)} rows to {output_path}")

    def verify_splits(self) -> bool:
        """Verify that the data splits are valid."""
        try:
            # Check that all directories exist
            for directory in [self.train_dir, self.val_dir, self.test_dir]:
                if not directory.exists():
                    logger.error(f"Directory not found: {directory}")
                    return False

            # Get all files in source directory
            source_files = set(f.stem.split('_terminal')[0] for f in self.source_dir.glob("*.parquet"))

            # Check that all files are present in each split
            for split_dir in [self.train_dir, self.val_dir, self.test_dir]:
                split_files = set(f.stem for f in split_dir.glob("*.parquet"))
                missing_files = source_files - split_files
                if missing_files:
                    logger.error(f"Missing files in {split_dir}: {missing_files}")
                    return False

            # Check for data leakage
            train_files = set(f.stem for f in self.train_dir.glob("*.parquet"))

            # Check for overlapping time periods
            for symbol_timeframe in train_files:
                train_df = pd.read_parquet(self.train_dir / f"{symbol_timeframe}.parquet")
                val_df = pd.read_parquet(self.val_dir / f"{symbol_timeframe}.parquet")
                test_df = pd.read_parquet(self.test_dir / f"{symbol_timeframe}.parquet")

                # Check for time overlap
                train_end = train_df.index.max()
                val_start = val_df.index.min()
                if train_end >= val_start:
                    logger.error(f"Time overlap between train and validation for {symbol_timeframe}")
                    return False

                val_end = val_df.index.max()
                test_start = test_df.index.min()
                if val_end >= test_start:
                    logger.error(f"Time overlap between validation and test for {symbol_timeframe}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error verifying splits: {str(e)}")
            return False

    def get_split_statistics(self) -> Dict[str, Dict[str, int]]:
        """Get statistics about the data splits."""
        stats = {}

        for split_dir in [self.train_dir, self.val_dir, self.test_dir]:
            split_name = split_dir.name
            stats[split_name] = {}

            for file_path in split_dir.glob("*.parquet"):
                try:
                    df = pd.read_parquet(file_path)
                    stats[split_name][file_path.stem] = {
                        "rows": len(df),
                        "start_date": df.index.min(),
                        "end_date": df.index.max()
                    }
                except Exception as e:
                    logger.error(f"Error reading {file_path}: {str(e)}")

        return stats

def main():
    """Main function to run data splitting."""
    # Initialize config manager first
    config = ConfigurationManager()

    # Pass config manager to DataSplitter
    # Assumes raw data is in {data_base_path}/raw/
    splitter = DataSplitter(config_manager=config,
                            source_subdir="raw",
                            train_subdir="training",
                            val_subdir="validation",
                            test_subdir="test")

    # Split the data
    logger.info(f"Starting data splitting from {splitter.source_dir}...")
    splitter.split_data()

    # Verify the splits
    logger.info("Verifying data splits...")
    if splitter.verify_splits():
        logger.info("Data splits verified successfully")
    else:
        logger.error("Data split verification failed")
        return

    # Print statistics
    logger.info("Data split statistics:")
    stats = splitter.get_split_statistics()
    for split_name, files in stats.items():
        print(f"\n{split_name.upper()} SET:")
        for file_name, file_stats in files.items():
            print(f"{file_name}:")
            print(f"  Rows: {file_stats['rows']}")
            print(f"  Period: {file_stats['start_date']} to {file_stats['end_date']}")

if __name__ == "__main__":
    main()