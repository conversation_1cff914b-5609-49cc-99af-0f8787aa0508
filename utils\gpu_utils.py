"""
GPU Utilities Module

This module provides utilities for checking GPU availability, CUDA configuration,
and running tests to verify GPU functionality for PyTorch.
"""

import logging
from typing import Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

def check_cuda_pytorch() -> Dict[str, Any]:
    """
    Check CUDA availability and GPU information for PyTorch.

    Returns:
        Dictionary with PyTorch CUDA information
    """
    try:
        import torch

        info = {
            'pytorch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'cuda_version': None,
            'device_count': 0,
            'device_name': None,
            'total_memory_gb': 0,
            'allocated_memory_gb': 0,
            'cached_memory_gb': 0
        }

        if info['cuda_available']:
            info['cuda_version'] = torch.version.cuda
            info['device_count'] = torch.cuda.device_count()
            info['device_name'] = torch.cuda.get_device_name(0)

            # Get GPU memory information
            gpu_props = torch.cuda.get_device_properties(0)
            info['total_memory_gb'] = gpu_props.total_memory / 1e9  # Convert to GB
            info['allocated_memory_gb'] = torch.cuda.memory_allocated(0) / 1e9
            info['cached_memory_gb'] = torch.cuda.memory_reserved(0) / 1e9

        return info

    except ImportError:
        logger.warning("PyTorch not installed. Cannot check CUDA for PyTorch.")
        return {
            'pytorch_version': None,
            'cuda_available': False,
            'error': 'PyTorch not installed'
        }
    except Exception as e:
        logger.error(f"Error checking PyTorch CUDA: {str(e)}")
        return {
            'pytorch_version': None,
            'cuda_available': False,
            'error': str(e)
        }



def test_gpu_pytorch() -> Dict[str, Any]:
    """
    Test GPU functionality for PyTorch by running a simple computation.

    Returns:
        Dictionary with test results
    """
    try:
        import torch

        result = {
            'success': False,
            'device': 'cpu',
            'computation_time_ms': 0,
            'error': None
        }

        if not torch.cuda.is_available():
            result['error'] = 'CUDA not available'
            return result

        # Create tensors on GPU
        device = torch.device('cuda')
        result['device'] = str(device)

        # Create large matrices to test computation
        size = 2000
        a = torch.randn(size, size, device=device)
        b = torch.randn(size, size, device=device)

        # Time the computation
        start = torch.cuda.Event(enable_timing=True)
        end = torch.cuda.Event(enable_timing=True)

        start.record()
        _ = torch.matmul(a, b)  # Perform computation but don't need the result
        end.record()

        # Wait for computation to finish
        torch.cuda.synchronize()

        # Get computation time
        result['computation_time_ms'] = start.elapsed_time(end)
        result['success'] = True

        return result

    except ImportError:
        logger.warning("PyTorch not installed. Cannot test GPU for PyTorch.")
        return {
            'success': False,
            'error': 'PyTorch not installed'
        }
    except Exception as e:
        logger.error(f"Error testing PyTorch GPU: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }



def print_gpu_info(pytorch_info: Dict[str, Any] = None):
    """
    Print GPU information in a formatted way.

    Args:
        pytorch_info: PyTorch GPU information
    """
    if pytorch_info is None:
        pytorch_info = check_cuda_pytorch()

    print("\n" + "="*50)
    print("GPU INFORMATION SUMMARY")
    print("="*50)

    # Print PyTorch information
    print("\nPyTorch GPU Information:")
    print(f"PyTorch Version: {pytorch_info.get('pytorch_version')}")
    print(f"CUDA Available: {pytorch_info.get('cuda_available')}")

    if pytorch_info.get('cuda_available'):
        print(f"CUDA Version: {pytorch_info.get('cuda_version')}")
        print(f"GPU Device Count: {pytorch_info.get('device_count')}")
        print(f"GPU Device Name: {pytorch_info.get('device_name')}")
        print(f"Total GPU Memory: {pytorch_info.get('total_memory_gb', 0):.2f} GB")
        print(f"Allocated GPU Memory: {pytorch_info.get('allocated_memory_gb', 0):.2f} GB")
        print(f"Cached GPU Memory: {pytorch_info.get('cached_memory_gb', 0):.2f} GB")
    elif 'error' in pytorch_info:
        print(f"Error: {pytorch_info.get('error')}")

    print("\n" + "="*50)
