"""
File protection utility to prevent accidental modifications to critical files.
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Dict

class FileProtector:
    def __init__(self):
        self.protected_files = {
            'test_mt5_connections.py': 'test_mt5_connections.py.bak',
            'config/credentials.py': 'config/credentials.py.bak',
            'config/.encryption_key': 'config/.encryption_key.bak'
        }
        self.file_hashes = {}
    
    def _calculate_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of a file."""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            print(f"Error calculating hash for {file_path}: {str(e)}")
            return ""
    
    def _backup_file(self, file_path: str, backup_path: str) -> bool:
        """Create a backup of a file."""
        try:
            if os.path.exists(file_path):
                shutil.copy2(file_path, backup_path)
                return True
            return False
        except Exception as e:
            print(f"Error backing up {file_path}: {str(e)}")
            return False
    
    def _restore_file(self, file_path: str, backup_path: str) -> bool:
        """Restore a file from backup."""
        try:
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, file_path)
                return True
            return False
        except Exception as e:
            print(f"Error restoring {file_path}: {str(e)}")
            return False
    
    def initialize_protection(self) -> None:
        """Initialize protection for all files."""
        for file_path, backup_path in self.protected_files.items():
            if os.path.exists(file_path):
                # Create backup if it doesn't exist
                if not os.path.exists(backup_path):
                    self._backup_file(file_path, backup_path)
                
                # Store initial hash
                self.file_hashes[file_path] = self._calculate_hash(file_path)
    
    def check_files(self) -> Dict[str, bool]:
        """Check if protected files have been modified."""
        results = {}
        for file_path, backup_path in self.protected_files.items():
            if os.path.exists(file_path):
                current_hash = self._calculate_hash(file_path)
                if file_path in self.file_hashes:
                    results[file_path] = current_hash == self.file_hashes[file_path]
                else:
                    results[file_path] = True
            else:
                results[file_path] = False
        return results
    
    def restore_if_modified(self) -> None:
        """Restore files if they have been modified."""
        modifications = self.check_files()
        for file_path, is_unchanged in modifications.items():
            if not is_unchanged:
                backup_path = self.protected_files[file_path]
                print(f"File {file_path} has been modified. Restoring from backup...")
                if self._restore_file(file_path, backup_path):
                    print(f"Successfully restored {file_path}")
                else:
                    print(f"Failed to restore {file_path}")

def main():
    """Run file protection checks."""
    protector = FileProtector()
    protector.initialize_protection()
    
    # Check for modifications
    modifications = protector.check_files()
    for file_path, is_unchanged in modifications.items():
        if not is_unchanged:
            print(f"Warning: {file_path} has been modified!")
    
    # Restore modified files
    protector.restore_if_modified()

if __name__ == "__main__":
    main() 