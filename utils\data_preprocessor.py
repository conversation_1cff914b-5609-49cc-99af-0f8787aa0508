import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from dataclasses import dataclass
import gc
import psutil
import time
import os

logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """Statistics about the data preprocessing operations"""
    input_rows: int
    output_rows: int
    input_columns: int
    output_columns: int
    missing_value_count: int
    outliers_removed: int
    memory_usage_mb: float
    processing_time_ms: float

class DataPreprocessor:
    """
    Handles data preprocessing for the trading bot including:

    1. Missing value handling
    2. Outlier detection and removal
    3. Feature engineering with technical indicators
    4. Normalization and scaling
    5. Sequence preparation for ML models
    6. Memory optimization
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the data preprocessor

        Args:
            config: Configuration dictionary containing necessary parameters.
                    Expected keys include strategy settings (e.g., sequence_length,
                    batch_size, feature usage flags, outlier_std_threshold) and
                    preprocessor settings (e.g., preprocessor_max_batch_size).
        """
        self.config = config # Store the passed config dictionary
        self.logger = logging.getLogger(__name__)

        # Get parameters from the config dictionary with defaults
        # Nested dictionaries (like strategy) are accessed carefully
        strategy_config = self.config.get('strategy', {})
        self.sequence_length = strategy_config.get('sequence_length', 60) # Default 60
        self.batch_size = strategy_config.get('batch_size', 32) # Default 32
        self.outlier_std_threshold = strategy_config.get('outlier_std_threshold', 3.0) # Default 3.0

        # Initialize scalers
        self.price_scaler = MinMaxScaler()
        self.feature_scaler = RobustScaler() # Using RobustScaler for features sensitive to outliers

        # Track processing statistics
        self.stats = ProcessingStats(
            input_rows=0,
            output_rows=0,
            input_columns=0,
            output_columns=0,
            missing_value_count=0,
            outliers_removed=0,
            memory_usage_mb=0,
            processing_time_ms=0
        )

        # Define indicator groups (these could potentially also come from config)
        self.trend_indicators = ['SMA', 'EMA', 'TEMA', 'MACD', 'ADX']
        self.momentum_indicators = ['RSI', 'CCI', 'STOCH', 'MOM', 'ROC', 'WILLR']
        self.volatility_indicators = ['ATR', 'BBANDS', 'NATR']
        self.volume_indicators = ['OBV', 'AD']

        # Features to include based on config
        self.enabled_features = {
            'trend': strategy_config.get('use_trend_indicators', True),
            'momentum': strategy_config.get('use_momentum_indicators', True),
            'volatility': strategy_config.get('use_volatility_indicators', True),
            'volume': strategy_config.get('use_volume_indicators', True),
            'time': strategy_config.get('use_time_features', True)
        }

        # Memory management parameters (from top-level config)
        self.max_batch_size = self.config.get('preprocessor_max_batch_size', 10000) # Default 10000

        logger.info("DataPreprocessor initialized with provided config.")

    def _calculate_sma(self, series: pd.Series, window: int) -> pd.Series:
        """Calculate Simple Moving Average"""
        return series.rolling(window=window).mean()

    def _calculate_ema(self, series: pd.Series, window: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return series.ewm(span=window, adjust=False).mean()

    def _calculate_rsi(self, series: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _calculate_macd(self, series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        exp1 = series.ewm(span=fast, adjust=False).mean()
        exp2 = series.ewm(span=slow, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal, adjust=False).mean()
        return pd.DataFrame({
            'MACD': macd,
            'Signal': signal_line,
            'Histogram': macd - signal_line
        })

    def _calculate_bollinger_bands(self, series: pd.Series, window: int = 20, num_std: float = 2.0) -> pd.DataFrame:
        """Calculate Bollinger Bands"""
        sma = self._calculate_sma(series, window)
        std = series.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return pd.DataFrame({
            'Upper': upper_band,
            'Middle': sma,
            'Lower': lower_band
        })

    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=window).mean()

    def _calculate_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                            k_window: int = 14, d_window: int = 3) -> pd.DataFrame:
        """Calculate Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        k = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d = k.rolling(window=d_window).mean()
        return pd.DataFrame({
            'K': k,
            'D': d
        })

    def _add_technical_indicators(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Add technical indicators to the dataframe

        Args:
            df: Input dataframe with OHLCV data

        Returns:
            DataFrame with added technical indicators or None if error
        """
        try:
            # Create a copy to avoid modifying the original
            result = df.copy()

            # Calculate indicators
            result['SMA_20'] = self._calculate_sma(result['close'], 20)
            result['SMA_50'] = self._calculate_sma(result['close'], 50)
            result['SMA_200'] = self._calculate_sma(result['close'], 200)

            result['EMA_12'] = self._calculate_ema(result['close'], 12)
            result['EMA_26'] = self._calculate_ema(result['close'], 26)

            result['RSI'] = self._calculate_rsi(result['close'])

            macd = self._calculate_macd(result['close'])
            result['MACD'] = macd['MACD']
            result['MACD_Signal'] = macd['Signal']
            result['MACD_Hist'] = macd['Histogram']

            bollinger = self._calculate_bollinger_bands(result['close'])
            result['BB_Upper'] = bollinger['Upper']
            result['BB_Middle'] = bollinger['Middle']
            result['BB_Lower'] = bollinger['Lower']

            result['ATR'] = self._calculate_atr(result['high'], result['low'], result['close'])

            stoch = self._calculate_stochastic(result['high'], result['low'], result['close'])
            result['Stoch_K'] = stoch['K']
            result['Stoch_D'] = stoch['D']

            # Add momentum indicators
            result['Momentum'] = result['close'].pct_change(periods=10)
            result['ROC'] = ((result['close'] - result['close'].shift(10)) / result['close'].shift(10)) * 100

            # Add volume indicators
            result['Volume_SMA'] = self._calculate_sma(result['volume'], 20)
            result['Volume_EMA'] = self._calculate_ema(result['volume'], 20)

            # Add volatility indicators
            result['Volatility'] = result['close'].pct_change().rolling(window=20).std() * np.sqrt(252)

            # Drop rows with NaN values (usually at the beginning due to indicator lookback)
            orig_len = len(result)
            result = result.dropna()
            dropped = orig_len - len(result)
            if dropped > 0:
                logger.info(f"Dropped {dropped} rows with NaN values after adding indicators")

            return result

        except Exception as e:
            logger.error(f"Error adding technical indicators: {str(e)}")
            return None

    def preprocess_data(self, df: pd.DataFrame, feature_cols: List[str]) -> Tuple[Optional[pd.DataFrame], Optional[np.ndarray]]:
        """Main method to preprocess raw data for prediction."""
        start_time = time.time()
        initial_memory = psutil.Process(os.getpid()).memory_info().rss
        self.stats.input_rows = len(df)
        self.stats.input_columns = len(df.columns)

        try:
            # 1. Handle Missing Values
            df, missing_count = self._handle_missing_values(df)
            self.stats.missing_value_count = missing_count
            if df is None or df.empty:
                logger.warning("Dataframe empty after handling missing values.")
                return None, None

            # 2. Add Technical Indicators (Optional, based on config)
            # This might depend on whether feature_cols includes them already
            # df = self._add_technical_indicators(df)
            # if df is None or df.empty: return None

            # 3. Add Derived Features (like time features, etc.)
            df = self._add_derived_features(df)
            if df is None or df.empty:
                logger.warning("Dataframe empty after adding derived features.")
                return None, None

            # 4. Handle Outliers
            df, outliers_removed = self._handle_outliers(df, feature_cols)
            self.stats.outliers_removed = outliers_removed
            if df is None or df.empty:
                logger.warning("Dataframe empty after handling outliers.")
                return None, None

            # 5. Select Final Features needed by the model
            if not all(col in df.columns for col in feature_cols):
                missing_req_cols = [col for col in feature_cols if col not in df.columns]
                logger.error(f"Required feature columns missing after processing: {missing_req_cols}")
                return None, None
            df_features = df[feature_cols]

            # 6. Scale Features
            df_scaled = self._scale_features(df_features)
            if df_scaled is None or df_scaled.empty:
                logger.error("Feature scaling failed.")
                return None, None

            # 7. Prepare Sequences
            sequences = self._prepare_sequences(df_scaled)
            if sequences is None:
                 logger.error("Sequence preparation failed.")
                 return None, None

            # Update stats
            final_memory = psutil.Process(os.getpid()).memory_info().rss
            self.stats.output_rows = len(sequences) if sequences is not None else 0
            self.stats.output_columns = sequences.shape[2] if sequences is not None and sequences.ndim == 3 else 0
            self.stats.memory_usage_mb = (final_memory - initial_memory) / (1024 * 1024)
            self.stats.processing_time_ms = (time.time() - start_time) * 1000
            logger.info(f"Preprocessing complete. Time: {self.stats.processing_time_ms:.2f}ms, Output Shape: {sequences.shape if sequences is not None else 'None'}")

            return df, sequences # Return both the processed DataFrame and the sequences (X)

        except Exception as e:
            logger.error(f"Error during data preprocessing: {e}", exc_info=True)
            # Handle error (e.g., using self.error_handler if passed in)
            return None, None

    def get_stats(self) -> ProcessingStats:
        """Return the latest processing statistics."""
        return self.stats

    def _handle_missing_values(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """
        Handle missing values in the data

        Args:
            df: DataFrame with possibly missing values

        Returns:
            Tuple of (cleaned DataFrame, count of missing values)
        """
        # Make a copy to avoid modifying the original
        df = df.copy()

        # Get missing value count before handling
        missing_count = df.isna().sum().sum()

        if missing_count > 0:
            logger.warning(f"Found {missing_count} missing values in data")

            # First, check for columns with excessive missing values (>50%)
            missing_pct = df.isna().mean() * 100
            excessive_missing = missing_pct[missing_pct > 50].index.tolist()

            if excessive_missing:
                logger.warning(f"Columns with >50% missing values: {excessive_missing}")
                # Drop columns with excessive missing values
                df = df.drop(columns=excessive_missing)
                logger.info(f"Dropped {len(excessive_missing)} columns with excessive missing values")

            # For price data, use forward fill then backward fill
            # This maintains the last known price until new data is available
            for col in ['open', 'high', 'low', 'close']:
                if col in df.columns:
                    df[col] = df[col].ffill()  # Forward fill
                    df[col] = df[col].bfill()  # Backward fill

            # For volume, replace with median or 0
            if 'volume' in df.columns:
                df['volume'] = df['volume'].fillna(df['volume'].median())
                df['volume'] = df['volume'].fillna(0)

            # For all other columns, use more aggressive filling
            for col in df.columns:
                if col not in ['open', 'high', 'low', 'close', 'volume'] and df[col].isna().any():
                    # For indicators, use forward fill, backward fill, then median
                    df[col] = df[col].ffill()  # Forward fill
                    df[col] = df[col].bfill()  # Backward fill
                    if df[col].isna().any():
                        df[col] = df[col].fillna(df[col].median())
                        # If still have NaN (e.g., all NaN column), fill with 0
                        if df[col].isna().any():
                            df[col] = df[col].fillna(0)

            # Check if we still have missing values
            remaining_missing = df.isna().sum().sum()
            if remaining_missing > 0:
                # Count rows before dropping
                rows_before = len(df)

                # If we still have missing values, drop those rows
                df = df.dropna()

                # Count rows after dropping
                rows_after = len(df)
                rows_dropped = rows_before - rows_after

                logger.warning(f"Dropped {rows_dropped} rows with missing values ({rows_dropped/rows_before:.2%} of data)")

        return df, missing_count

    def _add_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add derived features and time-based features

        Args:
            df: DataFrame with price and indicator data

        Returns:
            DataFrame with additional derived features
        """
        # Make a copy to avoid modifying the original
        result = df.copy()

        try:
            # Add price-based features
            result['returns'] = result['close'].pct_change()
            result['log_returns'] = np.log(result['close'] / result['close'].shift(1))
            result['range'] = result['high'] - result['low']
            result['range_pct'] = result['range'] / result['close']

            # High-Low ratio
            result['high_low_ratio'] = result['high'] / result['low']

            # Distance from price to moving averages
            if 'SMA_20' in result.columns:
                result['price_to_sma20'] = result['close'] / result['SMA_20'] - 1

            if 'EMA_20' in result.columns:
                result['price_to_ema20'] = result['close'] / result['EMA_20'] - 1

            # Add time-based features if enabled
            if self.enabled_features.get('time', False) and 'time' in result.columns: # Use .get() for safety
                # Convert time to datetime if it's not already
                if not pd.api.types.is_datetime64_any_dtype(result['time']):
                    try: # Add try-except for robust datetime conversion
                        result['time'] = pd.to_datetime(result['time'])
                    except Exception as dt_err:
                        logger.warning(f"Could not convert 'time' column to datetime: {dt_err}. Skipping time features.")
                        # Optionally return result here if time features are critical
                        # return result # Or handle differently
                # Check again if conversion was successful before proceeding
                if pd.api.types.is_datetime64_any_dtype(result['time']):
                    # Extract time components
                    result['hour'] = result['time'].dt.hour
                    result['day_of_week'] = result['time'].dt.dayofweek
                    result['day_of_month'] = result['time'].dt.day
                    result['month'] = result['time'].dt.month

                    # Create cyclical time features
                    result['hour_sin'] = np.sin(2 * np.pi * result['hour'] / 24)
                    result['hour_cos'] = np.cos(2 * np.pi * result['hour'] / 24)
                    result['day_of_week_sin'] = np.sin(2 * np.pi * result['day_of_week'] / 7)
                    result['day_of_week_cos'] = np.cos(2 * np.pi * result['day_of_week'] / 7)

                    # Drop the original time column as it's not numeric
                    result = result.drop('time', axis=1, errors='ignore') # Add errors='ignore'

            # Drop rows with NaN values introduced by pct_change, etc.
            orig_len_derived = len(result)
            result = result.dropna()
            dropped_derived = orig_len_derived - len(result)
            if dropped_derived > 0:
                logger.info(f"Dropped {dropped_derived} rows with NaN values after adding derived features.")

            return result

        except Exception as e:
            logger.warning(f"Error adding derived features: {str(e)}")
            # Return original DataFrame if we encounter an error
            return df

    def _handle_outliers(self, df: pd.DataFrame, feature_cols: List[str]) -> Tuple[pd.DataFrame, int]:
        """
        Detect and handle outliers in the data

        Args:
            df: DataFrame with features

        Returns:
            Tuple of (cleaned DataFrame, count of outliers removed)
        """
        # Make a copy to avoid modifying the original
        result = df.copy()
        outliers_removed = 0

        try:
            # Only detect outliers in returns and some indicators
            outlier_columns = ['returns', 'log_returns', 'range_pct']

            for indicator in ['RSI', 'CCI', 'MOM', 'ROC', 'ATR']:
                if indicator in result.columns:
                    outlier_columns.append(indicator)

            # For each column, calculate z-score and identify outliers
            for col in outlier_columns:
                if col in result.columns:
                    # Calculate z-score
                    mean = result[col].mean()
                    std = result[col].std()
                    if std == 0:  # Skip if standard deviation is 0 (constant values)
                        continue

                    z_scores = np.abs((result[col] - mean) / std)

                    # Identify outliers
                    outliers = z_scores > self.outlier_std_threshold
                    current_outliers = outliers.sum()

                    if current_outliers > 0:
                        # Handle outliers - replace with threshold values (capping/flooring)
                        # median = result[col].median() # Median replacement is another option

                        upper_threshold = mean + self.outlier_std_threshold * std
                        lower_threshold = mean - self.outlier_std_threshold * std

                        # Apply capping/flooring vectorized
                        result.loc[outliers & (result[col] > upper_threshold), col] = upper_threshold
                        result.loc[outliers & (result[col] < lower_threshold), col] = lower_threshold

                        # Count outliers
                        outliers_removed += current_outliers

                        logger.debug(f"Handled {current_outliers} outliers in {col} using std threshold {self.outlier_std_threshold}")

            return result, outliers_removed

        except Exception as e:
            logger.warning(f"Error handling outliers: {str(e)}")
            # Return original DataFrame if we encounter an error
            return df, 0

    def _scale_features(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Scale features to be in similar ranges

        Args:
            df: DataFrame with features

        Returns:
            DataFrame with scaled features
        """
        try:
            # Make a copy for scaling
            df_scaled = df.copy()

            # Scale price columns
            # Use default empty list if 'price_columns' not in config or None
            price_columns_config = self.config.get('scaling', {}).get('price_columns', ['open', 'high', 'low', 'close'])
            price_columns = [col for col in price_columns_config if col in df.columns]

            if price_columns:
                price_data = df_scaled[price_columns].values
                # Check if scaler has been fitted
                if hasattr(self.price_scaler, 'n_samples_seen_') and self.price_scaler.n_samples_seen_ > 0:
                    price_data_scaled = self.price_scaler.transform(price_data) # Use transform if already fitted
                else:
                    price_data_scaled = self.price_scaler.fit_transform(price_data) # Use fit_transform first time
                df_scaled[price_columns] = price_data_scaled
            else:
                 logger.warning("No price columns found or specified for scaling.")

            # Scale volume columns if not empty
            # Use default empty list if 'volume_columns' not in config or None
            volume_columns_config = self.config.get('scaling', {}).get('volume_columns', ['volume'])
            volume_columns = [col for col in volume_columns_config if col in df.columns]

            if volume_columns:
                volume_data = df_scaled[volume_columns].values.reshape(-1, len(volume_columns))
                # Check if scaler has been fitted
                if hasattr(self.feature_scaler, 'n_samples_seen_') and self.feature_scaler.n_samples_seen_ > 0:
                     # Note: Using a single feature scaler for volume and indicators might not be ideal
                     # Consider separate scalers if distributions differ significantly.
                     # For now, assume feature_scaler is fit on the fly or needs refitting.
                     # A robust approach might involve fitting scalers on training data only.
                     # Let's reset and fit here for simplicity in this context, but flag for review.
                     self.feature_scaler = RobustScaler() # Re-initialize or use a dedicated volume scaler
                     volume_data_scaled = self.feature_scaler.fit_transform(volume_data)
                else:
                     volume_data_scaled = self.feature_scaler.fit_transform(volume_data)
                df_scaled[volume_columns] = volume_data_scaled
            else:
                 logger.warning("No volume columns found or specified for scaling.")

            # Scale indicator columns
            # Infer indicator columns as those not in price or volume lists
            indicator_columns = [col for col in df.columns
                              if col not in price_columns and col not in volume_columns]

            if indicator_columns:
                indicator_data = df_scaled[indicator_columns].values
                # Assume feature_scaler needs fitting/refitting for indicators too
                # Re-initializing ensures it fits to the current indicator data
                self.feature_scaler = RobustScaler() # Re-initialize or use dedicated indicator scaler
                indicator_data_scaled = self.feature_scaler.fit_transform(indicator_data)
                df_scaled[indicator_columns] = indicator_data_scaled
            else:
                logger.info("No remaining indicator columns to scale.")

            return df_scaled

        except Exception as e:
            logger.warning(f"Error scaling features: {str(e)}")
            # Return original DataFrame if we encounter an error
            return None

    def _prepare_sequences(self, df_scaled: pd.DataFrame) -> Optional[np.ndarray]:
        """Prepare sequences from the scaled feature dataframe."""
        try:
            data = df_scaled.values
            if len(data) < self.sequence_length:
                logger.warning(f"Insufficient data length ({len(data)}) to create sequence of length {self.sequence_length}.")
                return None

            sequences = []
            # Prepare sequences ending at each step for prediction
            for i in range(len(data) - self.sequence_length + 1):
                sequence = data[i : i + self.sequence_length]
                sequences.append(sequence)

            if not sequences:
                return None

            return np.array(sequences)

        except Exception as e:
            logger.error(f"Error preparing sequences: {e}", exc_info=True)
            return None