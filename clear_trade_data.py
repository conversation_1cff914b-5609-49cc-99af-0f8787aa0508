"""
Clear Trade Data

This script clears any existing trade data files that might be causing issues with the trading bot.
"""

import os
import glob
import json
import pickle
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_trade_files():
    """Find all files that might contain trade data."""
    trade_files = []
    
    # Look for JSON files
    json_files = glob.glob("**/*trade*.json", recursive=True)
    trade_files.extend(json_files)
    
    # Look for pickle files
    pkl_files = glob.glob("**/*trade*.pkl", recursive=True)
    trade_files.extend(pkl_files)
    
    # Look for data files
    dat_files = glob.glob("**/*trade*.dat", recursive=True)
    trade_files.extend(dat_files)
    
    return trade_files

def clear_trade_files(files):
    """Clear or delete trade files."""
    for file_path in files:
        try:
            # For JSON files, replace with empty array
            if file_path.endswith('.json'):
                with open(file_path, 'w') as f:
                    json.dump([], f)
                logger.info(f"Cleared JSON file: {file_path}")
            # For pickle files, replace with empty list
            elif file_path.endswith('.pkl'):
                with open(file_path, 'wb') as f:
                    pickle.dump([], f)
                logger.info(f"Cleared pickle file: {file_path}")
            # For other files, just delete them
            else:
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
        except Exception as e:
            logger.error(f"Error clearing file {file_path}: {str(e)}")

def main():
    """Main function."""
    logger.info("Searching for trade data files...")
    trade_files = find_trade_files()
    
    if not trade_files:
        logger.info("No trade data files found.")
        return
    
    logger.info(f"Found {len(trade_files)} trade data files:")
    for file in trade_files:
        logger.info(f"  - {file}")
    
    logger.info("Clearing trade data files...")
    clear_trade_files(trade_files)
    
    logger.info("Done.")

if __name__ == "__main__":
    main()
